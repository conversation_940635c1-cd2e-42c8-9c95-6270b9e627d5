# ================================
# FlexProxy 优化配置文件
# ================================
# 经过五阶段配置优化的完整配置文件
# 优化内容：DNS统一、超时统一、端口统一、缓存层次化、模块统一管理
# 版本：v2.0 (优化版)
# 创建时间：2025-01-11

# ================================
# 全局配置
# ================================
global:
  enable: true
  proxy_file: "./proxies.txt"
  
  # 代理策略配置
  ip_rotation_mode: "smart"
  default_process_stage: "pre"
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 60
  retry_proxy_global_tracking: true
  
  # 代理池配置
  min_proxy_pool_size: 10
  max_proxy_fetch_attempts: 3
  
  # 规则配置
  rule_priority: 50
  excluded_scope: "all"
  excluded_patterns:
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.internal.company.com"
  
  # 安全配置
  trusted_ips:
    - "127.0.0.1"
    - "::1"
    - "***********"
    - "********"
  
  blocked_ips:
    - "***********"
    - "************"
    - "*********"
  
  # 全局封禁配置
  global_banned_ips:
    - ip: "***********00"
      duration: 3600
    - ip: "*********"
      duration: "1h30m"
    - ip: "***********"
      duration: "reboot"
  
  banned_domains:
    - domain: "malicious-site.com"
      duration: 86400
    - domain: "spam-domain.net"
      duration: "24h"
    - domain: "blocked-forever.com"
      duration: "reboot"

# ================================
# 统一模块管理配置
# ================================
modules:
  # 启用的模块
  enabled:
    - "server"
    - "proxy"
    - "cache"
    - "logging"
    - "monitoring"
    - "security"
    - "dns_service"
  
  # 禁用的模块
  disabled:
    - "rate_limiting"
    - "plugins"
  
  # 模块依赖关系
  dependencies:
    # 核心依赖模块
    core:
      - "server"
      - "logging"
    
    # 模块依赖映射
    dependencies:
      proxy:
        - "server"
        - "logging"
      cache:
        - "logging"
      monitoring:
        - "server"
        - "logging"
      security:
        - "server"
        - "logging"
      rate_limiting:
        - "server"
        - "logging"
        - "cache"
      dns_service:
        - "logging"
        - "cache"
      plugins:
        - "server"
        - "logging"
    
    # 可选依赖
    optional:
      proxy:
        - "cache"
        - "monitoring"
      monitoring:
        - "cache"
      security:
        - "cache"
    
    # 冲突模块（当前无冲突）
    conflicts: {}
  
  # 模块启动顺序
  startup_order:
    phases:
      - name: "core"
        modules: ["logging"]
        timeout: "30s"
        required: true
      
      - name: "infrastructure"
        modules: ["server", "cache", "dns_service"]
        timeout: "60s"
        required: true
      
      - name: "services"
        modules: ["proxy", "security", "rate_limiting"]
        timeout: "60s"
        required: false
      
      - name: "monitoring"
        modules: ["monitoring"]
        timeout: "30s"
        required: false
      
      - name: "extensions"
        modules: ["plugins"]
        timeout: "30s"
        required: false
    
    phase_delay: "5s"
    parallel:
      enabled: true
      max_workers: 3
      timeout: "120s"
  
  # 模块生命周期配置
  lifecycle:
    startup_timeout: "300s"
    shutdown_timeout: "60s"
    restart_policy:
      enabled: true
      max_retries: 3
      retry_delay: "10s"
      backoff_multiplier: 2.0
      max_delay: "300s"
    graceful_shutdown:
      enabled: true
      timeout: "30s"
      force_after: "60s"
      wait_for_connections: true
  
  # 模块健康检查配置
  health_check:
    enabled: true
    interval: "30s"
    timeout: "10s"
    endpoints:
      server: "/health"
      monitoring: "/metrics"
      proxy: "/proxy/health"
      cache: "/cache/health"
      dns_service: "/dns/health"
    failure_threshold: 3
    success_threshold: 2
    auto_restart: false

# ================================
# 统一超时配置
# ================================
timeouts:
  # 网络超时
  network:
    connect: "10s"
    read: "30s"
    write: "30s"
    idle: "120s"
  
  # DNS超时
  dns:
    query: "5s"
    resolve: "5s"
  
  # 代理超时
  proxy:
    retry: "1s"
    max_retry: "30s"
    health_check: "10s"
    cooldown: "60s"
  
  # 监控超时
  monitoring:
    interval: "5s"
    collection: "3s"
  
  # 缓存超时
  cache:
    cleanup: "300s"
  
  # 动作超时
  action:
    default: "10s"
    request: "30s"
    bypass: "30s"

# ================================
# 统一端口配置
# ================================
ports:
  # 主要服务端口
  http: 8080
  https: 8443
  socks: 1080
  
  # 管理和监控端口
  monitoring: 9090
  debug: 6060
  admin: 8081
  
  # 协议特定端口
  dns: 53
  dhcp: 67
  
  # 端口范围配置
  ranges:
    dynamic_start: 49152
    dynamic_end: 65535
    reserved_start: 1
    reserved_end: 1023
    user_start: 1024
    user_end: 49151
  
  # 端口冲突检测
  conflict_detection:
    enabled: true
    check_local: true
    auto_resolve: false
    exclude_ports: []
    fallback_ports: [8082, 8083, 8084, 8085]

# ================================
# 统一DNS服务配置
# ================================
dns_service:
  # 查询配置
  lookup_mode: "custom"
  timeout: "5s"
  retries: 3
  
  # 缓存配置
  cache:
    enabled: true
    ttl: "300s"
    cleanup_interval: "600s"
    max_size: 1000
  
  # 服务器配置
  servers:
    primary:
      - server: "1.1.1.1:53"
        protocol: "udp"
        timeout: 5000
        priority: 1
        tags: ["cloudflare", "primary"]
      
      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 2
        tags: ["google", "backup"]
    
    secondary:
      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 1
        tags: ["google", "secondary"]
      
      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 2
        tags: ["cloudflare", "secondary"]
    
    fallback: "system"
  
  # IP版本优先级
  ip_version_priority: "ipv4"
  
  # 反向DNS查询配置
  reverse_lookup:
    enabled: true
    mode: "dns"
    source: ""

# ================================
# 层次化缓存配置
# ================================
cache:
  # 全局缓存配置
  global:
    enabled: true
    default_ttl: "3600s"
    default_size: 1000
    cleanup_interval: "300s"
    max_memory_usage: "512MB"
    eviction_policy: "lru"

  # 专用模块缓存配置
  modules:
    # DNS缓存
    dns:
      enabled: true
      ttl: "300s"
      size: 500
      cleanup_interval: "600s"
      eviction_policy: "lru"
      compression: false

    # 代理缓存
    proxy:
      enabled: true
      ttl: "1800s"
      size: 200
      cleanup_interval: "900s"
      eviction_policy: "lru"
      compression: false

    # 会话缓存
    session:
      enabled: true
      ttl: "7200s"
      size: 100
      cleanup_interval: "3600s"
      eviction_policy: "lru"
      compression: false

    # 正则表达式缓存
    regex:
      enabled: true
      ttl: "86400s"
      size: 50
      cleanup_interval: "7200s"
      eviction_policy: "lfu"
      compression: false

    # 认证缓存
    auth:
      enabled: true
      ttl: "3600s"
      size: 100
      cleanup_interval: "1800s"
      eviction_policy: "lru"
      compression: false

    # 限流缓存
    rate:
      enabled: true
      ttl: "300s"
      size: 1000
      cleanup_interval: "600s"
      eviction_policy: "fifo"
      compression: false

  # 缓存键前缀配置
  key_prefixes:
    proxy_list: "proxy:list"
    proxy_status: "proxy:status:"
    user_session: "user:session:"
    rate_limit: "rate:limit:"
    dns_cache: "dns:cache:"
    regex_cache: "regex:cache:"
    auth_cache: "auth:cache:"

  # 缓存策略配置
  policies:
    # 缓存预热策略
    warmup:
      enabled: false
      on_startup: false
      sources: []
      batch_size: 100

    # 缓存失效策略
    invalidation:
      strategy: "ttl"
      events: []
      patterns: []

    # 缓存同步策略
    sync:
      enabled: false
      mode: "master"
      interval: "60s"

    # 缓存压缩策略
    compression:
      enabled: false
      algorithm: "gzip"
      level: 6
      min_size: 1024

  # 存储配置
  storage:
    type: "memory"

    # 内存存储配置
    memory:
      max_size: 10000
      max_memory: "256MB"
      eviction_policy: "lru"
      shards: 16

    # Redis存储配置
    redis:
      address: "localhost:6379"
      password: ""
      database: 0
      pool_size: 10
      max_retries: 3
      dial_timeout: "5s"

    # 文件存储配置
    file:
      directory: "./cache"
      max_file_size: "100MB"
      max_files: 1000
      compression: true

# ================================
# 服务器配置
# ================================
server:
  host: "0.0.0.0"

  # 连接配置
  max_idle_conns: 100
  max_idle_conns_per_host: 10
  max_conns_per_host: 50
  buffer_size: 4096
  max_header_bytes: 1048576
  debounce_delay: "100ms"

  # 压缩配置
  compression:
    enabled: true
    level: 6
    min_size: 1024
    types: ["text/html", "text/css", "application/javascript", "application/json"]

  # HTTP/2配置
  http2:
    enabled: true
    max_concurrent_streams: 100
    max_frame_size: 16384
    max_header_list_size: 8192

  # 性能分析配置
  profiling:
    enabled: false
    cpu_profile: "./cpu.prof"
    memory_profile: "./mem.prof"
    block_profile: "./block.prof"
    mutex_profile: "./mutex.prof"

# ================================
# 代理配置
# ================================
proxy:
  strategy: "random"
  load_balancer: "round_robin"
  max_retries: 3
  pool_size: 50
  rotation_interval: 300

  # 健康检查配置
  health_check:
    path: "/health"
    max_consecutive_failures: 3
    max_consecutive_successes: 2

  # 质量评分配置
  quality_score:
    enabled: true
    response_time_weight: 0.4
    success_rate_weight: 0.4
    availability_weight: 0.2
    min_requests: 10

# ================================
# 日志配置
# ================================
logging:
  level: "info"
  format: "json"
  file: "./logs/flexproxy.log"
  max_size: 100
  max_age: 30
  max_backups: 10
  time_format: "2006-01-02T15:04:05.000Z07:00"

# ================================
# 监控配置
# ================================
monitoring:
  path: "/metrics"
  metrics:
    requests_total: "counter"
    request_duration: "histogram"
    active_connections: "gauge"
    proxy_status: "gauge"
  labels:
    service: "flexproxy"
    version: "2.0"
    environment: "production"

# ================================
# 安全配置
# ================================
security:
  # 认证配置
  auth:
    type: "basic"
    username: "admin"
    password: "secure_password_here"
    realm: "FlexProxy Admin"

  # TLS配置
  tls:
    enabled: true
    cert_file: "./certs/server.crt"
    key_file: "./certs/server.key"
    min_version: "1.2"
    max_version: "1.3"
    cipher_suites:
      - "TLS_AES_256_GCM_SHA384"
      - "TLS_CHACHA20_POLY1305_SHA256"
      - "TLS_AES_128_GCM_SHA256"

  # 加密配置
  encryption:
    algorithm: "AES-256-GCM"
    key_derivation: "PBKDF2"
    iterations: 100000

# ================================
# 限流配置（已禁用）
# ================================
rate_limiting:
  algorithm: "token_bucket"
  rate: 1000
  burst: 100
  window: "1m"
  cleanup_period: "5m"

# ================================
# 向后兼容字段
# ================================
# 以下字段保留以确保向后兼容性，建议使用上述统一配置

# 缓存兼容字段
cache_legacy:
  enabled: true
  type: "memory"
  ttl: "3600s"
  size: 1000
  cleanup_interval: "300s"

# 服务器端口兼容字段
server_legacy:
  port: 8080
  https_port: 8443
  socks_port: 1080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  connect_timeout: "10s"

# 模块启用兼容字段
modules_legacy:
  proxy_enabled: true
  cache_enabled: true
  logging_enabled: true
  monitoring_enabled: true
  security_enabled: true
  dns_service_enabled: true
  rate_limiting_enabled: false
