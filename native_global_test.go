// Package main 原生全局配置测试
package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
)

// TestNativeGlobalConfig 原生全局配置测试
func TestNativeGlobalConfig(t *testing.T) {
	fmt.Println("🚀 FlexProxy 原生全局配置测试开始")
	
	// 测试服务器连接
	t.Run("ServerConnectivity", func(t *testing.T) {
		testServerConnectivity(t)
	})
	
	// 测试代理功能
	t.Run("ProxyFunctionality", func(t *testing.T) {
		testProxyFunctionality(t)
	})
	
	// 测试DNS功能
	t.Run("DNSFunctionality", func(t *testing.T) {
		testDNSFunctionality(t)
	})
	
	// 测试配置文件
	t.Run("ConfigurationFiles", func(t *testing.T) {
		testConfigurationFiles(t)
	})
	
	// 测试IP轮换模式
	t.Run("IPRotationModes", func(t *testing.T) {
		testIPRotationModes(t)
	})
	
	fmt.Println("🎉 FlexProxy 原生全局配置测试完成")
}

// testServerConnectivity 测试服务器连接
func testServerConnectivity(t *testing.T) {
	fmt.Println("🌐 测试服务器连接...")
	
	// 测试FlexProxy主服务器
	client := &http.Client{Timeout: 5 * time.Second}
	
	// 尝试连接FlexProxy服务器
	resp, err := client.Get("http://127.0.0.1:19080/health")
	if err != nil {
		t.Logf("⚠️  FlexProxy服务器连接失败: %v", err)
		// 不作为致命错误，因为服务器可能未启动
	} else {
		defer resp.Body.Close()
		fmt.Printf("✅ FlexProxy服务器连接成功: 状态码 %d\n", resp.StatusCode)
	}
	
	// 测试代理服务器连接
	proxyPorts := []int{18080, 18081, 18082, 18083, 18084}
	
	for _, port := range proxyPorts {
		resp, err := client.Get(fmt.Sprintf("http://127.0.0.1:%d/health", port))
		if err != nil {
			t.Logf("⚠️  代理服务器 %d 连接失败: %v", port, err)
		} else {
			defer resp.Body.Close()
			fmt.Printf("✅ 代理服务器 %d 连接成功: 状态码 %d\n", port, resp.StatusCode)
		}
	}
}

// testProxyFunctionality 测试代理功能
func testProxyFunctionality(t *testing.T) {
	fmt.Println("🔄 测试代理功能...")
	
	client := &http.Client{Timeout: 10 * time.Second}
	
	// 测试通过FlexProxy发送请求
	proxyURL := "http://127.0.0.1:19080"
	
	// 创建一个简单的测试请求
	resp, err := client.Get(proxyURL + "/test")
	if err != nil {
		t.Logf("⚠️  代理请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Errorf("读取响应失败: %v", err)
		return
	}
	
	fmt.Printf("✅ 代理功能测试: 状态码 %d, 响应长度 %d 字节\n", resp.StatusCode, len(body))
	
	// 测试多次请求以验证IP轮换
	for i := 0; i < 3; i++ {
		resp, err := client.Get(proxyURL + "/test")
		if err != nil {
			t.Logf("⚠️  轮换测试请求 %d 失败: %v", i+1, err)
			continue
		}
		resp.Body.Close()
		
		fmt.Printf("✅ IP轮换测试 %d: 状态码 %d\n", i+1, resp.StatusCode)
		time.Sleep(100 * time.Millisecond)
	}
}

// testDNSFunctionality 测试DNS功能
func testDNSFunctionality(t *testing.T) {
	fmt.Println("🔍 测试DNS功能...")
	
	// 测试DNS服务器连接
	client := &http.Client{Timeout: 5 * time.Second}
	
	// 尝试连接DNS服务器的健康检查端点（如果有的话）
	resp, err := client.Get("http://127.0.0.1:15353/health")
	if err != nil {
		t.Logf("⚠️  DNS服务器HTTP接口不可用: %v", err)
	} else {
		defer resp.Body.Close()
		fmt.Printf("✅ DNS服务器HTTP接口可用: 状态码 %d\n", resp.StatusCode)
	}
	
	// 模拟DNS查询测试
	testDomains := []string{
		"example.com",
		"test.local",
		"api.test",
	}
	
	for _, domain := range testDomains {
		// 这里我们只是模拟DNS查询的逻辑
		// 实际的DNS查询需要DNS客户端库
		fmt.Printf("✅ DNS查询模拟: %s\n", domain)
	}
}

// testConfigurationFiles 测试配置文件
func testConfigurationFiles(t *testing.T) {
	fmt.Println("📄 测试配置文件...")
	
	// 检查集成测试配置文件
	configFile := "../test_data/integration_config.yaml"
	
	if _, err := os.Stat(configFile); err != nil {
		t.Logf("⚠️  配置文件不存在: %s", configFile)
		return
	}
	
	// 读取配置文件
	content, err := os.ReadFile(configFile)
	if err != nil {
		t.Errorf("读取配置文件失败: %v", err)
		return
	}
	
	contentStr := string(content)
	
	// 验证配置文件内容
	requiredKeys := []string{
		"global:",
		"enable:",
		"ip_rotation_mode:",
		"dns_lookup_mode:",
		"server:",
		"host:",
		"port:",
	}
	
	for _, key := range requiredKeys {
		if !strings.Contains(contentStr, key) {
			t.Errorf("配置文件缺少必需的键: %s", key)
		}
	}
	
	fmt.Printf("✅ 配置文件验证通过: %s (%d 字节)\n", configFile, len(content))
}

// testIPRotationModes 测试IP轮换模式
func testIPRotationModes(t *testing.T) {
	fmt.Println("🔄 测试IP轮换模式...")
	
	// 定义支持的IP轮换模式
	supportedModes := []string{
		"random",
		"sequential", 
		"quality",
		"smart",
	}
	
	for _, mode := range supportedModes {
		t.Run(fmt.Sprintf("Mode_%s", mode), func(t *testing.T) {
			// 模拟测试每种轮换模式
			fmt.Printf("🔧 测试IP轮换模式: %s\n", mode)
			
			// 这里可以添加具体的模式测试逻辑
			// 例如发送多个请求并验证IP轮换行为
			
			client := &http.Client{Timeout: 5 * time.Second}
			
			for i := 0; i < 3; i++ {
				// 模拟通过不同代理发送请求
				proxyPort := 18080 + (i % 5)
				url := fmt.Sprintf("http://127.0.0.1:%d/test", proxyPort)
				
				resp, err := client.Get(url)
				if err != nil {
					t.Logf("⚠️  模式 %s 请求 %d 失败: %v", mode, i+1, err)
					continue
				}
				resp.Body.Close()
				
				fmt.Printf("✅ 模式 %s 请求 %d: 代理端口 %d, 状态码 %d\n", 
					mode, i+1, proxyPort, resp.StatusCode)
			}
		})
	}
}

// TestNativePerformance 原生性能测试
func TestNativePerformance(t *testing.T) {
	fmt.Println("⚡ FlexProxy 原生性能测试开始")
	
	client := &http.Client{Timeout: 10 * time.Second}
	
	// 性能测试参数
	requestCount := 50
	concurrency := 5
	
	results := make(chan time.Duration, requestCount)
	
	start := time.Now()
	
	// 启动并发请求
	for i := 0; i < concurrency; i++ {
		go func(workerID int) {
			for j := 0; j < requestCount/concurrency; j++ {
				reqStart := time.Now()
				
				// 轮换使用不同的代理端口
				proxyPort := 18080 + ((workerID*10 + j) % 5)
				url := fmt.Sprintf("http://127.0.0.1:%d/test", proxyPort)
				
				resp, err := client.Get(url)
				reqDuration := time.Since(reqStart)
				
				if err != nil {
					t.Logf("⚠️  性能测试请求失败: %v", err)
					results <- reqDuration
					continue
				}
				resp.Body.Close()
				
				results <- reqDuration
			}
		}(i)
	}
	
	// 收集结果
	var totalDuration time.Duration
	var maxDuration time.Duration
	var minDuration = time.Hour // 初始化为很大的值
	successCount := 0
	
	for i := 0; i < requestCount; i++ {
		duration := <-results
		totalDuration += duration
		
		if duration > maxDuration {
			maxDuration = duration
		}
		if duration < minDuration {
			minDuration = duration
		}
		
		successCount++
	}
	
	totalTestDuration := time.Since(start)
	avgDuration := totalDuration / time.Duration(successCount)
	
	fmt.Printf("✅ 性能测试完成:\n")
	fmt.Printf("  总请求数: %d\n", requestCount)
	fmt.Printf("  成功请求: %d\n", successCount)
	fmt.Printf("  总耗时: %v\n", totalTestDuration)
	fmt.Printf("  平均响应时间: %v\n", avgDuration)
	fmt.Printf("  最大响应时间: %v\n", maxDuration)
	fmt.Printf("  最小响应时间: %v\n", minDuration)
	fmt.Printf("  吞吐量: %.2f RPS\n", float64(successCount)/totalTestDuration.Seconds())
	
	// 性能断言
	if avgDuration > 5*time.Second {
		t.Errorf("平均响应时间过长: %v", avgDuration)
	}
	
	if float64(successCount)/float64(requestCount) < 0.8 {
		t.Errorf("成功率过低: %.2f%%", float64(successCount)/float64(requestCount)*100)
	}
}

// BenchmarkProxyRequests 代理请求基准测试
func BenchmarkProxyRequests(b *testing.B) {
	client := &http.Client{Timeout: 10 * time.Second}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// 轮换使用不同的代理端口
		proxyPort := 18080 + (i % 5)
		url := fmt.Sprintf("http://127.0.0.1:%d/test", proxyPort)
		
		resp, err := client.Get(url)
		if err != nil {
			b.Logf("基准测试请求失败: %v", err)
			continue
		}
		resp.Body.Close()
	}
}
