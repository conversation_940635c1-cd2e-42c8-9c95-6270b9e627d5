// Package scenarios 包含配置热重载测试
package scenarios

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/flexp/flexp/tests/integration/framework"
	"github.com/stretchr/testify/suite"
)

// ConfigReloadTestSuite 配置热重载测试套件
type ConfigReloadTestSuite struct {
	framework.IntegrationTestSuite
	testUtils      *framework.TestUtils
	configFile     string
	backupFile     string
	reloadHistory  []ReloadEvent
	currentConfig  ConfigSnapshot
}

// ReloadEvent 重载事件
type ReloadEvent struct {
	Timestamp   time.Time
	ConfigType  string
	ChangeType  string
	Success     bool
	ErrorMsg    string
	Duration    time.Duration
}

// ConfigSnapshot 配置快照
type ConfigSnapshot struct {
	ServerPort     int
	LogLevel       string
	CacheSize      int
	ProxyStrategy  string
	MonitoringPort int
	LastModified   time.Time
}

// SetupSuite 测试套件初始化
func (s *ConfigReloadTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.reloadHistory = make([]ReloadEvent, 0)
	
	// 创建初始配置文件
	s.configFile = fmt.Sprintf("%s/config_reload_test.yaml", s.GetEnvironment().GetConfigDir())
	s.backupFile = fmt.Sprintf("%s/config_reload_backup.yaml", s.GetEnvironment().GetConfigDir())
	
	// 创建初始配置
	initialConfig := s.createInitialConfig()
	err := s.testUtils.WriteFile(s.configFile, []byte(initialConfig))
	s.Require().NoError(err, "创建初始配置文件失败")
	
	// 备份初始配置
	err = s.testUtils.WriteFile(s.backupFile, []byte(initialConfig))
	s.Require().NoError(err, "创建备份配置文件失败")
	
	// 记录初始配置快照
	s.currentConfig = ConfigSnapshot{
		ServerPort:     18080,
		LogLevel:       "info",
		CacheSize:      1000,
		ProxyStrategy:  "round_robin",
		MonitoringPort: 19080,
		LastModified:   time.Now(),
	}
	
	s.T().Logf("配置热重载测试环境初始化完成")
}

// TestBasicConfigReload 测试基本配置重载
func (s *ConfigReloadTestSuite) TestBasicConfigReload() {
	testName := "TestBasicConfigReload"
	s.AddLog(testName, "开始测试基本配置重载")
	
	// 测试日志级别变更
	s.testLogLevelReload(testName)
	
	// 测试缓存大小变更
	s.testCacheSizeReload(testName)
	
	// 测试代理策略变更
	s.testProxyStrategyReload(testName)
	
	s.AddLog(testName, "基本配置重载测试完成")
}

// TestInvalidConfigReload 测试无效配置重载
func (s *ConfigReloadTestSuite) TestInvalidConfigReload() {
	testName := "TestInvalidConfigReload"
	s.AddLog(testName, "开始测试无效配置重载")
	
	// 测试语法错误配置
	s.testSyntaxErrorReload(testName)
	
	// 测试无效值配置
	s.testInvalidValueReload(testName)
	
	// 测试缺失必需字段配置
	s.testMissingFieldReload(testName)
	
	s.AddLog(testName, "无效配置重载测试完成")
}

// TestConfigRollback 测试配置回滚
func (s *ConfigReloadTestSuite) TestConfigRollback() {
	testName := "TestConfigRollback"
	s.AddLog(testName, "开始测试配置回滚")
	
	// 记录当前配置
	originalConfig := s.currentConfig
	
	// 应用错误配置
	s.applyErrorConfig(testName)
	
	// 执行回滚
	s.executeRollback(testName, originalConfig)
	
	s.AddLog(testName, "配置回滚测试完成")
}

// TestConcurrentConfigReload 测试并发配置重载
func (s *ConfigReloadTestSuite) TestConcurrentConfigReload() {
	testName := "TestConcurrentConfigReload"
	s.AddLog(testName, "开始测试并发配置重载")
	
	// 模拟并发配置变更
	s.simulateConcurrentReload(testName)
	
	s.AddLog(testName, "并发配置重载测试完成")
}

// TestConfigValidation 测试配置验证
func (s *ConfigReloadTestSuite) TestConfigValidation() {
	testName := "TestConfigValidation"
	s.AddLog(testName, "开始测试配置验证")
	
	// 测试各种配置验证场景
	s.testPortValidation(testName)
	s.testTimeoutValidation(testName)
	s.testPathValidation(testName)
	
	s.AddLog(testName, "配置验证测试完成")
}

// TestConfigReloadPerformance 测试配置重载性能
func (s *ConfigReloadTestSuite) TestConfigReloadPerformance() {
	testName := "TestConfigReloadPerformance"
	s.AddLog(testName, "开始测试配置重载性能")
	
	// 测试重载性能
	s.measureReloadPerformance(testName)
	
	s.AddLog(testName, "配置重载性能测试完成")
}

// 具体测试方法

// testLogLevelReload 测试日志级别重载
func (s *ConfigReloadTestSuite) testLogLevelReload(testName string) {
	s.AddLog(testName, "测试日志级别重载")
	
	logLevels := []string{"debug", "info", "warn", "error"}
	
	for _, level := range logLevels {
		start := time.Now()
		s.AddLog(testName, fmt.Sprintf("变更日志级别为: %s", level))
		
		// 更新配置
		newConfig := s.createConfigWithLogLevel(level)
		err := s.updateConfigFile(newConfig)
		
		if err == nil {
			s.currentConfig.LogLevel = level
			s.currentConfig.LastModified = time.Now()
			
			// 记录重载事件
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "logging",
				ChangeType: "log_level_change",
				Success:    true,
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("✅ 日志级别重载成功: %s", level))
		} else {
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "logging",
				ChangeType: "log_level_change",
				Success:    false,
				ErrorMsg:   err.Error(),
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("❌ 日志级别重载失败: %s, 错误: %v", level, err))
		}
		
		time.Sleep(100 * time.Millisecond)
	}
}

// testCacheSizeReload 测试缓存大小重载
func (s *ConfigReloadTestSuite) testCacheSizeReload(testName string) {
	s.AddLog(testName, "测试缓存大小重载")
	
	cacheSizes := []int{500, 1000, 2000, 5000}
	
	for _, size := range cacheSizes {
		start := time.Now()
		s.AddLog(testName, fmt.Sprintf("变更缓存大小为: %d", size))
		
		// 更新配置
		newConfig := s.createConfigWithCacheSize(size)
		err := s.updateConfigFile(newConfig)
		
		if err == nil {
			s.currentConfig.CacheSize = size
			s.currentConfig.LastModified = time.Now()
			
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "cache",
				ChangeType: "cache_size_change",
				Success:    true,
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("✅ 缓存大小重载成功: %d", size))
		} else {
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "cache",
				ChangeType: "cache_size_change",
				Success:    false,
				ErrorMsg:   err.Error(),
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("❌ 缓存大小重载失败: %d, 错误: %v", size, err))
		}
		
		time.Sleep(100 * time.Millisecond)
	}
}

// testProxyStrategyReload 测试代理策略重载
func (s *ConfigReloadTestSuite) testProxyStrategyReload(testName string) {
	s.AddLog(testName, "测试代理策略重载")
	
	strategies := []string{"round_robin", "random", "weighted", "least_connections"}
	
	for _, strategy := range strategies {
		start := time.Now()
		s.AddLog(testName, fmt.Sprintf("变更代理策略为: %s", strategy))
		
		// 更新配置
		newConfig := s.createConfigWithProxyStrategy(strategy)
		err := s.updateConfigFile(newConfig)
		
		if err == nil {
			s.currentConfig.ProxyStrategy = strategy
			s.currentConfig.LastModified = time.Now()
			
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "proxy",
				ChangeType: "strategy_change",
				Success:    true,
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("✅ 代理策略重载成功: %s", strategy))
		} else {
			s.recordReloadEvent(ReloadEvent{
				Timestamp:  time.Now(),
				ConfigType: "proxy",
				ChangeType: "strategy_change",
				Success:    false,
				ErrorMsg:   err.Error(),
				Duration:   time.Since(start),
			})
			
			s.AddLog(testName, fmt.Sprintf("❌ 代理策略重载失败: %s, 错误: %v", strategy, err))
		}
		
		time.Sleep(100 * time.Millisecond)
	}
}

// testSyntaxErrorReload 测试语法错误重载
func (s *ConfigReloadTestSuite) testSyntaxErrorReload(testName string) {
	s.AddLog(testName, "测试语法错误配置重载")

	// 创建语法错误的配置
	invalidConfig := `
global:
  enable: true
  dns_lookup_mode: "system"
  invalid_yaml_syntax: [
server:
  host: "127.0.0.1"
  port: 18080
  # 缺少闭合括号
`

	start := time.Now()
	err := s.updateConfigFile(invalidConfig)

	s.recordReloadEvent(ReloadEvent{
		Timestamp:  time.Now(),
		ConfigType: "global",
		ChangeType: "syntax_error",
		Success:    false,
		ErrorMsg:   "YAML语法错误",
		Duration:   time.Since(start),
	})

	if err != nil {
		s.AddLog(testName, "✅ 语法错误配置被正确拒绝")
	} else {
		s.AddLog(testName, "❌ 语法错误配置未被检测到")
	}
}

// testInvalidValueReload 测试无效值重载
func (s *ConfigReloadTestSuite) testInvalidValueReload(testName string) {
	s.AddLog(testName, "测试无效值配置重载")

	// 测试无效端口
	invalidConfig := s.createConfigWithInvalidPort()
	start := time.Now()
	err := s.updateConfigFile(invalidConfig)

	s.recordReloadEvent(ReloadEvent{
		Timestamp:  time.Now(),
		ConfigType: "server",
		ChangeType: "invalid_port",
		Success:    false,
		ErrorMsg:   "端口号超出范围",
		Duration:   time.Since(start),
	})

	if err != nil {
		s.AddLog(testName, "✅ 无效端口配置被正确拒绝")
	} else {
		s.AddLog(testName, "❌ 无效端口配置未被检测到")
	}
}

// testMissingFieldReload 测试缺失字段重载
func (s *ConfigReloadTestSuite) testMissingFieldReload(testName string) {
	s.AddLog(testName, "测试缺失必需字段配置重载")

	// 创建缺失必需字段的配置
	incompleteConfig := `
global:
  enable: true
  # 缺失 dns_lookup_mode

server:
  # 缺失 host 和 port
  read_timeout: "10s"
`

	start := time.Now()
	err := s.updateConfigFile(incompleteConfig)

	s.recordReloadEvent(ReloadEvent{
		Timestamp:  time.Now(),
		ConfigType: "server",
		ChangeType: "missing_required_fields",
		Success:    false,
		ErrorMsg:   "缺失必需字段",
		Duration:   time.Since(start),
	})

	if err != nil {
		s.AddLog(testName, "✅ 缺失字段配置被正确拒绝")
	} else {
		s.AddLog(testName, "❌ 缺失字段配置未被检测到")
	}
}

// testPortValidation 测试端口验证
func (s *ConfigReloadTestSuite) testPortValidation(testName string) {
	s.AddLog(testName, "测试端口验证")

	invalidPorts := []int{-1, 0, 65536, 99999}

	for _, port := range invalidPorts {
		s.AddLog(testName, fmt.Sprintf("测试无效端口: %d", port))
		config := s.createConfigWithPort(port)
		err := s.updateConfigFile(config)

		if err != nil {
			s.AddLog(testName, fmt.Sprintf("✅ 无效端口 %d 被正确拒绝", port))
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 无效端口 %d 未被检测到", port))
		}
	}
}

// testTimeoutValidation 测试超时验证
func (s *ConfigReloadTestSuite) testTimeoutValidation(testName string) {
	s.AddLog(testName, "测试超时验证")

	invalidTimeouts := []string{"invalid", "-5s", "0", "999h"}

	for _, timeout := range invalidTimeouts {
		s.AddLog(testName, fmt.Sprintf("测试无效超时: %s", timeout))
		config := s.createConfigWithTimeout(timeout)
		err := s.updateConfigFile(config)

		if err != nil {
			s.AddLog(testName, fmt.Sprintf("✅ 无效超时 %s 被正确拒绝", timeout))
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 无效超时 %s 未被检测到", timeout))
		}
	}
}

// testPathValidation 测试路径验证
func (s *ConfigReloadTestSuite) testPathValidation(testName string) {
	s.AddLog(testName, "测试路径验证")

	invalidPaths := []string{"/nonexistent/path", "", "relative/path"}

	for _, path := range invalidPaths {
		s.AddLog(testName, fmt.Sprintf("测试无效路径: %s", path))
		config := s.createConfigWithLogPath(path)
		err := s.updateConfigFile(config)

		if err != nil {
			s.AddLog(testName, fmt.Sprintf("✅ 无效路径 %s 被正确拒绝", path))
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 无效路径 %s 未被检测到", path))
		}
	}
}

// measureReloadPerformance 测量重载性能
func (s *ConfigReloadTestSuite) measureReloadPerformance(testName string) {
	s.AddLog(testName, "测量配置重载性能")

	reloadCount := 10
	totalDuration := time.Duration(0)

	for i := 0; i < reloadCount; i++ {
		start := time.Now()

		// 交替使用不同配置
		var config string
		if i%2 == 0 {
			config = s.createConfigWithLogLevel("debug")
		} else {
			config = s.createConfigWithLogLevel("info")
		}

		err := s.updateConfigFile(config)
		duration := time.Since(start)
		totalDuration += duration

		if err == nil {
			s.AddLog(testName, fmt.Sprintf("重载 %d: 成功, 耗时: %v", i+1, duration))
		} else {
			s.AddLog(testName, fmt.Sprintf("重载 %d: 失败, 耗时: %v", i+1, duration))
		}

		time.Sleep(50 * time.Millisecond)
	}

	avgDuration := totalDuration / time.Duration(reloadCount)
	s.AddLog(testName, fmt.Sprintf("性能测试完成: 平均重载时间: %v", avgDuration))

	// 记录性能指标
	s.RecordMetric(testName, "request_count", int64(reloadCount))
	s.RecordMetric(testName, "avg_response_time", avgDuration)

	// 验证性能要求
	s.Assert().Less(avgDuration, 100*time.Millisecond, "平均重载时间应该小于100ms")
}

// 辅助方法

// updateConfigFile 更新配置文件
func (s *ConfigReloadTestSuite) updateConfigFile(content string) error {
	// 模拟配置文件更新和验证
	err := s.testUtils.WriteFile(s.configFile, []byte(content))
	if err != nil {
		return err
	}

	// 模拟配置验证（简化版本）
	if strings.Contains(content, "invalid_yaml_syntax: [") {
		return fmt.Errorf("YAML语法错误")
	}

	if strings.Contains(content, "port: -1") {
		return fmt.Errorf("端口号无效")
	}

	if strings.Contains(content, "dns_lookup_mode: \"invalid_mode\"") {
		return fmt.Errorf("DNS查询模式无效")
	}

	// 模拟重载延迟
	time.Sleep(10 * time.Millisecond)

	return nil
}

// recordReloadEvent 记录重载事件
func (s *ConfigReloadTestSuite) recordReloadEvent(event ReloadEvent) {
	s.reloadHistory = append(s.reloadHistory, event)
}

// 配置生成方法

// createInitialConfig 创建初始配置
func (s *ConfigReloadTestSuite) createInitialConfig() string {
	return `
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: 18080
  read_timeout: "10s"
  write_timeout: "10s"

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 3

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 1000

logging:
  enabled: true
  level: "info"
  format: "text"
  file: "./logs/config_reload_test.log"

monitoring:
  enabled: true
  port: 19080
  interval: "5s"
`
}

// createConfigWithLogLevel 创建指定日志级别的配置
func (s *ConfigReloadTestSuite) createConfigWithLogLevel(level string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "%s"
  format: "text"
  file: "%s/config_reload_test.log"

monitoring:
  enabled: true
  port: %d
`, s.currentConfig.ServerPort, level, s.GetLogDir(), s.currentConfig.MonitoringPort)
}

// createConfigWithCacheSize 创建指定缓存大小的配置
func (s *ConfigReloadTestSuite) createConfigWithCacheSize(size int) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: %d

logging:
  enabled: true
  level: "%s"
  file: "%s/config_reload_test.log"
`, s.currentConfig.ServerPort, size, s.currentConfig.LogLevel, s.GetLogDir())
}

// createConfigWithProxyStrategy 创建指定代理策略的配置
func (s *ConfigReloadTestSuite) createConfigWithProxyStrategy(strategy string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

proxy:
  enabled: true
  strategy: "%s"
  max_retries: 3

logging:
  enabled: true
  level: "%s"
  file: "%s/config_reload_test.log"
`, s.currentConfig.ServerPort, strategy, s.currentConfig.LogLevel, s.GetLogDir())
}

// createConfigWithInvalidPort 创建无效端口配置
func (s *ConfigReloadTestSuite) createConfigWithInvalidPort() string {
	return `
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: 99999  # 超出有效范围

logging:
  enabled: true
  level: "info"
  file: "./logs/config_reload_test.log"
`
}

// createConfigWithPort 创建指定端口的配置
func (s *ConfigReloadTestSuite) createConfigWithPort(port int) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "info"
  file: "%s/config_reload_test.log"
`, port, s.GetLogDir())
}

// createConfigWithTimeout 创建指定超时的配置
func (s *ConfigReloadTestSuite) createConfigWithTimeout(timeout string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "%s"
  write_timeout: "%s"

logging:
  enabled: true
  level: "info"
  file: "%s/config_reload_test.log"
`, s.currentConfig.ServerPort, timeout, timeout, s.GetLogDir())
}

// createConfigWithLogPath 创建指定日志路径的配置
func (s *ConfigReloadTestSuite) createConfigWithLogPath(logPath string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "info"
  file: "%s"
`, s.currentConfig.ServerPort, logPath)
}

// TearDownSuite 测试套件清理
func (s *ConfigReloadTestSuite) TearDownSuite() {
	// 清理配置文件
	if s.testUtils.FileExists(s.configFile) {
		os.Remove(s.configFile)
	}
	if s.testUtils.FileExists(s.backupFile) {
		os.Remove(s.backupFile)
	}

	// 输出重载历史统计
	s.printReloadStatistics()

	s.IntegrationTestSuite.TearDownSuite()
}

// printReloadStatistics 打印重载统计
func (s *ConfigReloadTestSuite) printReloadStatistics() {
	s.T().Logf("配置重载统计:")
	s.T().Logf("总重载次数: %d", len(s.reloadHistory))

	successCount := 0
	failureCount := 0
	totalDuration := time.Duration(0)

	for _, event := range s.reloadHistory {
		if event.Success {
			successCount++
		} else {
			failureCount++
		}
		totalDuration += event.Duration
	}

	if len(s.reloadHistory) > 0 {
		avgDuration := totalDuration / time.Duration(len(s.reloadHistory))
		s.T().Logf("成功重载: %d", successCount)
		s.T().Logf("失败重载: %d", failureCount)
		s.T().Logf("平均重载时间: %v", avgDuration)
		s.T().Logf("成功率: %.2f%%", float64(successCount)/float64(len(s.reloadHistory))*100)
	}
}

// TestConfigReloadIntegration 运行配置热重载集成测试
func TestConfigReloadIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(ConfigReloadTestSuite))
}
