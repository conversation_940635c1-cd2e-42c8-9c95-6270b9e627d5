// Package scenarios 包含端到端和跨模块协作测试
package scenarios

import (
	"fmt"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
	"github.com/stretchr/testify/suite"
)

// EndToEndTestSuite 端到端测试套件
type EndToEndTestSuite struct {
	framework.IntegrationTestSuite
	httpMockServer  *framework.HTTPMockServer
	dnsMockServer   *framework.DNSMockServer
	proxyMockServer *framework.ProxyMockServer
	testUtils       *framework.TestUtils
	serverPort      int
	monitoringPort  int
	testScenarios   []TestScenario
}

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	Steps       []TestStep
	Expected    ExpectedResult
}

// TestStep 测试步骤
type TestStep struct {
	Action      string
	Parameters  map[string]interface{}
	ExpectedResult string
}

// ExpectedResult 期望结果
type ExpectedResult struct {
	Success     bool
	ResponseTime time.Duration
	ErrorRate   float64
	Metrics     map[string]interface{}
}

// SetupSuite 测试套件初始化
func (s *EndToEndTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.testScenarios = make([]TestScenario, 0)
	
	// 分配端口
	ports, err := s.testUtils.FindFreePorts(2)
	s.Require().NoError(err, "分配端口失败")
	s.serverPort = ports[0]
	s.monitoringPort = ports[1]
	
	// 创建所有必要的 Mock 服务器
	s.httpMockServer, err = s.GetMockManager().CreateHTTPMockServer("target_server", 0)
	s.Require().NoError(err, "创建 HTTP Mock 服务器失败")
	
	s.dnsMockServer, err = s.GetMockManager().CreateDNSMockServer("dns_server", 0)
	s.Require().NoError(err, "创建 DNS Mock 服务器失败")
	
	s.proxyMockServer, err = s.GetMockManager().CreateProxyMockServer("proxy_server", 0, 100*time.Millisecond, 0.1)
	s.Require().NoError(err, "创建代理 Mock 服务器失败")
	
	// 设置测试数据
	s.setupTestData()
	
	// 启动所有 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(15 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("端到端测试环境初始化完成")
}

// setupTestData 设置测试数据
func (s *EndToEndTestSuite) setupTestData() {
	// 添加 DNS 记录
	s.dnsMockServer.AddRecord("api.example.com", []string{"*************"})
	s.dnsMockServer.AddRecord("cdn.example.com", []string{"***********", "***********"})
	s.dnsMockServer.AddRecord("load-test.com", []string{"********", "********", "********"})
	
	// 设置测试场景
	s.testScenarios = []TestScenario{
		{
			Name:        "完整代理请求流程",
			Description: "测试从客户端请求到目标服务器的完整流程",
			Steps: []TestStep{
				{"dns_lookup", map[string]interface{}{"domain": "api.example.com"}, "DNS解析成功"},
				{"proxy_selection", map[string]interface{}{"mode": "sequential"}, "选择代理成功"},
				{"http_request", map[string]interface{}{"url": "http://api.example.com/test"}, "HTTP请求成功"},
				{"cache_update", map[string]interface{}{"key": "dns:api.example.com"}, "缓存更新成功"},
				{"log_record", map[string]interface{}{"level": "info"}, "日志记录成功"},
				{"metrics_update", map[string]interface{}{"metric": "requests_total"}, "指标更新成功"},
			},
			Expected: ExpectedResult{
				Success:      true,
				ResponseTime: 500 * time.Millisecond,
				ErrorRate:    0.05,
				Metrics:      map[string]interface{}{"requests_total": 1, "cache_hits": 0},
			},
		},
		{
			Name:        "负载均衡和故障转移",
			Description: "测试多IP域名的负载均衡和代理故障转移",
			Steps: []TestStep{
				{"dns_lookup", map[string]interface{}{"domain": "load-test.com"}, "DNS解析返回多IP"},
				{"load_balance", map[string]interface{}{"strategy": "round_robin"}, "负载均衡选择IP"},
				{"proxy_failover", map[string]interface{}{"retries": 3}, "代理故障转移"},
				{"health_check", map[string]interface{}{"component": "proxy"}, "健康检查通过"},
			},
			Expected: ExpectedResult{
				Success:      true,
				ResponseTime: 800 * time.Millisecond,
				ErrorRate:    0.1,
				Metrics:      map[string]interface{}{"failover_count": 1, "health_status": "ok"},
			},
		},
	}
}

// TestCompleteProxyFlow 测试完整代理流程
func (s *EndToEndTestSuite) TestCompleteProxyFlow() {
	testName := "TestCompleteProxyFlow"
	s.AddLog(testName, "开始测试完整代理流程")
	
	// 创建完整流程配置
	configContent := s.createCompleteFlowConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("e2e_complete_flow.yaml", configContent)
	s.Require().NoError(err, "创建完整流程配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行完整流程测试
	s.executeTestScenario(testName, s.testScenarios[0])
	
	s.AddLog(testName, "完整代理流程测试完成")
}

// TestLoadBalancingAndFailover 测试负载均衡和故障转移
func (s *EndToEndTestSuite) TestLoadBalancingAndFailover() {
	testName := "TestLoadBalancingAndFailover"
	s.AddLog(testName, "开始测试负载均衡和故障转移")
	
	// 创建负载均衡配置
	configContent := s.createLoadBalancingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("e2e_loadbalance.yaml", configContent)
	s.Require().NoError(err, "创建负载均衡配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行负载均衡测试
	s.executeTestScenario(testName, s.testScenarios[1])
	
	s.AddLog(testName, "负载均衡和故障转移测试完成")
}

// TestModuleIntegration 测试模块集成
func (s *EndToEndTestSuite) TestModuleIntegration() {
	testName := "TestModuleIntegration"
	s.AddLog(testName, "开始测试模块集成")
	
	// 创建模块集成配置
	configContent := s.createModuleIntegrationConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("e2e_integration.yaml", configContent)
	s.Require().NoError(err, "创建模块集成配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟模块间协作测试
	s.simulateModuleIntegrationTest(testName)
	
	s.AddLog(testName, "模块集成测试完成")
}

// TestSecurityAndMonitoring 测试安全和监控集成
func (s *EndToEndTestSuite) TestSecurityAndMonitoring() {
	testName := "TestSecurityAndMonitoring"
	s.AddLog(testName, "开始测试安全和监控集成")
	
	// 创建安全监控配置
	configContent := s.createSecurityMonitoringConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("e2e_security_monitoring.yaml", configContent)
	s.Require().NoError(err, "创建安全监控配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟安全监控测试
	s.simulateSecurityMonitoringTest(testName)
	
	s.AddLog(testName, "安全和监控集成测试完成")
}

// TestCacheAndDNSIntegration 测试缓存和DNS集成
func (s *EndToEndTestSuite) TestCacheAndDNSIntegration() {
	testName := "TestCacheAndDNSIntegration"
	s.AddLog(testName, "开始测试缓存和DNS集成")
	
	// 创建缓存DNS配置
	configContent := s.createCacheDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("e2e_cache_dns.yaml", configContent)
	s.Require().NoError(err, "创建缓存DNS配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟缓存DNS集成测试
	s.simulateCacheDNSIntegrationTest(testName)
	
	s.AddLog(testName, "缓存和DNS集成测试完成")
}

// executeTestScenario 执行测试场景
func (s *EndToEndTestSuite) executeTestScenario(testName string, scenario TestScenario) {
	start := time.Now()
	
	s.AddLog(testName, fmt.Sprintf("执行测试场景: %s", scenario.Name))
	s.AddLog(testName, fmt.Sprintf("场景描述: %s", scenario.Description))
	
	successfulSteps := 0
	failedSteps := 0
	
	for i, step := range scenario.Steps {
		stepStart := time.Now()
		s.AddLog(testName, fmt.Sprintf("执行步骤 %d: %s", i+1, step.Action))
		
		// 模拟执行步骤
		success := s.executeTestStep(testName, step)
		stepDuration := time.Since(stepStart)
		
		if success {
			successfulSteps++
			s.AddLog(testName, fmt.Sprintf("✅ 步骤 %d 成功: %s (耗时: %v)", i+1, step.ExpectedResult, stepDuration))
		} else {
			failedSteps++
			s.AddLog(testName, fmt.Sprintf("❌ 步骤 %d 失败: %s (耗时: %v)", i+1, step.Action, stepDuration))
		}
		
		time.Sleep(10 * time.Millisecond)
	}
	
	// 验证场景结果
	scenarioSuccess := failedSteps == 0
	duration := time.Since(start)
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(scenario.Steps)))
	s.RecordMetric(testName, "success_count", int64(successfulSteps))
	s.RecordMetric(testName, "failure_count", int64(failedSteps))
	s.RecordMetric(testName, "avg_response_time", duration/time.Duration(len(scenario.Steps)))
	
	s.AddLog(testName, fmt.Sprintf("场景 %s 完成，耗时: %v，成功步骤: %d，失败步骤: %d", 
		scenario.Name, duration, successfulSteps, failedSteps))
	
	// 验证期望结果
	if scenario.Expected.Success {
		s.Assert().True(scenarioSuccess, fmt.Sprintf("场景 %s 应该成功", scenario.Name))
	}
	s.Assert().LessOrEqual(duration, scenario.Expected.ResponseTime*2, "响应时间应该在合理范围内")
}

// executeTestStep 执行测试步骤
func (s *EndToEndTestSuite) executeTestStep(testName string, step TestStep) bool {
	switch step.Action {
	case "dns_lookup":
		domain := step.Parameters["domain"].(string)
		s.AddLog(testName, fmt.Sprintf("执行DNS查询: %s", domain))
		return true // 模拟成功

	case "proxy_selection":
		mode := step.Parameters["mode"].(string)
		s.AddLog(testName, fmt.Sprintf("选择代理模式: %s", mode))
		return true // 模拟成功

	case "http_request":
		url := step.Parameters["url"].(string)
		s.AddLog(testName, fmt.Sprintf("发送HTTP请求: %s", url))
		return true // 模拟成功

	case "cache_update":
		key := step.Parameters["key"].(string)
		s.AddLog(testName, fmt.Sprintf("更新缓存: %s", key))
		return true // 模拟成功

	case "log_record":
		level := step.Parameters["level"].(string)
		s.AddLog(testName, fmt.Sprintf("记录日志: %s", level))
		return true // 模拟成功

	case "metrics_update":
		metric := step.Parameters["metric"].(string)
		s.AddLog(testName, fmt.Sprintf("更新指标: %s", metric))
		return true // 模拟成功

	case "load_balance":
		strategy := step.Parameters["strategy"].(string)
		s.AddLog(testName, fmt.Sprintf("负载均衡策略: %s", strategy))
		return true // 模拟成功

	case "proxy_failover":
		retries := step.Parameters["retries"].(int)
		s.AddLog(testName, fmt.Sprintf("代理故障转移，重试次数: %d", retries))
		return true // 模拟成功

	case "health_check":
		component := step.Parameters["component"].(string)
		s.AddLog(testName, fmt.Sprintf("健康检查组件: %s", component))
		return true // 模拟成功

	default:
		s.AddLog(testName, fmt.Sprintf("未知步骤: %s", step.Action))
		return false
	}
}

// simulateModuleIntegrationTest 模拟模块集成测试
func (s *EndToEndTestSuite) simulateModuleIntegrationTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟模块间协作")

	// 模拟各模块协作流程
	modules := []struct {
		name     string
		function string
		duration time.Duration
		success  bool
	}{
		{"Server", "接收HTTP请求", 10 * time.Millisecond, true},
		{"Security", "验证请求安全性", 15 * time.Millisecond, true},
		{"DNS", "解析目标域名", 20 * time.Millisecond, true},
		{"Cache", "检查DNS缓存", 5 * time.Millisecond, true},
		{"Proxy", "选择代理服务器", 12 * time.Millisecond, true},
		{"Logging", "记录请求日志", 8 * time.Millisecond, true},
		{"Monitoring", "更新监控指标", 6 * time.Millisecond, true},
	}

	successfulModules := 0
	totalDuration := time.Duration(0)

	for _, module := range modules {
		moduleStart := time.Now()
		s.AddLog(testName, fmt.Sprintf("模块 %s: %s", module.name, module.function))

		// 模拟模块执行时间
		time.Sleep(module.duration)

		if module.success {
			successfulModules++
			s.AddLog(testName, fmt.Sprintf("✅ 模块 %s 执行成功", module.name))
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 模块 %s 执行失败", module.name))
		}

		moduleDuration := time.Since(moduleStart)
		totalDuration += moduleDuration
	}

	// 模拟模块间数据传递
	s.AddLog(testName, "验证模块间数据传递")
	dataFlows := []string{
		"Server -> Security: 请求数据",
		"Security -> DNS: 验证通过的域名",
		"DNS -> Cache: DNS查询结果",
		"Cache -> Proxy: 缓存的IP地址",
		"Proxy -> Logging: 代理选择结果",
		"Logging -> Monitoring: 日志统计数据",
	}

	for _, flow := range dataFlows {
		s.AddLog(testName, fmt.Sprintf("数据流: %s", flow))
		time.Sleep(2 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(modules)))
	s.RecordMetric(testName, "success_count", int64(successfulModules))
	s.RecordMetric(testName, "avg_response_time", totalDuration/time.Duration(len(modules)))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("模块集成测试完成，耗时: %v，成功模块: %d/%d",
		duration, successfulModules, len(modules)))

	// 验证模块集成
	s.Assert().Equal(len(modules), successfulModules, "所有模块都应该成功执行")
	s.Assert().Equal(len(dataFlows), 6, "应该有正确的数据流数量")
}

// simulateSecurityMonitoringTest 模拟安全监控集成测试
func (s *EndToEndTestSuite) simulateSecurityMonitoringTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟安全监控集成")

	// 模拟安全事件和监控响应
	securityEvents := []struct {
		eventType string
		severity  string
		action    string
		monitored bool
	}{
		{"认证失败", "中等", "记录日志", true},
		{"可疑IP访问", "高", "阻止请求", true},
		{"频繁请求", "低", "限制速率", true},
		{"SQL注入尝试", "高", "阻止并告警", true},
		{"正常请求", "无", "允许通过", true},
	}

	processedEvents := 0
	alertsTriggered := 0

	for _, event := range securityEvents {
		s.AddLog(testName, fmt.Sprintf("安全事件: %s (严重性: %s)", event.eventType, event.severity))

		// 模拟安全处理
		s.AddLog(testName, fmt.Sprintf("安全动作: %s", event.action))

		// 模拟监控响应
		if event.monitored {
			s.AddLog(testName, fmt.Sprintf("监控记录: %s", event.eventType))
			processedEvents++

			if event.severity == "高" {
				alertsTriggered++
				s.AddLog(testName, fmt.Sprintf("🚨 触发告警: %s", event.eventType))
			}
		}

		time.Sleep(8 * time.Millisecond)
	}

	// 模拟监控指标更新
	s.AddLog(testName, "更新安全监控指标")
	metrics := map[string]int{
		"security_events_total":    len(securityEvents),
		"security_events_blocked":  2,
		"alerts_triggered":         alertsTriggered,
		"requests_allowed":         1,
	}

	for metric, value := range metrics {
		s.AddLog(testName, fmt.Sprintf("指标更新: %s = %d", metric, value))
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(securityEvents)))
	s.RecordMetric(testName, "success_count", int64(processedEvents))
	s.RecordMetric(testName, "failure_count", int64(alertsTriggered))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("安全监控集成测试完成，耗时: %v，处理事件: %d，触发告警: %d",
		duration, processedEvents, alertsTriggered))

	// 验证安全监控集成
	s.Assert().Equal(len(securityEvents), processedEvents, "所有安全事件都应该被处理")
	s.Assert().Greater(alertsTriggered, 0, "应该触发高严重性告警")
}

// simulateCacheDNSIntegrationTest 模拟缓存DNS集成测试
func (s *EndToEndTestSuite) simulateCacheDNSIntegrationTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟缓存和DNS集成")

	// 模拟DNS查询和缓存交互
	dnsQueries := []struct {
		domain      string
		firstQuery  bool
		cacheHit    bool
		responseTime time.Duration
	}{
		{"api.example.com", true, false, 50 * time.Millisecond},
		{"api.example.com", false, true, 5 * time.Millisecond},
		{"cdn.example.com", true, false, 45 * time.Millisecond},
		{"cdn.example.com", false, true, 3 * time.Millisecond},
		{"new-domain.com", true, false, 60 * time.Millisecond},
	}

	totalQueries := 0
	cacheHits := 0
	cacheMisses := 0
	totalResponseTime := time.Duration(0)

	for _, query := range dnsQueries {
		queryStart := time.Now()
		s.AddLog(testName, fmt.Sprintf("DNS查询: %s", query.domain))

		if query.cacheHit {
			s.AddLog(testName, fmt.Sprintf("✅ 缓存命中: %s", query.domain))
			cacheHits++
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 缓存未命中: %s", query.domain))
			cacheMisses++
		}

		// 模拟查询时间
		time.Sleep(query.responseTime)

		queryDuration := time.Since(queryStart)
		totalResponseTime += queryDuration
		totalQueries++

		s.AddLog(testName, fmt.Sprintf("查询完成: %s，耗时: %v", query.domain, queryDuration))
	}

	// 模拟缓存管理操作
	s.AddLog(testName, "执行缓存管理操作")
	cacheOperations := []string{
		"检查缓存容量",
		"清理过期条目",
		"更新缓存统计",
		"优化缓存策略",
	}

	for _, operation := range cacheOperations {
		s.AddLog(testName, fmt.Sprintf("缓存操作: %s", operation))
		time.Sleep(5 * time.Millisecond)
	}

	// 计算缓存效率
	hitRate := float64(cacheHits) / float64(totalQueries)
	avgResponseTime := totalResponseTime / time.Duration(totalQueries)

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(totalQueries))
	s.RecordMetric(testName, "success_count", int64(totalQueries))
	s.RecordMetric(testName, "avg_response_time", avgResponseTime)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("缓存DNS集成测试完成，耗时: %v，查询: %d，命中率: %.2f%%，平均响应: %v",
		duration, totalQueries, hitRate*100, avgResponseTime))

	// 验证缓存DNS集成
	s.Assert().Greater(cacheHits, 0, "应该有缓存命中")
	s.Assert().Greater(cacheMisses, 0, "应该有缓存未命中")
	s.Assert().Greater(hitRate, 0.3, "缓存命中率应该合理")
}

// 配置生成方法

// createCompleteFlowConfig 创建完整流程配置
func (s *EndToEndTestSuite) createCompleteFlowConfig() string {
	proxyList := []string{s.proxyMockServer.GetAddress()}
	proxyFile, _ := s.GetEnvironment().CreateProxyListFile(proxyList)

	return fmt.Sprintf(`
global:
  enable: true
  proxy_file: "%s"
  ip_rotation_mode: "sequential"
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "10s"
  write_timeout: "10s"

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 3
  retry_interval: "500ms"

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 1000

  dns:
    ttl: "300s"
    cleanup_interval: "60s"

logging:
  enabled: true
  level: "info"
  format: "json"
  file: "%s/e2e_complete_flow.log"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "5s"

security:
  enabled: true

  auth:
    type: "basic"
    token_expiry: "1h"
`, proxyFile, s.dnsMockServer.GetAddress(), s.serverPort, s.GetLogDir(), s.monitoringPort)
}

// createLoadBalancingConfig 创建负载均衡配置
func (s *EndToEndTestSuite) createLoadBalancingConfig() string {
	proxyList := []string{
		s.proxyMockServer.GetAddress(),
		"127.0.0.1:18081",
		"127.0.0.1:18082",
	}
	proxyFile, _ := s.GetEnvironment().CreateProxyListFile(proxyList)

	return fmt.Sprintf(`
global:
  enable: true
  proxy_file: "%s"
  ip_rotation_mode: "quality"
  dns_lookup_mode: "custom"

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

server:
  host: "127.0.0.1"
  port: %d

proxy:
  enabled: true
  strategy: "round_robin"
  load_balancer: "weighted"
  max_retries: 3

  health_check:
    enabled: true
    interval: "10s"
    timeout: "3s"

dns_service:
  enabled: true

  load_balancing:
    enabled: true
    strategy: "round_robin"

logging:
  enabled: true
  level: "info"
  file: "%s/e2e_loadbalance.log"

monitoring:
  enabled: true
  port: %d
  interval: "3s"
`, proxyFile, s.dnsMockServer.GetAddress(), s.serverPort, s.GetLogDir(), s.monitoringPort)
}

// createModuleIntegrationConfig 创建模块集成配置
func (s *EndToEndTestSuite) createModuleIntegrationConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "10s"
  write_timeout: "10s"

proxy:
  enabled: true
  strategy: "adaptive"
  max_retries: 2

cache:
  enabled: true
  type: "memory"
  ttl: "600s"
  size: 500

logging:
  enabled: true
  level: "debug"
  format: "json"
  file: "%s/e2e_integration.log"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "5s"

  system_metrics:
    enabled: true
    collect_interval: "10s"

security:
  enabled: true

  request_filtering:
    enabled: true
    ip_filtering:
      enabled: true
`, s.dnsMockServer.GetAddress(), s.serverPort, s.GetLogDir(), s.monitoringPort)
}

// createSecurityMonitoringConfig 创建安全监控配置
func (s *EndToEndTestSuite) createSecurityMonitoringConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d

security:
  enabled: true

  auth:
    type: "jwt"
    token_expiry: "1h"

  request_filtering:
    enabled: true

    ip_filtering:
      enabled: true
      blocked_ips:
        - "***********/24"

    rate_limiting:
      enabled: true
      global_rate: 1000
      per_ip_rate: 100

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "2s"

  alerting:
    enabled: true
    check_interval: "5s"
    rules:
      - name: "security_events_high"
        condition: "security_events_total > 10"
        severity: "warning"
      - name: "blocked_requests_high"
        condition: "blocked_requests_total > 5"
        severity: "critical"

logging:
  enabled: true
  level: "info"
  format: "json"
  file: "%s/e2e_security_monitoring.log"
`, s.serverPort, s.monitoringPort, s.GetLogDir())
}

// createCacheDNSConfig 创建缓存DNS配置
func (s *EndToEndTestSuite) createCacheDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

server:
  host: "127.0.0.1"
  port: %d

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 1000
  cleanup_interval: "60s"

  dns:
    ttl: "300s"
    cleanup_interval: "120s"
    max_entries: 500

dns_service:
  enabled: true

  caching:
    enabled: true
    default_ttl: 300
    max_ttl: 3600
    negative_ttl: 60

logging:
  enabled: true
  level: "debug"
  file: "%s/e2e_cache_dns.log"

monitoring:
  enabled: true
  port: %d
  interval: "5s"
`, s.dnsMockServer.GetAddress(), s.serverPort, s.GetLogDir(), s.monitoringPort)
}

// TestEndToEndIntegration 运行端到端集成测试
func TestEndToEndIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(EndToEndTestSuite))
}
