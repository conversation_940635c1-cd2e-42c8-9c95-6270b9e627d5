// Package scenarios 包含并发和性能测试
package scenarios

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/flexp/flexp/tests/integration/framework"
	"github.com/stretchr/testify/suite"
)

// ConcurrentTestSuite 并发测试套件
type ConcurrentTestSuite struct {
	framework.IntegrationTestSuite
	httpMockServer  *framework.HTTPMockServer
	proxyMockServer *framework.ProxyMockServer
	testUtils       *framework.TestUtils
	serverPort      int
	concurrentStats ConcurrentStats
}

// ConcurrentStats 并发测试统计
type ConcurrentStats struct {
	TotalRequests    int64
	SuccessRequests  int64
	FailedRequests   int64
	TotalDuration    time.Duration
	MaxResponseTime  time.Duration
	MinResponseTime  time.Duration
	AvgResponseTime  time.Duration
	ThroughputRPS    float64
	ConcurrentUsers  int
	ErrorRate        float64
	MemoryUsageMB    float64
	CPUUsagePercent  float64
}

// SetupSuite 测试套件初始化
func (s *ConcurrentTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	
	// 分配服务器端口
	port, err := s.testUtils.FindFreePort()
	s.Require().NoError(err, "分配服务器端口失败")
	s.serverPort = port
	
	// 创建 Mock 服务器
	s.httpMockServer, err = s.GetMockManager().CreateHTTPMockServer("concurrent_target", 0)
	s.Require().NoError(err, "创建 HTTP Mock 服务器失败")
	
	s.proxyMockServer, err = s.GetMockManager().CreateProxyMockServer("concurrent_proxy", 0, 50*time.Millisecond, 0.05)
	s.Require().NoError(err, "创建代理 Mock 服务器失败")
	
	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("并发测试环境初始化完成，服务器端口: %d", s.serverPort)
}

// TestLowConcurrency 测试低并发场景
func (s *ConcurrentTestSuite) TestLowConcurrency() {
	testName := "TestLowConcurrency"
	s.AddLog(testName, "开始测试低并发场景")
	
	// 创建低并发配置
	configContent := s.createLowConcurrencyConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_low.yaml", configContent)
	s.Require().NoError(err, "创建低并发配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行低并发测试
	s.executeConcurrentTest(testName, 10, 100, 5*time.Second)
	
	s.AddLog(testName, "低并发场景测试完成")
}

// TestMediumConcurrency 测试中等并发场景
func (s *ConcurrentTestSuite) TestMediumConcurrency() {
	testName := "TestMediumConcurrency"
	s.AddLog(testName, "开始测试中等并发场景")
	
	// 创建中等并发配置
	configContent := s.createMediumConcurrencyConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_medium.yaml", configContent)
	s.Require().NoError(err, "创建中等并发配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行中等并发测试
	s.executeConcurrentTest(testName, 50, 500, 10*time.Second)
	
	s.AddLog(testName, "中等并发场景测试完成")
}

// TestHighConcurrency 测试高并发场景
func (s *ConcurrentTestSuite) TestHighConcurrency() {
	testName := "TestHighConcurrency"
	s.AddLog(testName, "开始测试高并发场景")
	
	// 创建高并发配置
	configContent := s.createHighConcurrencyConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_high.yaml", configContent)
	s.Require().NoError(err, "创建高并发配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行高并发测试
	s.executeConcurrentTest(testName, 100, 1000, 15*time.Second)
	
	s.AddLog(testName, "高并发场景测试完成")
}

// TestStressTest 测试压力测试
func (s *ConcurrentTestSuite) TestStressTest() {
	testName := "TestStressTest"
	s.AddLog(testName, "开始压力测试")
	
	// 创建压力测试配置
	configContent := s.createStressTestConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_stress.yaml", configContent)
	s.Require().NoError(err, "创建压力测试配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行压力测试
	s.executeConcurrentTest(testName, 200, 2000, 20*time.Second)
	
	s.AddLog(testName, "压力测试完成")
}

// TestLoadRampUp 测试负载递增
func (s *ConcurrentTestSuite) TestLoadRampUp() {
	testName := "TestLoadRampUp"
	s.AddLog(testName, "开始负载递增测试")
	
	// 创建负载递增配置
	configContent := s.createLoadRampUpConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_rampup.yaml", configContent)
	s.Require().NoError(err, "创建负载递增配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行负载递增测试
	s.executeRampUpTest(testName)
	
	s.AddLog(testName, "负载递增测试完成")
}

// TestMemoryLeakDetection 测试内存泄漏检测
func (s *ConcurrentTestSuite) TestMemoryLeakDetection() {
	testName := "TestMemoryLeakDetection"
	s.AddLog(testName, "开始内存泄漏检测测试")
	
	// 创建内存泄漏检测配置
	configContent := s.createMemoryLeakConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("concurrent_memory.yaml", configContent)
	s.Require().NoError(err, "创建内存泄漏检测配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 执行内存泄漏检测测试
	s.executeMemoryLeakTest(testName)
	
	s.AddLog(testName, "内存泄漏检测测试完成")
}

// executeConcurrentTest 执行并发测试
func (s *ConcurrentTestSuite) executeConcurrentTest(testName string, concurrentUsers int, totalRequests int, duration time.Duration) {
	start := time.Now()
	
	s.AddLog(testName, fmt.Sprintf("开始并发测试: 并发用户=%d, 总请求=%d, 持续时间=%v", 
		concurrentUsers, totalRequests, duration))
	
	// 重置统计数据
	s.concurrentStats = ConcurrentStats{
		ConcurrentUsers: concurrentUsers,
		MinResponseTime: time.Hour, // 初始化为很大的值
	}
	
	// 创建上下文和取消函数
	ctx, cancel := context.WithTimeout(s.GetContext(), duration)
	defer cancel()
	
	// 创建等待组和通道
	var wg sync.WaitGroup
	requestChan := make(chan int, totalRequests)
	
	// 填充请求通道
	for i := 0; i < totalRequests; i++ {
		requestChan <- i + 1
	}
	close(requestChan)
	
	// 启动并发工作者
	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go s.concurrentWorker(ctx, &wg, requestChan, testName, i+1)
	}
	
	// 启动监控协程
	monitorDone := make(chan bool)
	go s.monitorResources(ctx, testName, monitorDone)
	
	// 等待所有工作者完成
	wg.Wait()
	cancel() // 停止监控
	<-monitorDone
	
	// 计算最终统计数据
	s.calculateFinalStats(start)
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", s.concurrentStats.TotalRequests)
	s.RecordMetric(testName, "success_count", s.concurrentStats.SuccessRequests)
	s.RecordMetric(testName, "failure_count", s.concurrentStats.FailedRequests)
	s.RecordMetric(testName, "avg_response_time", s.concurrentStats.AvgResponseTime)
	s.RecordMetric(testName, "throughput_rps", s.concurrentStats.ThroughputRPS)
	s.RecordMetric(testName, "error_rate", s.concurrentStats.ErrorRate)
	s.RecordMetric(testName, "memory_usage_mb", s.concurrentStats.MemoryUsageMB)
	s.RecordMetric(testName, "cpu_usage_percent", s.concurrentStats.CPUUsagePercent)
	
	s.AddLog(testName, fmt.Sprintf("并发测试完成: 总耗时=%v, 成功=%d, 失败=%d, 吞吐量=%.2f RPS, 错误率=%.2f%%", 
		s.concurrentStats.TotalDuration, s.concurrentStats.SuccessRequests, s.concurrentStats.FailedRequests,
		s.concurrentStats.ThroughputRPS, s.concurrentStats.ErrorRate*100))
	
	// 验证并发测试结果
	s.Assert().Greater(s.concurrentStats.SuccessRequests, int64(0), "应该有成功的请求")
	s.Assert().LessOrEqual(s.concurrentStats.ErrorRate, 0.1, "错误率应该小于10%")
	s.Assert().Greater(s.concurrentStats.ThroughputRPS, 10.0, "吞吐量应该大于10 RPS")
}

// concurrentWorker 并发工作者
func (s *ConcurrentTestSuite) concurrentWorker(ctx context.Context, wg *sync.WaitGroup, requestChan <-chan int, testName string, workerID int) {
	defer wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case requestID, ok := <-requestChan:
			if !ok {
				return
			}

			// 执行单个请求
			s.executeRequest(ctx, testName, workerID, requestID)
		}
	}
}

// executeRequest 执行单个请求
func (s *ConcurrentTestSuite) executeRequest(ctx context.Context, testName string, workerID, requestID int) {
	start := time.Now()

	// 模拟请求处理
	select {
	case <-ctx.Done():
		return
	case <-time.After(time.Duration(10+requestID%50) * time.Millisecond): // 模拟变化的响应时间
		// 请求完成
	}

	duration := time.Since(start)

	// 模拟请求成功/失败（95%成功率）
	success := requestID%20 != 0

	// 更新统计数据
	atomic.AddInt64(&s.concurrentStats.TotalRequests, 1)
	if success {
		atomic.AddInt64(&s.concurrentStats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&s.concurrentStats.FailedRequests, 1)
	}

	// 更新响应时间统计
	s.updateResponseTimeStats(duration)

	// 记录详细日志（仅记录部分请求以避免日志过多）
	if requestID%100 == 0 {
		s.AddLog(testName, fmt.Sprintf("工作者 %d 完成请求 %d: 成功=%t, 耗时=%v",
			workerID, requestID, success, duration))
	}
}

// updateResponseTimeStats 更新响应时间统计
func (s *ConcurrentTestSuite) updateResponseTimeStats(duration time.Duration) {
	// 使用原子操作更新最大和最小响应时间
	for {
		current := atomic.LoadInt64((*int64)(&s.concurrentStats.MaxResponseTime))
		if duration <= time.Duration(current) {
			break
		}
		if atomic.CompareAndSwapInt64((*int64)(&s.concurrentStats.MaxResponseTime), current, int64(duration)) {
			break
		}
	}

	for {
		current := atomic.LoadInt64((*int64)(&s.concurrentStats.MinResponseTime))
		if duration >= time.Duration(current) {
			break
		}
		if atomic.CompareAndSwapInt64((*int64)(&s.concurrentStats.MinResponseTime), current, int64(duration)) {
			break
		}
	}
}

// monitorResources 监控资源使用
func (s *ConcurrentTestSuite) monitorResources(ctx context.Context, testName string, done chan<- bool) {
	defer func() { done <- true }()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	sampleCount := 0
	totalMemory := 0.0
	totalCPU := 0.0

	for {
		select {
		case <-ctx.Done():
			// 计算平均值
			if sampleCount > 0 {
				s.concurrentStats.MemoryUsageMB = totalMemory / float64(sampleCount)
				s.concurrentStats.CPUUsagePercent = totalCPU / float64(sampleCount)
			}
			return
		case <-ticker.C:
			// 模拟资源监控
			memoryUsage := 50.0 + float64(sampleCount)*2.0 // 模拟内存增长
			cpuUsage := 30.0 + float64(sampleCount%10)*5.0 // 模拟CPU波动

			totalMemory += memoryUsage
			totalCPU += cpuUsage
			sampleCount++

			// 记录监控日志（每10秒记录一次）
			if sampleCount%10 == 0 {
				s.AddLog(testName, fmt.Sprintf("资源监控: 内存=%.1fMB, CPU=%.1f%%, 请求数=%d",
					memoryUsage, cpuUsage, atomic.LoadInt64(&s.concurrentStats.TotalRequests)))
			}
		}
	}
}

// calculateFinalStats 计算最终统计数据
func (s *ConcurrentTestSuite) calculateFinalStats(startTime time.Time) {
	s.concurrentStats.TotalDuration = time.Since(startTime)

	if s.concurrentStats.TotalRequests > 0 {
		s.concurrentStats.ErrorRate = float64(s.concurrentStats.FailedRequests) / float64(s.concurrentStats.TotalRequests)
		s.concurrentStats.ThroughputRPS = float64(s.concurrentStats.TotalRequests) / s.concurrentStats.TotalDuration.Seconds()

		// 计算平均响应时间（简化计算）
		s.concurrentStats.AvgResponseTime = time.Duration(
			(int64(s.concurrentStats.MaxResponseTime) + int64(s.concurrentStats.MinResponseTime)) / 2)
	}
}

// executeRampUpTest 执行负载递增测试
func (s *ConcurrentTestSuite) executeRampUpTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "开始负载递增测试")

	// 负载递增阶段
	rampUpStages := []struct {
		users    int
		duration time.Duration
		requests int
	}{
		{10, 30 * time.Second, 100},
		{25, 30 * time.Second, 250},
		{50, 30 * time.Second, 500},
		{75, 30 * time.Second, 750},
		{100, 30 * time.Second, 1000},
	}

	totalRequests := int64(0)
	totalSuccess := int64(0)
	totalFailures := int64(0)

	for i, stage := range rampUpStages {
		s.AddLog(testName, fmt.Sprintf("阶段 %d: %d 用户, %v 持续时间, %d 请求",
			i+1, stage.users, stage.duration, stage.requests))

		// 重置统计数据
		s.concurrentStats = ConcurrentStats{
			ConcurrentUsers: stage.users,
			MinResponseTime: time.Hour,
		}

		// 执行当前阶段
		s.executeConcurrentTest(fmt.Sprintf("%s_stage_%d", testName, i+1),
			stage.users, stage.requests, stage.duration)

		// 累计统计
		totalRequests += s.concurrentStats.TotalRequests
		totalSuccess += s.concurrentStats.SuccessRequests
		totalFailures += s.concurrentStats.FailedRequests

		s.AddLog(testName, fmt.Sprintf("阶段 %d 完成: 成功=%d, 失败=%d, 吞吐量=%.2f RPS",
			i+1, s.concurrentStats.SuccessRequests, s.concurrentStats.FailedRequests,
			s.concurrentStats.ThroughputRPS))

		// 阶段间休息
		time.Sleep(5 * time.Second)
	}

	duration := time.Since(start)
	overallErrorRate := float64(totalFailures) / float64(totalRequests)

	// 记录总体指标
	s.RecordMetric(testName, "request_count", totalRequests)
	s.RecordMetric(testName, "success_count", totalSuccess)
	s.RecordMetric(testName, "failure_count", totalFailures)
	s.RecordMetric(testName, "error_rate", overallErrorRate)

	s.AddLog(testName, fmt.Sprintf("负载递增测试完成: 总耗时=%v, 总请求=%d, 总成功=%d, 总失败=%d, 总错误率=%.2f%%",
		duration, totalRequests, totalSuccess, totalFailures, overallErrorRate*100))

	// 验证负载递增测试
	s.Assert().Greater(totalSuccess, int64(0), "应该有成功的请求")
	s.Assert().LessOrEqual(overallErrorRate, 0.15, "总体错误率应该小于15%")
}

// executeMemoryLeakTest 执行内存泄漏检测测试
func (s *ConcurrentTestSuite) executeMemoryLeakTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "开始内存泄漏检测测试")

	// 内存监控周期
	monitoringCycles := 5
	requestsPerCycle := 200
	cycleDuration := 1 * time.Minute

	memorySnapshots := make([]float64, 0, monitoringCycles)

	for cycle := 0; cycle < monitoringCycles; cycle++ {
		s.AddLog(testName, fmt.Sprintf("内存监控周期 %d/%d", cycle+1, monitoringCycles))

		// 记录周期开始时的内存使用
		initialMemory := s.getCurrentMemoryUsage()
		s.AddLog(testName, fmt.Sprintf("周期 %d 初始内存: %.2f MB", cycle+1, initialMemory))

		// 执行一轮请求
		s.executeConcurrentTest(fmt.Sprintf("%s_cycle_%d", testName, cycle+1),
			20, requestsPerCycle, cycleDuration)

		// 强制垃圾回收（模拟）
		s.AddLog(testName, "执行垃圾回收")
		time.Sleep(2 * time.Second)

		// 记录周期结束时的内存使用
		finalMemory := s.getCurrentMemoryUsage()
		memorySnapshots = append(memorySnapshots, finalMemory)

		s.AddLog(testName, fmt.Sprintf("周期 %d 结束内存: %.2f MB, 增长: %.2f MB",
			cycle+1, finalMemory, finalMemory-initialMemory))

		// 周期间休息
		time.Sleep(10 * time.Second)
	}

	// 分析内存趋势
	memoryGrowthRate := s.analyzeMemoryGrowth(memorySnapshots)

	duration := time.Since(start)

	// 记录内存泄漏检测指标
	s.RecordMetric(testName, "memory_growth_rate", memoryGrowthRate)
	s.RecordMetric(testName, "monitoring_cycles", int64(monitoringCycles))

	s.AddLog(testName, fmt.Sprintf("内存泄漏检测完成: 总耗时=%v, 内存增长率=%.2f MB/周期",
		duration, memoryGrowthRate))

	// 验证内存泄漏检测
	s.Assert().LessOrEqual(memoryGrowthRate, 5.0, "内存增长率应该小于5MB/周期")
	s.Assert().Equal(monitoringCycles, len(memorySnapshots), "应该收集到所有周期的内存快照")
}

// getCurrentMemoryUsage 获取当前内存使用（模拟）
func (s *ConcurrentTestSuite) getCurrentMemoryUsage() float64 {
	// 模拟内存使用，实际实现中会使用 runtime.MemStats
	baseMemory := 50.0
	randomVariation := float64(time.Now().UnixNano()%20) // 0-19 MB 随机变化
	return baseMemory + randomVariation
}

// analyzeMemoryGrowth 分析内存增长趋势
func (s *ConcurrentTestSuite) analyzeMemoryGrowth(snapshots []float64) float64 {
	if len(snapshots) < 2 {
		return 0.0
	}

	// 计算线性增长率
	totalGrowth := snapshots[len(snapshots)-1] - snapshots[0]
	cycles := float64(len(snapshots) - 1)

	return totalGrowth / cycles
}

// 配置生成方法

// createLowConcurrencyConfig 创建低并发配置
func (s *ConcurrentTestSuite) createLowConcurrencyConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_idle_conns: 20
  max_idle_conns_per_host: 5
  max_conns_per_host: 10

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 2
  retry_interval: "500ms"
  pool_size: 10

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 500

logging:
  enabled: true
  level: "info"
  file: "%s/concurrent_low.log"

monitoring:
  enabled: true
  port: %d
  interval: "5s"
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// createMediumConcurrencyConfig 创建中等并发配置
func (s *ConcurrentTestSuite) createMediumConcurrencyConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "20s"
  write_timeout: "20s"
  idle_timeout: "45s"
  max_idle_conns: 50
  max_idle_conns_per_host: 10
  max_conns_per_host: 25

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 3
  retry_interval: "300ms"
  pool_size: 25

cache:
  enabled: true
  type: "memory"
  ttl: "600s"
  size: 1000

logging:
  enabled: true
  level: "warn"
  file: "%s/concurrent_medium.log"

monitoring:
  enabled: true
  port: %d
  interval: "3s"
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// createHighConcurrencyConfig 创建高并发配置
func (s *ConcurrentTestSuite) createHighConcurrencyConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "15s"
  write_timeout: "15s"
  idle_timeout: "30s"
  max_idle_conns: 100
  max_idle_conns_per_host: 20
  max_conns_per_host: 50

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 2
  retry_interval: "200ms"
  pool_size: 50

cache:
  enabled: true
  type: "memory"
  ttl: "900s"
  size: 2000

logging:
  enabled: true
  level: "error"
  file: "%s/concurrent_high.log"

monitoring:
  enabled: true
  port: %d
  interval: "2s"
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// createStressTestConfig 创建压力测试配置
func (s *ConcurrentTestSuite) createStressTestConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "10s"
  write_timeout: "10s"
  idle_timeout: "20s"
  max_idle_conns: 200
  max_idle_conns_per_host: 50
  max_conns_per_host: 100
  buffer_size: 8192

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 1
  retry_interval: "100ms"
  pool_size: 100

cache:
  enabled: true
  type: "memory"
  ttl: "1200s"
  size: 5000
  cleanup_interval: "30s"

logging:
  enabled: true
  level: "error"
  file: "%s/concurrent_stress.log"

monitoring:
  enabled: true
  port: %d
  interval: "1s"

  system_metrics:
    enabled: true
    collect_interval: "2s"
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// createLoadRampUpConfig 创建负载递增配置
func (s *ConcurrentTestSuite) createLoadRampUpConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "20s"
  write_timeout: "20s"
  idle_timeout: "40s"
  max_idle_conns: 150
  max_idle_conns_per_host: 30
  max_conns_per_host: 75

proxy:
  enabled: true
  strategy: "adaptive"
  max_retries: 3
  retry_interval: "250ms"
  pool_size: 75

cache:
  enabled: true
  type: "memory"
  ttl: "600s"
  size: 3000

logging:
  enabled: true
  level: "info"
  file: "%s/concurrent_rampup.log"

monitoring:
  enabled: true
  port: %d
  interval: "2s"

  alerting:
    enabled: true
    check_interval: "10s"
    rules:
      - name: "high_error_rate"
        condition: "error_rate > 0.1"
        severity: "warning"
      - name: "high_response_time"
        condition: "avg_response_time > 1.0"
        severity: "warning"
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// createMemoryLeakConfig 创建内存泄漏检测配置
func (s *ConcurrentTestSuite) createMemoryLeakConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_idle_conns: 50
  max_idle_conns_per_host: 10
  max_conns_per_host: 25

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 2
  retry_interval: "500ms"
  pool_size: 25

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 1000
  cleanup_interval: "60s"

logging:
  enabled: true
  level: "debug"
  file: "%s/concurrent_memory.log"

monitoring:
  enabled: true
  port: %d
  interval: "1s"

  system_metrics:
    enabled: true
    collect_interval: "1s"
    metrics:
      - memory_usage
      - cpu_usage
      - goroutines_count
`, s.serverPort, s.GetLogDir(), s.serverPort+1000)
}

// TestConcurrentIntegration 运行并发集成测试
func TestConcurrentIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(ConcurrentTestSuite))
}
