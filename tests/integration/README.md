# FlexProxy 全局配置集成测试环境

这是一个完整的集成测试环境，用于在真实场景下验证 FlexProxy 的 `global` 配置模块的所有功能和性能表现。

## 🏗️ 测试环境架构

```
┌─────────────────────────────────────────────────────────────┐
│                    集成测试环境                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ DNS服务器   │  │ 代理服务器   │  │ FlexProxy   │         │
│  │ :5353       │  │ :8080-8084  │  │ :18080      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │               │               │                  │
│         └───────────────┼───────────────┘                  │
│                         │                                  │
│  ┌─────────────────────────────────────────────────────────┤
│  │              集成测试套件                               │
│  │  • IP轮换模式测试                                       │
│  │  • DNS解析和缓存测试                                    │
│  │  • 重试策略和冷却机制测试                               │
│  │  • 封禁系统测试                                         │
│  │  • 并发和性能测试                                       │
│  │  • 故障转移测试                                         │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
tests/integration/
├── README.md                    # 本文档
├── main_test.go                 # 主测试入口
├── run_integration_tests.sh     # 自动化测试脚本
├── framework/                   # 测试框架和基础设施
│   ├── test_suite.go           # 基础测试套件
│   ├── mock_manager.go         # Mock 服务器管理
│   ├── environment.go          # 测试环境管理
│   └── utils.go                # 测试工具函数
├── modules/                     # 模块集成测试
│   ├── server_test.go          # Server 模块测试
│   ├── proxy_test.go           # Proxy 模块测试
│   ├── cache_test.go           # Cache 模块测试
│   ├── logging_test.go         # Logging 模块测试
│   ├── monitoring_test.go      # Monitoring 模块测试
│   ├── security_test.go        # Security 模块测试
│   └── dns_service_test.go     # DNS Service 模块测试
├── scenarios/                   # 集成测试场景
│   ├── end_to_end_test.go      # 端到端测试
│   ├── concurrent_test.go      # 并发测试
│   ├── config_reload_test.go   # 配置热重载测试
│   ├── ban_system_test.go      # 封禁系统测试
│   ├── network_failure_test.go # 网络故障测试
│   └── global_config_integration_test.go # 全局配置测试
├── reports/                     # 报告生成
│   └── report_generator.go     # 报告生成器
├── mock_servers/                # 模拟服务器
│   ├── dns_server.go           # 模拟DNS服务器
│   └── proxy_server.go         # 模拟代理服务器
├── test_data/                   # 测试数据
│   ├── proxies_large.txt       # 大型代理列表
│   ├── hosts_custom.txt        # 自定义hosts文件
│   └── integration_config.yaml # 集成测试配置
├── performance/                 # 性能测试
│   └── performance_test.go     # 性能测试套件
├── logs/                       # 日志文件 (运行时创建)
└── pids/                       # PID文件 (运行时创建)
```

## 🚀 快速开始

### 1. 运行完整测试流程
```bash
cd tests/integration
./run_integration_tests.sh full
```

### 2. 分步骤运行
```bash
# 启动所有模拟服务器
./run_integration_tests.sh start

# 检查服务状态
./run_integration_tests.sh status

# 运行集成测试
./run_integration_tests.sh test

# 运行性能测试
./run_integration_tests.sh performance

# 停止所有服务
./run_integration_tests.sh stop
```

### 3. 运行特定模块测试
```bash
# 运行所有模块测试
cd modules && go test -v ./...

# 运行特定模块测试
cd modules && go test -run TestServerIntegration -v
cd modules && go test -run TestProxyIntegration -v
cd modules && go test -run TestCacheIntegration -v
cd modules && go test -run TestLoggingIntegration -v
cd modules && go test -run TestMonitoringIntegration -v
cd modules && go test -run TestSecurityIntegration -v
cd modules && go test -run TestDNSServiceIntegration -v
```

### 4. 运行场景测试
```bash
# 运行所有场景测试
cd scenarios && go test -v ./...

# 运行特定场景测试
cd scenarios && go test -run TestEndToEndIntegration -v
cd scenarios && go test -run TestConcurrentIntegration -v
cd scenarios && go test -run TestConfigReloadIntegration -v
```

### 5. 生成测试报告
```bash
# 运行测试并生成报告
go test ./tests/integration -output=./reports -format=both -v

# 只生成 HTML 报告
go test ./tests/integration -format=html -v

# 只生成 JSON 报告
go test ./tests/integration -format=json -v
```

## 🧪 测试覆盖范围

### 1. 模拟服务器功能

#### DNS服务器 (端口 5353)
- ✅ 自定义DNS记录解析
- ✅ DNS缓存机制
- ✅ TTL过期处理
- ✅ 反向DNS查找
- ✅ IPv4/IPv6双栈支持

#### 代理服务器 (端口 8080-8084)
- ✅ HTTP/HTTPS代理
- ✅ SOCKS5代理支持
- ✅ 健康检查端点
- ✅ 可配置响应延迟
- ✅ 可配置失败率
- ✅ 统计信息收集
- ✅ 动态配置控制

### 2. 核心模块测试

#### Server 模块测试
- ✅ **HTTP/HTTPS 服务器**: 基本功能和 TLS 支持
- ✅ **连接管理**: 超时处理和连接池
- ✅ **请求路由**: 代理转发和错误处理
- ✅ **中间件**: 认证、日志、监控中间件
- ✅ **优雅关闭**: 服务器优雅停止机制

#### Proxy 模块测试
- ✅ **IP 轮换策略**: 顺序、随机、质量、智能模式
- ✅ **代理池管理**: 代理添加、删除、健康检查
- ✅ **故障转移**: 代理故障检测和自动切换
- ✅ **负载均衡**: 多代理负载分配
- ✅ **性能优化**: 连接复用和缓存

#### Cache 模块测试
- ✅ **DNS 缓存**: 域名解析结果缓存
- ✅ **正则缓存**: 正则表达式编译缓存
- ✅ **缓存过期**: TTL 和自动清理机制
- ✅ **内存管理**: 缓存大小限制和内存优化
- ✅ **缓存统计**: 命中率和性能指标

#### Logging 模块测试
- ✅ **多级别日志**: Debug、Info、Warn、Error、Fatal
- ✅ **多种格式**: 文本格式和 JSON 格式
- ✅ **日志轮转**: 文件大小和时间轮转
- ✅ **追踪 ID**: 请求链路追踪
- ✅ **结构化日志**: 字段化日志记录

#### Monitoring 模块测试
- ✅ **指标收集**: HTTP 请求、代理、缓存指标
- ✅ **健康检查**: 组件健康状态监控
- ✅ **系统指标**: CPU、内存、网络使用情况
- ✅ **告警系统**: 阈值告警和通知
- ✅ **HTTP 监控**: Prometheus 格式指标导出

#### Security 模块测试
- ✅ **认证授权**: 基本认证、JWT、RBAC
- ✅ **TLS 配置**: 证书管理和加密通信
- ✅ **请求过滤**: IP 过滤、用户代理过滤
- ✅ **令牌管理**: 令牌生成、验证、撤销
- ✅ **加密解密**: 数据加密和密钥管理

#### DNS Service 模块测试
- ✅ **正向查询**: 域名到 IP 地址解析
- ✅ **反向查询**: IP 地址到域名解析
- ✅ **自定义 DNS**: 多 DNS 服务器配置
- ✅ **DNS 缓存**: 查询结果缓存和过期
- ✅ **负载均衡**: DNS 查询负载分配
- ✅ **故障转移**: DNS 服务器故障处理

### 3. 场景测试

#### 端到端测试
- ✅ **完整流程**: 从请求到响应的完整链路
- ✅ **模块协作**: 各模块间的数据传递和协作
- ✅ **错误传播**: 错误在模块间的传播和处理
- ✅ **性能验证**: 端到端性能指标验证

#### 并发测试
- ✅ **多级并发**: 低、中、高并发场景测试
- ✅ **压力测试**: 极限负载下的稳定性
- ✅ **负载递增**: 渐进式负载增加测试
- ✅ **内存泄漏**: 长时间运行的内存监控
- ✅ **资源监控**: CPU、内存、网络资源使用

#### 配置热重载测试
- ✅ **基本重载**: 配置文件动态更新
- ✅ **无效配置**: 错误配置的处理和回滚
- ✅ **并发重载**: 多个配置同时更新
- ✅ **配置验证**: 配置格式和值的验证
- ✅ **性能影响**: 重载对性能的影响

### 4. 全局配置测试

#### IP轮换模式测试
- ✅ **Random模式**: 验证随机代理选择
- ✅ **Sequential模式**: 验证顺序代理轮换
- ✅ **Quality模式**: 验证基于质量的代理选择
- ✅ **Smart模式**: 验证智能缓存和优化

#### DNS功能测试
- ✅ **自定义DNS服务器**: 测试多DNS服务器配置
- ✅ **DNS缓存**: 验证缓存命中率和TTL
- ✅ **反向DNS查找**: 测试所有模式 (no, dns, file, values)
- ✅ **DNS故障转移**: 测试DNS服务器故障处理

#### 重试策略测试
- ✅ **Allow策略**: 允许代理复用
- ✅ **Deny策略**: 禁止代理复用
- ✅ **Cooldown策略**: 冷却机制验证
- ✅ **全局跟踪**: 跨请求代理状态跟踪

#### 封禁系统测试
- ✅ **IP封禁**: 单IP和网段封禁
- ✅ **域名封禁**: 域名黑名单功能
- ✅ **信任IP**: 白名单功能
- ✅ **动态封禁**: 运行时封禁管理

### 3. 性能和压力测试

#### 并发测试
- ✅ **多级并发**: 1, 10, 50, 100, 200并发
- ✅ **吞吐量测试**: RPS (每秒请求数)
- ✅ **延迟测试**: 平均/最小/最大延迟
- ✅ **成功率测试**: 高并发下的成功率

#### 代理池性能
- ✅ **不同池大小**: 5, 10, 20, 50, 100代理
- ✅ **轮换性能**: 代理切换延迟
- ✅ **健康检查影响**: 健康检查对性能的影响

#### DNS性能测试
- ✅ **缓存vs无缓存**: 性能对比
- ✅ **DNS解析延迟**: 不同DNS服务器性能
- ✅ **缓存命中率**: 缓存效果验证

#### 故障转移性能
- ✅ **代理故障检测**: 故障检测时间
- ✅ **自动切换**: 故障转移延迟
- ✅ **恢复性能**: 代理恢复后的性能

## 📊 测试指标和基准

### 性能基准
- **RPS**: > 50 requests/second (单实例)
- **成功率**: > 95% (正常情况)
- **平均延迟**: < 100ms (本地测试)
- **故障转移时间**: < 5s
- **DNS缓存命中**: > 80%

### 资源使用
- **内存增长**: < 100MB (5000请求)
- **CPU使用**: < 50% (正常负载)
- **网络连接**: 合理的连接池使用

## 🔧 配置说明

### 集成测试配置 (integration_config.yaml)
```yaml
global:
  enable: true
  proxy_file: "./test_data/proxies_large.txt"
  ip_rotation_mode: "smart"
  dns_lookup_mode: "custom"
  reverse_dns_lookup: "file:./test_data/hosts_custom.txt"
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 30
  # ... 更多配置
```

### 代理服务器配置
- **快速代理** (8080-8081): 50ms延迟, 1%失败率
- **中等代理** (8082-8083): 200ms延迟, 5%失败率
- **慢速代理** (8084): 1000ms延迟, 10%失败率

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口使用情况
   lsof -i :5353
   lsof -i :8080-8084
   lsof -i :18080
   
   # 强制停止服务
   ./run_integration_tests.sh stop
   ```

2. **服务启动失败**
   ```bash
   # 查看日志
   tail -f logs/dns_server.log
   tail -f logs/proxy_server_8080.log
   tail -f logs/flexproxy.log
   ```

3. **测试失败**
   ```bash
   # 检查服务状态
   ./run_integration_tests.sh status
   
   # 重启服务
   ./run_integration_tests.sh restart
   ```

### 调试模式
```bash
# 启用详细日志
export DEBUG=1
./run_integration_tests.sh full

# 单独运行特定测试
cd scenarios
go test -v -run TestIPRotationModes

cd ../performance
go test -v -bench=BenchmarkProxyRotation
```

## 📈 扩展和定制

### 添加新的测试场景
1. 在 `scenarios/` 目录下创建新的测试文件
2. 实现 `testify/suite` 测试套件
3. 在 `run_integration_tests.sh` 中添加测试调用

### 添加新的模拟服务器
1. 在 `mock_servers/` 目录下创建新的服务器
2. 在 `run_integration_tests.sh` 中添加启动逻辑
3. 更新配置文件以使用新服务器

### 自定义性能测试
1. 在 `performance/` 目录下添加新的基准测试
2. 使用 `testing.B` 进行基准测试
3. 收集和分析性能指标

## 📝 贡献指南

### 测试开发规范
1. **命名规范**: 测试函数以 `Test` 开头，基准测试以 `Benchmark` 开头
2. **错误处理**: 使用 `require` 处理致命错误，`assert` 处理非致命错误
3. **清理资源**: 在 `TearDown` 方法中清理资源
4. **日志记录**: 使用 `t.Logf` 记录重要信息

### 提交要求
1. 所有测试必须通过
2. 新功能需要对应的测试用例
3. 性能测试需要基准数据
4. 更新相关文档

## 📄 许可证

本集成测试环境遵循 FlexProxy 项目的许可证。
