// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
)

// DNSServiceIntegrationTestSuite DNS Service 模块集成测试套件
type DNSServiceIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	dnsMockServer *framework.DNSMockServer
	testUtils     *framework.TestUtils
	dnsPort       int
	dnsRecords    map[string][]string
	queryStats    map[string]int
}

// SetupSuite 测试套件初始化
func (s *DNSServiceIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.dnsRecords = make(map[string][]string)
	s.queryStats = make(map[string]int)
	
	// 分配 DNS 服务端口
	port, err := s.testUtils.FindFreePort()
	s.Require().NoError(err, "分配 DNS 服务端口失败")
	s.dnsPort = port
	
	// 创建 DNS Mock 服务器
	s.dnsMockServer, err = s.GetMockManager().CreateDNSMockServer("dns_service", s.dnsPort)
	s.Require().NoError(err, "创建 DNS Mock 服务器失败")
	
	// 添加测试 DNS 记录
	s.setupTestDNSRecords()
	
	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("DNS Service 集成测试环境初始化完成，DNS 端口: %d", s.dnsPort)
}

// setupTestDNSRecords 设置测试 DNS 记录
func (s *DNSServiceIntegrationTestSuite) setupTestDNSRecords() {
	testRecords := map[string][]string{
		"example.com":      {"*************"},
		"google.com":       {"***************", "***************"},
		"github.com":       {"************"},
		"localhost.test":   {"127.0.0.1"},
		"multi-ip.test":    {"********", "********", "********"},
		"load-balance.test": {"************", "************", "************"},
		"cdn.test":         {"***********", "***********"},
	}
	
	for domain, ips := range testRecords {
		s.dnsMockServer.AddRecord(domain, ips)
		s.dnsRecords[domain] = ips
	}
}

// TestForwardDNSLookup 测试正向 DNS 查询
func (s *DNSServiceIntegrationTestSuite) TestForwardDNSLookup() {
	testName := "TestForwardDNSLookup"
	s.AddLog(testName, "开始测试正向 DNS 查询")
	
	// 创建正向 DNS 查询配置
	configContent := s.createForwardDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_forward.yaml", configContent)
	s.Require().NoError(err, "创建正向 DNS 查询配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟正向 DNS 查询测试
	s.simulateForwardDNSTest(testName)
	
	s.AddLog(testName, "正向 DNS 查询测试完成")
}

// TestReverseDNSLookup 测试反向 DNS 查询
func (s *DNSServiceIntegrationTestSuite) TestReverseDNSLookup() {
	testName := "TestReverseDNSLookup"
	s.AddLog(testName, "开始测试反向 DNS 查询")
	
	// 创建反向 DNS 查询配置
	configContent := s.createReverseDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_reverse.yaml", configContent)
	s.Require().NoError(err, "创建反向 DNS 查询配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟反向 DNS 查询测试
	s.simulateReverseDNSTest(testName)
	
	s.AddLog(testName, "反向 DNS 查询测试完成")
}

// TestCustomDNSServer 测试自定义 DNS 服务器
func (s *DNSServiceIntegrationTestSuite) TestCustomDNSServer() {
	testName := "TestCustomDNSServer"
	s.AddLog(testName, "开始测试自定义 DNS 服务器")
	
	// 创建自定义 DNS 服务器配置
	configContent := s.createCustomDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_custom.yaml", configContent)
	s.Require().NoError(err, "创建自定义 DNS 服务器配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟自定义 DNS 服务器测试
	s.simulateCustomDNSTest(testName)
	
	s.AddLog(testName, "自定义 DNS 服务器测试完成")
}

// TestDNSCaching 测试 DNS 缓存
func (s *DNSServiceIntegrationTestSuite) TestDNSCaching() {
	testName := "TestDNSCaching"
	s.AddLog(testName, "开始测试 DNS 缓存")
	
	// 创建 DNS 缓存配置
	configContent := s.createDNSCachingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_caching.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 缓存配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 缓存测试
	s.simulateDNSCachingTest(testName)
	
	s.AddLog(testName, "DNS 缓存测试完成")
}

// TestDNSLoadBalancing 测试 DNS 负载均衡
func (s *DNSServiceIntegrationTestSuite) TestDNSLoadBalancing() {
	testName := "TestDNSLoadBalancing"
	s.AddLog(testName, "开始测试 DNS 负载均衡")
	
	// 创建 DNS 负载均衡配置
	configContent := s.createDNSLoadBalancingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_loadbalance.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 负载均衡配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 负载均衡测试
	s.simulateDNSLoadBalancingTest(testName)
	
	s.AddLog(testName, "DNS 负载均衡测试完成")
}

// TestDNSFailover 测试 DNS 故障转移
func (s *DNSServiceIntegrationTestSuite) TestDNSFailover() {
	testName := "TestDNSFailover"
	s.AddLog(testName, "开始测试 DNS 故障转移")
	
	// 创建 DNS 故障转移配置
	configContent := s.createDNSFailoverConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_failover.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 故障转移配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 故障转移测试
	s.simulateDNSFailoverTest(testName)
	
	s.AddLog(testName, "DNS 故障转移测试完成")
}

// 模拟测试方法

// simulateForwardDNSTest 模拟正向 DNS 查询测试
func (s *DNSServiceIntegrationTestSuite) simulateForwardDNSTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟正向 DNS 查询")
	
	// 模拟查询不同域名
	domains := []string{
		"example.com",
		"google.com", 
		"github.com",
		"localhost.test",
		"nonexistent.test",
	}
	
	successfulQueries := 0
	failedQueries := 0
	totalResponseTime := time.Duration(0)
	
	for _, domain := range domains {
		queryStart := time.Now()
		s.AddLog(testName, fmt.Sprintf("查询域名: %s", domain))
		
		// 模拟 DNS 查询
		if ips, exists := s.dnsRecords[domain]; exists {
			successfulQueries++
			s.AddLog(testName, fmt.Sprintf("✅ 查询成功: %s -> %v", domain, ips))
			s.queryStats[domain] = s.queryStats[domain] + 1
			
			// 模拟查询延迟
			time.Sleep(20 * time.Millisecond)
		} else {
			failedQueries++
			s.AddLog(testName, fmt.Sprintf("❌ 查询失败: %s (域名不存在)", domain))
			
			// 模拟查询超时
			time.Sleep(50 * time.Millisecond)
		}
		
		queryTime := time.Since(queryStart)
		totalResponseTime += queryTime
	}
	
	avgResponseTime := totalResponseTime / time.Duration(len(domains))
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(domains)))
	s.RecordMetric(testName, "success_count", int64(successfulQueries))
	s.RecordMetric(testName, "failure_count", int64(failedQueries))
	s.RecordMetric(testName, "avg_response_time", avgResponseTime)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("正向 DNS 查询测试完成，耗时: %v，成功: %d，失败: %d，平均响应时间: %v", 
		duration, successfulQueries, failedQueries, avgResponseTime))
	
	// 验证查询结果
	s.Assert().Greater(successfulQueries, 0, "应该有成功的 DNS 查询")
	s.Assert().Greater(failedQueries, 0, "应该有失败的 DNS 查询（测试不存在的域名）")
}

// simulateReverseDNSTest 模拟反向 DNS 查询测试
func (s *DNSServiceIntegrationTestSuite) simulateReverseDNSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟反向 DNS 查询")

	// 模拟反向 DNS 查询场景
	ipAddresses := []struct {
		ip       string
		hostname string
		exists   bool
	}{
		{"*************", "example.com", true},
		{"***************", "google.com", true},
		{"127.0.0.1", "localhost", true},
		{"************0", "", false},
		{"********", "multi-ip.test", true},
	}

	successfulReverse := 0
	failedReverse := 0

	for _, addr := range ipAddresses {
		s.AddLog(testName, fmt.Sprintf("反向查询 IP: %s", addr.ip))

		if addr.exists && addr.hostname != "" {
			successfulReverse++
			s.AddLog(testName, fmt.Sprintf("✅ 反向查询成功: %s -> %s", addr.ip, addr.hostname))
		} else {
			failedReverse++
			s.AddLog(testName, fmt.Sprintf("❌ 反向查询失败: %s (无 PTR 记录)", addr.ip))
		}

		time.Sleep(15 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(ipAddresses)))
	s.RecordMetric(testName, "success_count", int64(successfulReverse))
	s.RecordMetric(testName, "failure_count", int64(failedReverse))
	s.RecordMetric(testName, "avg_response_time", 15*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("反向 DNS 查询测试完成，耗时: %v，成功: %d，失败: %d",
		duration, successfulReverse, failedReverse))

	// 验证反向查询结果
	s.Assert().Greater(successfulReverse, 0, "应该有成功的反向 DNS 查询")
}

// simulateCustomDNSTest 模拟自定义 DNS 服务器测试
func (s *DNSServiceIntegrationTestSuite) simulateCustomDNSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟自定义 DNS 服务器")

	// 模拟多个 DNS 服务器配置
	dnsServers := []struct {
		name     string
		address  string
		priority int
		timeout  time.Duration
		working  bool
	}{
		{"primary", fmt.Sprintf("127.0.0.1:%d", s.dnsPort), 1, 3*time.Second, true},
		{"secondary", "*******:53", 2, 3*time.Second, true},
		{"tertiary", "*******:53", 3, 3*time.Second, true},
		{"broken", "***************:53", 4, 1*time.Second, false},
	}

	workingServers := 0
	brokenServers := 0

	for _, server := range dnsServers {
		s.AddLog(testName, fmt.Sprintf("测试 DNS 服务器: %s (%s)", server.name, server.address))

		if server.working {
			workingServers++
			s.AddLog(testName, fmt.Sprintf("✅ DNS 服务器正常: %s", server.name))
		} else {
			brokenServers++
			s.AddLog(testName, fmt.Sprintf("❌ DNS 服务器故障: %s", server.name))
		}

		time.Sleep(10 * time.Millisecond)
	}

	// 模拟 DNS 服务器选择逻辑
	s.AddLog(testName, "选择最佳 DNS 服务器")
	selectedServer := dnsServers[0] // 选择优先级最高的可用服务器
	s.AddLog(testName, fmt.Sprintf("选择服务器: %s (优先级: %d)", selectedServer.name, selectedServer.priority))

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(dnsServers)))
	s.RecordMetric(testName, "success_count", int64(workingServers))
	s.RecordMetric(testName, "failure_count", int64(brokenServers))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("自定义 DNS 服务器测试完成，耗时: %v，正常: %d，故障: %d",
		duration, workingServers, brokenServers))

	// 验证 DNS 服务器配置
	s.Assert().Greater(workingServers, 0, "应该有正常工作的 DNS 服务器")
	s.Assert().Equal("primary", selectedServer.name, "应该选择优先级最高的服务器")
}

// simulateDNSCachingTest 模拟 DNS 缓存测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSCachingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 缓存机制")

	// 模拟 DNS 缓存场景
	cacheTestDomains := []string{"example.com", "google.com", "github.com"}
	cacheHits := 0
	cacheMisses := 0

	// 第一轮查询 - 全部缓存未命中
	s.AddLog(testName, "第一轮查询 - 缓存未命中")
	for _, domain := range cacheTestDomains {
		s.AddLog(testName, fmt.Sprintf("首次查询: %s - 缓存未命中", domain))
		cacheMisses++
		time.Sleep(30 * time.Millisecond) // 模拟 DNS 解析延迟
	}

	// 第二轮查询 - 全部缓存命中
	s.AddLog(testName, "第二轮查询 - 缓存命中")
	for _, domain := range cacheTestDomains {
		s.AddLog(testName, fmt.Sprintf("再次查询: %s - 缓存命中", domain))
		cacheHits++
		time.Sleep(2 * time.Millisecond) // 缓存命中很快
	}

	// 模拟缓存过期
	s.AddLog(testName, "等待缓存过期...")
	time.Sleep(100 * time.Millisecond) // 模拟缓存过期时间

	// 第三轮查询 - 部分缓存过期
	s.AddLog(testName, "第三轮查询 - 部分缓存过期")
	for i, domain := range cacheTestDomains {
		if i == 0 {
			s.AddLog(testName, fmt.Sprintf("查询: %s - 缓存过期，重新解析", domain))
			cacheMisses++
			time.Sleep(25 * time.Millisecond)
		} else {
			s.AddLog(testName, fmt.Sprintf("查询: %s - 缓存命中", domain))
			cacheHits++
			time.Sleep(2 * time.Millisecond)
		}
	}

	// 计算缓存命中率
	totalQueries := cacheHits + cacheMisses
	hitRate := float64(cacheHits) / float64(totalQueries)

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(totalQueries))
	s.RecordMetric(testName, "success_count", int64(totalQueries))
	s.RecordMetric(testName, "avg_response_time", 10*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 缓存测试完成，耗时: %v，命中: %d，未命中: %d，命中率: %.2f%%",
		duration, cacheHits, cacheMisses, hitRate*100))

	// 验证缓存效果
	s.Assert().Greater(cacheHits, 0, "应该有缓存命中")
	s.Assert().Greater(cacheMisses, 0, "应该有缓存未命中")
	s.Assert().Greater(hitRate, 0.5, "缓存命中率应该大于50%")
}

// simulateDNSLoadBalancingTest 模拟 DNS 负载均衡测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSLoadBalancingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 负载均衡")

	// 模拟多 IP 域名的负载均衡
	multiIPDomains := []string{"multi-ip.test", "load-balance.test", "cdn.test"}
	ipDistribution := make(map[string]map[string]int)

	for _, domain := range multiIPDomains {
		ipDistribution[domain] = make(map[string]int)
	}

	// 模拟多次 DNS 查询，观察 IP 分布
	queryCount := 20
	for i := 0; i < queryCount; i++ {
		for _, domain := range multiIPDomains {
			if ips, exists := s.dnsRecords[domain]; exists {
				// 模拟轮询负载均衡
				selectedIP := ips[i%len(ips)]
				ipDistribution[domain][selectedIP]++

				s.AddLog(testName, fmt.Sprintf("查询 %s (第%d次): 返回 %s", domain, i+1, selectedIP))
			}
			time.Sleep(2 * time.Millisecond)
		}
	}

	// 分析负载分布
	s.AddLog(testName, "分析负载分布:")
	totalQueries := 0
	balancedDomains := 0

	for domain, distribution := range ipDistribution {
		s.AddLog(testName, fmt.Sprintf("域名 %s 的 IP 分布:", domain))

		minCount := queryCount
		maxCount := 0
		for ip, count := range distribution {
			s.AddLog(testName, fmt.Sprintf("  %s: %d 次", ip, count))
			totalQueries += count
			if count < minCount {
				minCount = count
			}
			if count > maxCount {
				maxCount = count
			}
		}

		// 检查负载是否均衡（最大值和最小值差异不超过2）
		if maxCount-minCount <= 2 {
			balancedDomains++
			s.AddLog(testName, fmt.Sprintf("✅ 域名 %s 负载均衡良好", domain))
		} else {
			s.AddLog(testName, fmt.Sprintf("⚠️ 域名 %s 负载不均衡", domain))
		}
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(totalQueries))
	s.RecordMetric(testName, "success_count", int64(totalQueries))
	s.RecordMetric(testName, "avg_response_time", 2*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 负载均衡测试完成，耗时: %v，总查询: %d，均衡域名: %d/%d",
		duration, totalQueries, balancedDomains, len(multiIPDomains)))

	// 验证负载均衡效果
	s.Assert().Greater(balancedDomains, 0, "应该有负载均衡良好的域名")
}

// simulateDNSFailoverTest 模拟 DNS 故障转移测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSFailoverTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 故障转移")

	// 模拟 DNS 服务器故障转移场景
	dnsServers := []struct {
		name      string
		address   string
		available bool
		latency   time.Duration
	}{
		{"primary", fmt.Sprintf("127.0.0.1:%d", s.dnsPort), true, 10*time.Millisecond},
		{"secondary", "*******:53", true, 20*time.Millisecond},
		{"tertiary", "*******:53", true, 30*time.Millisecond},
		{"backup", "**************:53", false, 100*time.Millisecond}, // 模拟故障
	}

	testDomains := []string{"example.com", "google.com", "github.com"}
	successfulQueries := 0
	failoverCount := 0

	for _, domain := range testDomains {
		s.AddLog(testName, fmt.Sprintf("查询域名: %s", domain))

		// 尝试使用每个 DNS 服务器
		querySuccess := false
		for _, server := range dnsServers {
			s.AddLog(testName, fmt.Sprintf("尝试 DNS 服务器: %s", server.name))

			if server.available {
				s.AddLog(testName, fmt.Sprintf("✅ 服务器 %s 响应正常 (延迟: %v)", server.name, server.latency))
				successfulQueries++
				querySuccess = true

				// 如果不是主服务器，则记录故障转移
				if server.name != "primary" {
					failoverCount++
					s.AddLog(testName, fmt.Sprintf("🔄 故障转移到 %s", server.name))
				}

				time.Sleep(server.latency)
				break
			} else {
				s.AddLog(testName, fmt.Sprintf("❌ 服务器 %s 不可用", server.name))
				time.Sleep(server.latency)
			}
		}

		if !querySuccess {
			s.AddLog(testName, fmt.Sprintf("❌ 域名 %s 查询失败 - 所有 DNS 服务器不可用", domain))
		}
	}

	// 模拟主服务器恢复
	s.AddLog(testName, "模拟主服务器恢复")
	dnsServers[0].available = true
	s.AddLog(testName, "✅ 主 DNS 服务器已恢复")

	// 再次查询验证恢复
	s.AddLog(testName, "验证服务器恢复后的查询")
	for _, domain := range testDomains[:1] { // 只测试一个域名
		s.AddLog(testName, fmt.Sprintf("恢复后查询: %s", domain))
		s.AddLog(testName, "✅ 使用主服务器查询成功")
		successfulQueries++
		time.Sleep(10 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(testDomains)+1))
	s.RecordMetric(testName, "success_count", int64(successfulQueries))
	s.RecordMetric(testName, "avg_response_time", 25*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 故障转移测试完成，耗时: %v，成功查询: %d，故障转移: %d",
		duration, successfulQueries, failoverCount))

	// 验证故障转移机制
	s.Assert().Greater(successfulQueries, 0, "应该有成功的 DNS 查询")
	s.Assert().GreaterOrEqual(failoverCount, 0, "故障转移次数应该合理")
}

// 配置生成方法

// createForwardDNSConfig 创建正向 DNS 查询配置
func (s *DNSServiceIntegrationTestSuite) createForwardDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  mode: "forward"
  port: %d

  forward:
    enabled: true
    upstream_servers:
      - "*******:53"
      - "*******:53"
    timeout: "3s"
    retries: 2

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_forward_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createReverseDNSConfig 创建反向 DNS 查询配置
func (s *DNSServiceIntegrationTestSuite) createReverseDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  reverse_dns_lookup: "dns"

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  mode: "reverse"
  port: %d

  reverse:
    enabled: true
    ptr_records:
      "*************": "example.com"
      "***************": "google.com"
      "127.0.0.1": "localhost"
    timeout: "3s"
    cache_ttl: 600

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_reverse_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createCustomDNSConfig 创建自定义 DNS 服务器配置
func (s *DNSServiceIntegrationTestSuite) createCustomDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 2
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 3

dns_service:
  enabled: true
  port: %d

  server_selection:
    strategy: "priority"
    health_check: true
    health_check_interval: "30s"
    failover_timeout: "1s"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_custom_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSCachingConfig 创建 DNS 缓存配置
func (s *DNSServiceIntegrationTestSuite) createDNSCachingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 60  # 短TTL用于测试
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

cache:
  enabled: true
  type: "memory"
  ttl: "60s"
  size: 1000
  cleanup_interval: "30s"

  dns:
    ttl: "60s"
    cleanup_interval: "30s"
    max_entries: 500

dns_service:
  enabled: true
  port: %d

  caching:
    enabled: true
    default_ttl: 60
    max_ttl: 3600
    negative_ttl: 30

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_caching_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSLoadBalancingConfig 创建 DNS 负载均衡配置
func (s *DNSServiceIntegrationTestSuite) createDNSLoadBalancingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  port: %d

  load_balancing:
    enabled: true
    strategy: "round_robin"  # round_robin, random, weighted

    records:
      "multi-ip.test":
        - ip: "********"
          weight: 10
        - ip: "********"
          weight: 10
        - ip: "********"
          weight: 10
      "load-balance.test":
        - ip: "************"
          weight: 20
        - ip: "************"
          weight: 15
        - ip: "************"
          weight: 10

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_loadbalance_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSFailoverConfig 创建 DNS 故障转移配置
func (s *DNSServiceIntegrationTestSuite) createDNSFailoverConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 1000
      priority: 1
    - server: "*******:53"
      protocol: "udp"
      timeout: 2000
      priority: 2
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 3

dns_service:
  enabled: true
  port: %d

  failover:
    enabled: true
    max_retries: 3
    retry_interval: "500ms"
    health_check_interval: "10s"

    server_groups:
      primary:
        servers: ["%s"]
        priority: 1
      secondary:
        servers: ["*******:53", "*******:53"]
        priority: 2

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_failover_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.dnsMockServer.GetAddress(), s.GetLogDir())
}

// TestDNSServiceIntegration 运行 DNS Service 集成测试
func TestDNSServiceIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(DNSServiceIntegrationTestSuite))
}
