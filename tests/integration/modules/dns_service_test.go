// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"net"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
)

// DNSServiceIntegrationTestSuite DNS Service 模块集成测试套件
type DNSServiceIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	dnsMockServer   *framework.DNSMockServer
	testUtils       *framework.TestUtils
	dnsPort         int
	dnsRecords      map[string][]string
	queryStats      map[string]int
	realDNSResolver *RealDNSResolver
	dnsCache        *DNSCache
	dnsLoadBalancer *DNSLoadBalancer
	dnsFailover     *DNSFailover
}

// SetupSuite 测试套件初始化
func (s *DNSServiceIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.dnsRecords = make(map[string][]string)
	s.queryStats = make(map[string]int)

	// 分配 DNS 服务端口
	port, err := s.testUtils.FindFreePort()
	s.Require().NoError(err, "分配 DNS 服务端口失败")
	s.dnsPort = port

	// 创建 DNS Mock 服务器
	s.dnsMockServer, err = s.GetMockManager().CreateDNSMockServer("dns_service", s.dnsPort)
	s.Require().NoError(err, "创建 DNS Mock 服务器失败")

	// 添加测试 DNS 记录
	s.setupTestDNSRecords()

	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")

	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")

	// 创建真实的DNS系统组件
	s.realDNSResolver = s.createRealDNSResolver("SetupSuite")
	s.dnsCache = s.createDNSCache("SetupSuite")
	s.dnsLoadBalancer = s.createDNSLoadBalancer("SetupSuite")
	s.dnsFailover = s.createDNSFailover("SetupSuite")

	s.AddLog("SetupSuite", "🌐 DNS Service 集成测试环境初始化完成")
	s.AddLog("SetupSuite", fmt.Sprintf("📡 DNS 端口: %d", s.dnsPort))
}

// setupTestDNSRecords 设置测试 DNS 记录
func (s *DNSServiceIntegrationTestSuite) setupTestDNSRecords() {
	testRecords := map[string][]string{
		"example.com":      {"*************"},
		"google.com":       {"***************", "***************"},
		"github.com":       {"************"},
		"localhost.test":   {"127.0.0.1"},
		"multi-ip.test":    {"********", "********", "********"},
		"load-balance.test": {"************", "************", "************"},
		"cdn.test":         {"***********", "***********"},
	}
	
	for domain, ips := range testRecords {
		s.dnsMockServer.AddRecord(domain, ips)
		s.dnsRecords[domain] = ips
	}
}

// TestForwardDNSLookup 测试正向 DNS 查询
func (s *DNSServiceIntegrationTestSuite) TestForwardDNSLookup() {
	testName := "TestForwardDNSLookup"
	s.AddLog(testName, "开始测试正向 DNS 查询")
	
	// 创建正向 DNS 查询配置
	configContent := s.createForwardDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_forward.yaml", configContent)
	s.Require().NoError(err, "创建正向 DNS 查询配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟正向 DNS 查询测试
	s.simulateForwardDNSTest(testName)
	
	s.AddLog(testName, "正向 DNS 查询测试完成")
}

// TestReverseDNSLookup 测试反向 DNS 查询
func (s *DNSServiceIntegrationTestSuite) TestReverseDNSLookup() {
	testName := "TestReverseDNSLookup"
	s.AddLog(testName, "开始测试反向 DNS 查询")
	
	// 创建反向 DNS 查询配置
	configContent := s.createReverseDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_reverse.yaml", configContent)
	s.Require().NoError(err, "创建反向 DNS 查询配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟反向 DNS 查询测试
	s.simulateReverseDNSTest(testName)
	
	s.AddLog(testName, "反向 DNS 查询测试完成")
}

// TestCustomDNSServer 测试自定义 DNS 服务器
func (s *DNSServiceIntegrationTestSuite) TestCustomDNSServer() {
	testName := "TestCustomDNSServer"
	s.AddLog(testName, "开始测试自定义 DNS 服务器")
	
	// 创建自定义 DNS 服务器配置
	configContent := s.createCustomDNSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_custom.yaml", configContent)
	s.Require().NoError(err, "创建自定义 DNS 服务器配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟自定义 DNS 服务器测试
	s.simulateCustomDNSTest(testName)
	
	s.AddLog(testName, "自定义 DNS 服务器测试完成")
}

// TestDNSCaching 测试 DNS 缓存
func (s *DNSServiceIntegrationTestSuite) TestDNSCaching() {
	testName := "TestDNSCaching"
	s.AddLog(testName, "开始测试 DNS 缓存")
	
	// 创建 DNS 缓存配置
	configContent := s.createDNSCachingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_caching.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 缓存配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 缓存测试
	s.simulateDNSCachingTest(testName)
	
	s.AddLog(testName, "DNS 缓存测试完成")
}

// TestDNSLoadBalancing 测试 DNS 负载均衡
func (s *DNSServiceIntegrationTestSuite) TestDNSLoadBalancing() {
	testName := "TestDNSLoadBalancing"
	s.AddLog(testName, "开始测试 DNS 负载均衡")
	
	// 创建 DNS 负载均衡配置
	configContent := s.createDNSLoadBalancingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_loadbalance.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 负载均衡配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 负载均衡测试
	s.simulateDNSLoadBalancingTest(testName)
	
	s.AddLog(testName, "DNS 负载均衡测试完成")
}

// TestDNSFailover 测试 DNS 故障转移
func (s *DNSServiceIntegrationTestSuite) TestDNSFailover() {
	testName := "TestDNSFailover"
	s.AddLog(testName, "开始测试 DNS 故障转移")
	
	// 创建 DNS 故障转移配置
	configContent := s.createDNSFailoverConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("dns_failover.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 故障转移配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 故障转移测试
	s.simulateDNSFailoverTest(testName)
	
	s.AddLog(testName, "DNS 故障转移测试完成")
}

// 模拟测试方法

// simulateForwardDNSTest 真实正向 DNS 查询测试
func (s *DNSServiceIntegrationTestSuite) simulateForwardDNSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "🌐 执行真实正向 DNS 查询测试")

	// 真实的测试域名
	domains := []string{
		"www.google.com",
		"www.github.com",
		"httpbin.org",
		"www.cloudflare.com",
		"nonexistent-domain-12345.test",
	}

	// 执行真实的DNS查询
	s.AddLog(testName, "🔍 执行真实DNS查询...")
	queryResults := s.performRealDNSQueries(testName, domains)

	// 执行DNS缓存测试
	s.AddLog(testName, "💾 执行DNS缓存测试...")
	cacheResults := s.performDNSCacheTest(testName, domains)

	// 执行DNS性能基准测试
	s.AddLog(testName, "⚡ 执行DNS性能基准测试...")
	perfResults := s.performDNSPerformanceTest(testName, domains[:3]) // 只测试存在的域名

	// 执行DNS并发查询测试
	s.AddLog(testName, "🔄 执行DNS并发查询测试...")
	concurrentResults := s.performConcurrentDNSTest(testName, domains[:3])

	// 记录详细指标
	s.RecordMetric(testName, "total_queries", queryResults.TotalQueries)
	s.RecordMetric(testName, "successful_queries", queryResults.SuccessfulQueries)
	s.RecordMetric(testName, "failed_queries", queryResults.FailedQueries)
	s.RecordMetric(testName, "avg_response_time_ms", queryResults.AvgResponseTime.Milliseconds())
	s.RecordMetric(testName, "cache_hits", cacheResults.CacheHits)
	s.RecordMetric(testName, "cache_misses", cacheResults.CacheMisses)
	s.RecordMetric(testName, "cache_hit_rate", cacheResults.HitRate)
	s.RecordMetric(testName, "dns_qps", perfResults.QueriesPerSecond)
	s.RecordMetric(testName, "concurrent_queries", concurrentResults.ConcurrentQueries)
	s.RecordMetric(testName, "concurrent_success_rate", concurrentResults.SuccessRate)

	duration := time.Since(start)
	successRate := float64(queryResults.SuccessfulQueries) / float64(queryResults.TotalQueries) * 100

	s.AddLog(testName, fmt.Sprintf("✅ 真实正向DNS查询测试完成"))
	s.AddLog(testName, fmt.Sprintf("📊 查询统计: 总数=%d, 成功=%d, 失败=%d, 成功率=%.1f%%",
		queryResults.TotalQueries, queryResults.SuccessfulQueries, queryResults.FailedQueries, successRate))
	s.AddLog(testName, fmt.Sprintf("⏱️ 性能指标: 平均响应时间=%.2fms, QPS=%.1f",
		queryResults.AvgResponseTime.Seconds()*1000, perfResults.QueriesPerSecond))
	s.AddLog(testName, fmt.Sprintf("💾 缓存统计: 命中=%d, 未命中=%d, 命中率=%.1f%%",
		cacheResults.CacheHits, cacheResults.CacheMisses, cacheResults.HitRate*100))
	s.AddLog(testName, fmt.Sprintf("🔄 并发测试: %d个并发查询, 成功率=%.1f%%",
		concurrentResults.ConcurrentQueries, concurrentResults.SuccessRate*100))
	s.AddLog(testName, fmt.Sprintf("🕒 总耗时: %v", duration))

	// 验证查询结果
	s.Assert().Greater(queryResults.SuccessfulQueries, int64(0), "应该有成功的 DNS 查询")
	s.Assert().Greater(successRate, 60.0, "DNS查询成功率应该大于60%")
	s.Assert().Less(queryResults.AvgResponseTime, 2*time.Second, "平均响应时间应该小于2秒")
	s.Assert().Greater(perfResults.QueriesPerSecond, 1.0, "QPS应该大于1")
	s.Assert().Greater(concurrentResults.SuccessRate, 0.8, "并发查询成功率应该大于80%")
}

// simulateReverseDNSTest 模拟反向 DNS 查询测试
func (s *DNSServiceIntegrationTestSuite) simulateReverseDNSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟反向 DNS 查询")

	// 模拟反向 DNS 查询场景
	ipAddresses := []struct {
		ip       string
		hostname string
		exists   bool
	}{
		{"*************", "example.com", true},
		{"***************", "google.com", true},
		{"127.0.0.1", "localhost", true},
		{"************0", "", false},
		{"********", "multi-ip.test", true},
	}

	successfulReverse := 0
	failedReverse := 0

	for _, addr := range ipAddresses {
		s.AddLog(testName, fmt.Sprintf("反向查询 IP: %s", addr.ip))

		if addr.exists && addr.hostname != "" {
			successfulReverse++
			s.AddLog(testName, fmt.Sprintf("✅ 反向查询成功: %s -> %s", addr.ip, addr.hostname))
		} else {
			failedReverse++
			s.AddLog(testName, fmt.Sprintf("❌ 反向查询失败: %s (无 PTR 记录)", addr.ip))
		}

		time.Sleep(15 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(ipAddresses)))
	s.RecordMetric(testName, "success_count", int64(successfulReverse))
	s.RecordMetric(testName, "failure_count", int64(failedReverse))
	s.RecordMetric(testName, "avg_response_time", 15*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("反向 DNS 查询测试完成，耗时: %v，成功: %d，失败: %d",
		duration, successfulReverse, failedReverse))

	// 验证反向查询结果
	s.Assert().Greater(successfulReverse, 0, "应该有成功的反向 DNS 查询")
}

// simulateCustomDNSTest 模拟自定义 DNS 服务器测试
func (s *DNSServiceIntegrationTestSuite) simulateCustomDNSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟自定义 DNS 服务器")

	// 模拟多个 DNS 服务器配置
	dnsServers := []struct {
		name     string
		address  string
		priority int
		timeout  time.Duration
		working  bool
	}{
		{"primary", fmt.Sprintf("127.0.0.1:%d", s.dnsPort), 1, 3*time.Second, true},
		{"secondary", "*******:53", 2, 3*time.Second, true},
		{"tertiary", "*******:53", 3, 3*time.Second, true},
		{"broken", "***************:53", 4, 1*time.Second, false},
	}

	workingServers := 0
	brokenServers := 0

	for _, server := range dnsServers {
		s.AddLog(testName, fmt.Sprintf("测试 DNS 服务器: %s (%s)", server.name, server.address))

		if server.working {
			workingServers++
			s.AddLog(testName, fmt.Sprintf("✅ DNS 服务器正常: %s", server.name))
		} else {
			brokenServers++
			s.AddLog(testName, fmt.Sprintf("❌ DNS 服务器故障: %s", server.name))
		}

		time.Sleep(10 * time.Millisecond)
	}

	// 模拟 DNS 服务器选择逻辑
	s.AddLog(testName, "选择最佳 DNS 服务器")
	selectedServer := dnsServers[0] // 选择优先级最高的可用服务器
	s.AddLog(testName, fmt.Sprintf("选择服务器: %s (优先级: %d)", selectedServer.name, selectedServer.priority))

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(dnsServers)))
	s.RecordMetric(testName, "success_count", int64(workingServers))
	s.RecordMetric(testName, "failure_count", int64(brokenServers))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("自定义 DNS 服务器测试完成，耗时: %v，正常: %d，故障: %d",
		duration, workingServers, brokenServers))

	// 验证 DNS 服务器配置
	s.Assert().Greater(workingServers, 0, "应该有正常工作的 DNS 服务器")
	s.Assert().Equal("primary", selectedServer.name, "应该选择优先级最高的服务器")
}

// simulateDNSCachingTest 模拟 DNS 缓存测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSCachingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 缓存机制")

	// 模拟 DNS 缓存场景
	cacheTestDomains := []string{"example.com", "google.com", "github.com"}
	cacheHits := 0
	cacheMisses := 0

	// 第一轮查询 - 全部缓存未命中
	s.AddLog(testName, "第一轮查询 - 缓存未命中")
	for _, domain := range cacheTestDomains {
		s.AddLog(testName, fmt.Sprintf("首次查询: %s - 缓存未命中", domain))
		cacheMisses++
		time.Sleep(30 * time.Millisecond) // 模拟 DNS 解析延迟
	}

	// 第二轮查询 - 全部缓存命中
	s.AddLog(testName, "第二轮查询 - 缓存命中")
	for _, domain := range cacheTestDomains {
		s.AddLog(testName, fmt.Sprintf("再次查询: %s - 缓存命中", domain))
		cacheHits++
		time.Sleep(2 * time.Millisecond) // 缓存命中很快
	}

	// 模拟缓存过期
	s.AddLog(testName, "等待缓存过期...")
	time.Sleep(100 * time.Millisecond) // 模拟缓存过期时间

	// 第三轮查询 - 部分缓存过期
	s.AddLog(testName, "第三轮查询 - 部分缓存过期")
	for i, domain := range cacheTestDomains {
		if i == 0 {
			s.AddLog(testName, fmt.Sprintf("查询: %s - 缓存过期，重新解析", domain))
			cacheMisses++
			time.Sleep(25 * time.Millisecond)
		} else {
			s.AddLog(testName, fmt.Sprintf("查询: %s - 缓存命中", domain))
			cacheHits++
			time.Sleep(2 * time.Millisecond)
		}
	}

	// 计算缓存命中率
	totalQueries := cacheHits + cacheMisses
	hitRate := float64(cacheHits) / float64(totalQueries)

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(totalQueries))
	s.RecordMetric(testName, "success_count", int64(totalQueries))
	s.RecordMetric(testName, "avg_response_time", 10*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 缓存测试完成，耗时: %v，命中: %d，未命中: %d，命中率: %.2f%%",
		duration, cacheHits, cacheMisses, hitRate*100))

	// 验证缓存效果
	s.Assert().Greater(cacheHits, 0, "应该有缓存命中")
	s.Assert().Greater(cacheMisses, 0, "应该有缓存未命中")
	s.Assert().Greater(hitRate, 0.5, "缓存命中率应该大于50%")
}

// simulateDNSLoadBalancingTest 模拟 DNS 负载均衡测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSLoadBalancingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 负载均衡")

	// 模拟多 IP 域名的负载均衡
	multiIPDomains := []string{"multi-ip.test", "load-balance.test", "cdn.test"}
	ipDistribution := make(map[string]map[string]int)

	for _, domain := range multiIPDomains {
		ipDistribution[domain] = make(map[string]int)
	}

	// 模拟多次 DNS 查询，观察 IP 分布
	queryCount := 20
	for i := 0; i < queryCount; i++ {
		for _, domain := range multiIPDomains {
			if ips, exists := s.dnsRecords[domain]; exists {
				// 模拟轮询负载均衡
				selectedIP := ips[i%len(ips)]
				ipDistribution[domain][selectedIP]++

				s.AddLog(testName, fmt.Sprintf("查询 %s (第%d次): 返回 %s", domain, i+1, selectedIP))
			}
			time.Sleep(2 * time.Millisecond)
		}
	}

	// 分析负载分布
	s.AddLog(testName, "分析负载分布:")
	totalQueries := 0
	balancedDomains := 0

	for domain, distribution := range ipDistribution {
		s.AddLog(testName, fmt.Sprintf("域名 %s 的 IP 分布:", domain))

		minCount := queryCount
		maxCount := 0
		for ip, count := range distribution {
			s.AddLog(testName, fmt.Sprintf("  %s: %d 次", ip, count))
			totalQueries += count
			if count < minCount {
				minCount = count
			}
			if count > maxCount {
				maxCount = count
			}
		}

		// 检查负载是否均衡（最大值和最小值差异不超过2）
		if maxCount-minCount <= 2 {
			balancedDomains++
			s.AddLog(testName, fmt.Sprintf("✅ 域名 %s 负载均衡良好", domain))
		} else {
			s.AddLog(testName, fmt.Sprintf("⚠️ 域名 %s 负载不均衡", domain))
		}
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(totalQueries))
	s.RecordMetric(testName, "success_count", int64(totalQueries))
	s.RecordMetric(testName, "avg_response_time", 2*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 负载均衡测试完成，耗时: %v，总查询: %d，均衡域名: %d/%d",
		duration, totalQueries, balancedDomains, len(multiIPDomains)))

	// 验证负载均衡效果
	s.Assert().Greater(balancedDomains, 0, "应该有负载均衡良好的域名")
}

// simulateDNSFailoverTest 模拟 DNS 故障转移测试
func (s *DNSServiceIntegrationTestSuite) simulateDNSFailoverTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 DNS 故障转移")

	// 模拟 DNS 服务器故障转移场景
	dnsServers := []struct {
		name      string
		address   string
		available bool
		latency   time.Duration
	}{
		{"primary", fmt.Sprintf("127.0.0.1:%d", s.dnsPort), true, 10*time.Millisecond},
		{"secondary", "*******:53", true, 20*time.Millisecond},
		{"tertiary", "*******:53", true, 30*time.Millisecond},
		{"backup", "**************:53", false, 100*time.Millisecond}, // 模拟故障
	}

	testDomains := []string{"example.com", "google.com", "github.com"}
	successfulQueries := 0
	failoverCount := 0

	for _, domain := range testDomains {
		s.AddLog(testName, fmt.Sprintf("查询域名: %s", domain))

		// 尝试使用每个 DNS 服务器
		querySuccess := false
		for _, server := range dnsServers {
			s.AddLog(testName, fmt.Sprintf("尝试 DNS 服务器: %s", server.name))

			if server.available {
				s.AddLog(testName, fmt.Sprintf("✅ 服务器 %s 响应正常 (延迟: %v)", server.name, server.latency))
				successfulQueries++
				querySuccess = true

				// 如果不是主服务器，则记录故障转移
				if server.name != "primary" {
					failoverCount++
					s.AddLog(testName, fmt.Sprintf("🔄 故障转移到 %s", server.name))
				}

				time.Sleep(server.latency)
				break
			} else {
				s.AddLog(testName, fmt.Sprintf("❌ 服务器 %s 不可用", server.name))
				time.Sleep(server.latency)
			}
		}

		if !querySuccess {
			s.AddLog(testName, fmt.Sprintf("❌ 域名 %s 查询失败 - 所有 DNS 服务器不可用", domain))
		}
	}

	// 模拟主服务器恢复
	s.AddLog(testName, "模拟主服务器恢复")
	dnsServers[0].available = true
	s.AddLog(testName, "✅ 主 DNS 服务器已恢复")

	// 再次查询验证恢复
	s.AddLog(testName, "验证服务器恢复后的查询")
	for _, domain := range testDomains[:1] { // 只测试一个域名
		s.AddLog(testName, fmt.Sprintf("恢复后查询: %s", domain))
		s.AddLog(testName, "✅ 使用主服务器查询成功")
		successfulQueries++
		time.Sleep(10 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(testDomains)+1))
	s.RecordMetric(testName, "success_count", int64(successfulQueries))
	s.RecordMetric(testName, "avg_response_time", 25*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 故障转移测试完成，耗时: %v，成功查询: %d，故障转移: %d",
		duration, successfulQueries, failoverCount))

	// 验证故障转移机制
	s.Assert().Greater(successfulQueries, 0, "应该有成功的 DNS 查询")
	s.Assert().GreaterOrEqual(failoverCount, 0, "故障转移次数应该合理")
}

// 配置生成方法

// createForwardDNSConfig 创建正向 DNS 查询配置
func (s *DNSServiceIntegrationTestSuite) createForwardDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  mode: "forward"
  port: %d

  forward:
    enabled: true
    upstream_servers:
      - "*******:53"
      - "*******:53"
    timeout: "3s"
    retries: 2

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_forward_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createReverseDNSConfig 创建反向 DNS 查询配置
func (s *DNSServiceIntegrationTestSuite) createReverseDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  reverse_dns_lookup: "dns"

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  mode: "reverse"
  port: %d

  reverse:
    enabled: true
    ptr_records:
      "*************": "example.com"
      "***************": "google.com"
      "127.0.0.1": "localhost"
    timeout: "3s"
    cache_ttl: 600

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_reverse_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createCustomDNSConfig 创建自定义 DNS 服务器配置
func (s *DNSServiceIntegrationTestSuite) createCustomDNSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 2
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 3

dns_service:
  enabled: true
  port: %d

  server_selection:
    strategy: "priority"
    health_check: true
    health_check_interval: "30s"
    failover_timeout: "1s"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_custom_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSCachingConfig 创建 DNS 缓存配置
func (s *DNSServiceIntegrationTestSuite) createDNSCachingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 60  # 短TTL用于测试
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

cache:
  enabled: true
  type: "memory"
  ttl: "60s"
  size: 1000
  cleanup_interval: "30s"

  dns:
    ttl: "60s"
    cleanup_interval: "30s"
    max_entries: 500

dns_service:
  enabled: true
  port: %d

  caching:
    enabled: true
    default_ttl: 60
    max_ttl: 3600
    negative_ttl: 30

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_caching_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSLoadBalancingConfig 创建 DNS 负载均衡配置
func (s *DNSServiceIntegrationTestSuite) createDNSLoadBalancingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

dns_service:
  enabled: true
  port: %d

  load_balancing:
    enabled: true
    strategy: "round_robin"  # round_robin, random, weighted

    records:
      "multi-ip.test":
        - ip: "********"
          weight: 10
        - ip: "********"
          weight: 10
        - ip: "********"
          weight: 10
      "load-balance.test":
        - ip: "************"
          weight: 20
        - ip: "************"
          weight: 15
        - ip: "************"
          weight: 10

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_loadbalance_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.GetLogDir())
}

// createDNSFailoverConfig 创建 DNS 故障转移配置
func (s *DNSServiceIntegrationTestSuite) createDNSFailoverConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 1000
      priority: 1
    - server: "*******:53"
      protocol: "udp"
      timeout: 2000
      priority: 2
    - server: "*******:53"
      protocol: "udp"
      timeout: 3000
      priority: 3

dns_service:
  enabled: true
  port: %d

  failover:
    enabled: true
    max_retries: 3
    retry_interval: "500ms"
    health_check_interval: "10s"

    server_groups:
      primary:
        servers: ["%s"]
        priority: 1
      secondary:
        servers: ["*******:53", "*******:53"]
        priority: 2

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/dns_failover_test.log"
`, s.dnsMockServer.GetAddress(), s.dnsPort, s.dnsMockServer.GetAddress(), s.GetLogDir())
}

// RealDNSResolver 真实的DNS解析器
type RealDNSResolver struct {
	resolvers    []string
	timeout      time.Duration
	retries      int
	mutex        sync.RWMutex
	queryCount   int64
	successCount int64
	failureCount int64
}

// DNSCache DNS缓存
type DNSCache struct {
	cache     map[string]*DNSCacheEntry
	mutex     sync.RWMutex
	maxSize   int
	defaultTTL time.Duration
	hits      int64
	misses    int64
}

// DNSCacheEntry DNS缓存条目
type DNSCacheEntry struct {
	IPs       []string
	ExpiresAt time.Time
	CreatedAt time.Time
}

// DNSLoadBalancer DNS负载均衡器
type DNSLoadBalancer struct {
	strategy string
	mutex    sync.RWMutex
	counters map[string]int64
}

// DNSFailover DNS故障转移
type DNSFailover struct {
	primaryServers   []string
	secondaryServers []string
	healthCheck      bool
	mutex            sync.RWMutex
	serverStatus     map[string]bool
}

// DNSQueryResult DNS查询结果
type DNSQueryResult struct {
	TotalQueries      int64
	SuccessfulQueries int64
	FailedQueries     int64
	AvgResponseTime   time.Duration
	TotalTime         time.Duration
}

// DNSCacheResult DNS缓存结果
type DNSCacheResult struct {
	CacheHits   int64
	CacheMisses int64
	HitRate     float64
	TotalTime   time.Duration
}

// DNSPerformanceResult DNS性能结果
type DNSPerformanceResult struct {
	QueriesPerSecond  float64
	AvgResponseTime   time.Duration
	MinResponseTime   time.Duration
	MaxResponseTime   time.Duration
	TotalQueries      int64
}

// DNSConcurrentResult DNS并发结果
type DNSConcurrentResult struct {
	ConcurrentQueries int64
	SuccessfulQueries int64
	FailedQueries     int64
	SuccessRate       float64
	TotalTime         time.Duration
}

// createRealDNSResolver 创建真实的DNS解析器
func (s *DNSServiceIntegrationTestSuite) createRealDNSResolver(testName string) *RealDNSResolver {
	s.AddLog(testName, "🌐 创建真实DNS解析器")

	resolver := &RealDNSResolver{
		resolvers: []string{
			"*******:53",
			"8.8.4.4:53",
			"*******:53",
			"1.0.0.1:53",
		},
		timeout: 5 * time.Second,
		retries: 3,
	}

	return resolver
}

// createDNSCache 创建DNS缓存
func (s *DNSServiceIntegrationTestSuite) createDNSCache(testName string) *DNSCache {
	s.AddLog(testName, "💾 创建DNS缓存")

	cache := &DNSCache{
		cache:      make(map[string]*DNSCacheEntry),
		maxSize:    1000,
		defaultTTL: 300 * time.Second, // 5分钟
	}

	return cache
}

// createDNSLoadBalancer 创建DNS负载均衡器
func (s *DNSServiceIntegrationTestSuite) createDNSLoadBalancer(testName string) *DNSLoadBalancer {
	s.AddLog(testName, "⚖️ 创建DNS负载均衡器")

	lb := &DNSLoadBalancer{
		strategy: "round_robin",
		counters: make(map[string]int64),
	}

	return lb
}

// createDNSFailover 创建DNS故障转移
func (s *DNSServiceIntegrationTestSuite) createDNSFailover(testName string) *DNSFailover {
	s.AddLog(testName, "🔄 创建DNS故障转移")

	failover := &DNSFailover{
		primaryServers: []string{
			"*******:53",
			"*******:53",
		},
		secondaryServers: []string{
			"8.8.4.4:53",
			"1.0.0.1:53",
		},
		healthCheck:  true,
		serverStatus: make(map[string]bool),
	}

	// 初始化服务器状态
	for _, server := range append(failover.primaryServers, failover.secondaryServers...) {
		failover.serverStatus[server] = true
	}

	return failover
}

// performRealDNSQueries 执行真实的DNS查询
func (s *DNSServiceIntegrationTestSuite) performRealDNSQueries(testName string, domains []string) DNSQueryResult {
	start := time.Now()

	var totalQueries, successfulQueries, failedQueries int64
	var totalResponseTime time.Duration

	s.AddLog(testName, fmt.Sprintf("  开始查询 %d 个域名", len(domains)))

	for _, domain := range domains {
		queryStart := time.Now()

		// 执行真实的DNS查询
		ips, err := net.LookupHost(domain)
		queryTime := time.Since(queryStart)
		totalResponseTime += queryTime
		totalQueries++

		if err != nil {
			failedQueries++
			s.AddLog(testName, fmt.Sprintf("    ❌ DNS查询失败: %s, 错误: %v", domain, err))
		} else {
			successfulQueries++
			atomic.AddInt64(&s.realDNSResolver.successCount, 1)
			s.AddLog(testName, fmt.Sprintf("    ✅ DNS查询成功: %s -> %d个IP (%v)",
				domain, len(ips), queryTime))

			// 将结果存储到缓存
			s.dnsCache.Set(domain, ips, s.dnsCache.defaultTTL)
		}

		atomic.AddInt64(&s.realDNSResolver.queryCount, 1)
	}

	avgResponseTime := time.Duration(0)
	if totalQueries > 0 {
		avgResponseTime = totalResponseTime / time.Duration(totalQueries)
	}

	totalTime := time.Since(start)

	s.AddLog(testName, fmt.Sprintf("  DNS查询完成: 总数=%d, 成功=%d, 失败=%d, 平均响应时间=%v",
		totalQueries, successfulQueries, failedQueries, avgResponseTime))

	return DNSQueryResult{
		TotalQueries:      totalQueries,
		SuccessfulQueries: successfulQueries,
		FailedQueries:     failedQueries,
		AvgResponseTime:   avgResponseTime,
		TotalTime:         totalTime,
	}
}

// Set 设置DNS缓存
func (c *DNSCache) Set(domain string, ips []string, ttl time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 如果缓存已满，删除最旧的条目
	if len(c.cache) >= c.maxSize {
		var oldestDomain string
		var oldestTime time.Time
		for domain, entry := range c.cache {
			if oldestDomain == "" || entry.CreatedAt.Before(oldestTime) {
				oldestDomain = domain
				oldestTime = entry.CreatedAt
			}
		}
		if oldestDomain != "" {
			delete(c.cache, oldestDomain)
		}
	}

	c.cache[domain] = &DNSCacheEntry{
		IPs:       ips,
		ExpiresAt: time.Now().Add(ttl),
		CreatedAt: time.Now(),
	}
}

// Get 获取DNS缓存
func (c *DNSCache) Get(domain string) ([]string, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, exists := c.cache[domain]
	if !exists {
		atomic.AddInt64(&c.misses, 1)
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(entry.ExpiresAt) {
		// 异步删除过期条目
		go func() {
			c.mutex.Lock()
			delete(c.cache, domain)
			c.mutex.Unlock()
		}()
		atomic.AddInt64(&c.misses, 1)
		return nil, false
	}

	atomic.AddInt64(&c.hits, 1)
	return entry.IPs, true
}

// performDNSCacheTest 执行DNS缓存测试
func (s *DNSServiceIntegrationTestSuite) performDNSCacheTest(testName string, domains []string) DNSCacheResult {
	start := time.Now()

	initialHits := atomic.LoadInt64(&s.dnsCache.hits)
	initialMisses := atomic.LoadInt64(&s.dnsCache.misses)

	s.AddLog(testName, "  开始DNS缓存测试")

	// 第一轮查询（缓存未命中）
	for _, domain := range domains {
		if _, found := s.dnsCache.Get(domain); found {
			s.AddLog(testName, fmt.Sprintf("    💾 缓存命中: %s", domain))
		} else {
			s.AddLog(testName, fmt.Sprintf("    💾 缓存未命中: %s", domain))
		}
	}

	// 第二轮查询（应该有缓存命中）
	s.AddLog(testName, "  执行第二轮缓存查询")
	for _, domain := range domains {
		if ips, found := s.dnsCache.Get(domain); found {
			s.AddLog(testName, fmt.Sprintf("    ✅ 缓存命中: %s -> %d个IP", domain, len(ips)))
		} else {
			s.AddLog(testName, fmt.Sprintf("    ❌ 缓存未命中: %s", domain))
		}
	}

	finalHits := atomic.LoadInt64(&s.dnsCache.hits)
	finalMisses := atomic.LoadInt64(&s.dnsCache.misses)

	cacheHits := finalHits - initialHits
	cacheMisses := finalMisses - initialMisses

	hitRate := float64(0)
	if cacheHits+cacheMisses > 0 {
		hitRate = float64(cacheHits) / float64(cacheHits+cacheMisses)
	}

	totalTime := time.Since(start)

	s.AddLog(testName, fmt.Sprintf("  DNS缓存测试完成: 命中=%d, 未命中=%d, 命中率=%.1f%%",
		cacheHits, cacheMisses, hitRate*100))

	return DNSCacheResult{
		CacheHits:   cacheHits,
		CacheMisses: cacheMisses,
		HitRate:     hitRate,
		TotalTime:   totalTime,
	}
}

// performDNSPerformanceTest 执行DNS性能基准测试
func (s *DNSServiceIntegrationTestSuite) performDNSPerformanceTest(testName string, domains []string) DNSPerformanceResult {
	start := time.Now()

	const benchmarkRounds = 50 // 减少轮数以提高测试速度
	var totalQueries int64
	var minResponseTime, maxResponseTime, totalResponseTime time.Duration
	minResponseTime = time.Hour // 初始化为很大的值

	s.AddLog(testName, fmt.Sprintf("  开始DNS性能基准测试: %d轮", benchmarkRounds))

	for round := 0; round < benchmarkRounds; round++ {
		for _, domain := range domains {
			queryStart := time.Now()

			// 先检查缓存
			if _, found := s.dnsCache.Get(domain); !found {
				// 缓存未命中，执行真实查询
				_, err := net.LookupHost(domain)
				if err == nil {
					totalQueries++
				}
			} else {
				totalQueries++
			}

			queryTime := time.Since(queryStart)
			totalResponseTime += queryTime

			if queryTime < minResponseTime {
				minResponseTime = queryTime
			}
			if queryTime > maxResponseTime {
				maxResponseTime = queryTime
			}
		}

		if round%10 == 0 {
			s.AddLog(testName, fmt.Sprintf("    完成第 %d 轮性能测试", round+1))
		}
	}

	totalTime := time.Since(start)
	avgResponseTime := time.Duration(0)
	qps := float64(0)

	if totalQueries > 0 {
		avgResponseTime = totalResponseTime / time.Duration(totalQueries)
		qps = float64(totalQueries) / totalTime.Seconds()
	}

	s.AddLog(testName, fmt.Sprintf("  DNS性能测试完成: QPS=%.1f, 平均响应时间=%v, 最小=%v, 最大=%v",
		qps, avgResponseTime, minResponseTime, maxResponseTime))

	return DNSPerformanceResult{
		QueriesPerSecond: qps,
		AvgResponseTime:  avgResponseTime,
		MinResponseTime:  minResponseTime,
		MaxResponseTime:  maxResponseTime,
		TotalQueries:     totalQueries,
	}
}

// performConcurrentDNSTest 执行DNS并发查询测试
func (s *DNSServiceIntegrationTestSuite) performConcurrentDNSTest(testName string, domains []string) DNSConcurrentResult {
	start := time.Now()

	const concurrentWorkers = 10
	const queriesPerWorker = 5

	var wg sync.WaitGroup
	var totalQueries, successfulQueries, failedQueries int64

	s.AddLog(testName, fmt.Sprintf("  开始DNS并发测试: %d个工作协程, 每个%d次查询",
		concurrentWorkers, queriesPerWorker))

	for worker := 0; worker < concurrentWorkers; worker++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for query := 0; query < queriesPerWorker; query++ {
				domain := domains[query%len(domains)]

				// 执行DNS查询
				_, err := net.LookupHost(domain)
				atomic.AddInt64(&totalQueries, 1)

				if err != nil {
					atomic.AddInt64(&failedQueries, 1)
				} else {
					atomic.AddInt64(&successfulQueries, 1)
				}
			}

			s.AddLog(testName, fmt.Sprintf("    工作协程 %d 完成", workerID))
		}(worker)
	}

	wg.Wait()

	totalTime := time.Since(start)
	successRate := float64(0)
	if totalQueries > 0 {
		successRate = float64(successfulQueries) / float64(totalQueries)
	}

	s.AddLog(testName, fmt.Sprintf("  DNS并发测试完成: 总查询=%d, 成功=%d, 失败=%d, 成功率=%.1f%%",
		totalQueries, successfulQueries, failedQueries, successRate*100))

	return DNSConcurrentResult{
		ConcurrentQueries: totalQueries,
		SuccessfulQueries: successfulQueries,
		FailedQueries:     failedQueries,
		SuccessRate:       successRate,
		TotalTime:         totalTime,
	}
}

// TestDNSServiceIntegration 运行 DNS Service 集成测试
func TestDNSServiceIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(DNSServiceIntegrationTestSuite))
}
