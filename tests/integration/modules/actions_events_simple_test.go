package modules

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestMockActionManager 测试Mock动作管理器
func TestMockActionManager(t *testing.T) {
	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	// 测试日志动作
	t.Run("TestLogAction", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"level":   "info",
			"message": "测试日志消息",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
		assert.NoError(t, err, "日志动作应该执行成功")

		// 检查执行结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 1, "应该有一个执行结果")
		assert.True(t, results[0].Success, "动作应该执行成功")

		// 检查日志记录
		logs := logService.GetLogs()
		assert.Len(t, logs, 1, "应该有一条日志记录")
		assert.Equal(t, "info", logs[0].Level, "日志级别应该正确")
		assert.Equal(t, "测试日志消息", logs[0].Message, "日志消息应该正确")
	})

	// 测试IP封禁动作
	t.Run("TestBanIPAction", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"ip":       "*************",
			"duration": 3600,
			"reason":   "测试封禁",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeBanIP), params)
		assert.NoError(t, err, "IP封禁动作应该执行成功")

		// 检查执行结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 2, "应该有两个执行结果") // 包括之前的日志动作

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "动作应该执行成功")
		assert.Contains(t, lastResult.Metadata, "ip", "元数据应该包含IP")
		assert.Equal(t, "*************", lastResult.Metadata["ip"], "IP应该正确")
	})

	// 测试无效动作类型
	t.Run("TestInvalidActionType", func(t *testing.T) {
		ctx := context.Background()
		err := actionManager.ExecuteAction(ctx, "invalid_action", map[string]interface{}{})
		assert.Error(t, err, "无效动作类型应该返回错误")
		assert.Contains(t, err.Error(), "未知的动作类型", "错误消息应该正确")
	})
}

// TestMockTriggers 测试Mock触发器
func TestMockTriggers(t *testing.T) {
	// 测试状态码触发器
	t.Run("TestStatusTrigger", func(t *testing.T) {
		trigger := &MockStatusTrigger{
			Codes:        []int{500, 502, 503},
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "服务器错误",
					},
				},
			},
		}

		// 创建测试请求和响应
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		resp := &http.Response{StatusCode: 500}

		// 测试匹配
		matched := trigger.Match(req, resp, 100*time.Millisecond)
		assert.True(t, matched, "状态码500应该匹配触发器")

		// 测试不匹配
		resp.StatusCode = 200
		matched = trigger.Match(req, resp, 100*time.Millisecond)
		assert.False(t, matched, "状态码200不应该匹配触发器")

		// 测试获取动作
		actions := trigger.GetActions()
		assert.Len(t, actions, 1, "应该有一个动作")
		assert.Equal(t, ActionTypeLog, actions[0].Type, "动作类型应该正确")
	})

	// 测试URL触发器
	t.Run("TestURLTrigger", func(t *testing.T) {
		trigger := &MockURLTrigger{
			Pattern:      "/api/",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "API访问",
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "包含'/api/'的URL应该匹配")

		// 测试不匹配
		req, _ = http.NewRequest("GET", "http://example.com/home", nil)
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "不包含'/api/'的URL不应该匹配")
	})

	// 测试域名触发器
	t.Run("TestDomainTrigger", func(t *testing.T) {
		trigger := &MockDomainTrigger{
			Pattern:      "malicious-site.com",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeBanDomain,
					Params: map[string]interface{}{
						"domain":   "malicious-site.com",
						"duration": 86400,
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://malicious-site.com/attack", nil)
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "恶意域名应该匹配")

		// 测试不匹配
		req, _ = http.NewRequest("GET", "http://example.com/safe", nil)
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "安全域名不应该匹配")
	})

	// 测试请求头触发器
	t.Run("TestRequestHeaderTrigger", func(t *testing.T) {
		trigger := &MockRequestHeaderTrigger{
			HeaderName:   "User-Agent",
			Pattern:      ".*Bot.*",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeBanIP,
					Params: map[string]interface{}{
						"reason": "Bot检测",
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set("User-Agent", "AttackBot/1.0")
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "包含'Bot'的User-Agent应该匹配")

		// 测试不匹配
		req.Header.Set("User-Agent", "Mozilla/5.0")
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "正常的User-Agent不应该匹配")
	})
}

// TestMockTriggerManager 测试Mock触发器管理器
func TestMockTriggerManager(t *testing.T) {
	manager := NewMockTriggerManager()

	// 添加状态码触发器
	statusTrigger := &MockStatusTrigger{
		Codes:        []int{500},
		Priority:     1,
		ProcessStage: ProcessStagePostBody,
		Actions: []ActionConfig{
			{
				Type: ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "error",
					"message": "服务器错误",
				},
			},
		},
	}
	manager.AddTrigger(statusTrigger)

	// 添加URL触发器
	urlTrigger := &MockURLTrigger{
		Pattern:      "/api/",
		Priority:     2,
		ProcessStage: ProcessStagePreRequest,
		Actions: []ActionConfig{
			{
				Type: ActionTypeModifyRequest,
				Params: map[string]interface{}{
					"headers": map[string]interface{}{
						"add": map[string]string{
							"X-API-Request": "true",
						},
					},
				},
			},
		},
	}
	manager.AddTrigger(urlTrigger)

	// 测试处理触发器
	t.Run("TestProcessTriggers", func(t *testing.T) {
		// 测试PreRequest阶段
		req, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
		actions := manager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		assert.Len(t, actions, 1, "PreRequest阶段应该有一个触发的动作")
		assert.Equal(t, ActionTypeModifyRequest, actions[0].Type, "应该是修改请求动作")

		// 测试PostBody阶段
		resp := &http.Response{StatusCode: 500}
		actions = manager.ProcessTriggers(ProcessStagePostBody, req, resp, 100*time.Millisecond)
		assert.Len(t, actions, 1, "PostBody阶段应该有一个触发的动作")
		assert.Equal(t, ActionTypeLog, actions[0].Type, "应该是日志动作")

		// 测试无匹配的阶段
		actions = manager.ProcessTriggers(ProcessStagePostHeader, req, resp, 100*time.Millisecond)
		assert.Len(t, actions, 0, "PostHeader阶段不应该有触发的动作")
	})
}

// TestMockLogService 测试Mock日志服务
func TestMockLogService(t *testing.T) {
	logService := NewMockLogService()

	// 测试日志记录
	t.Run("TestLogging", func(t *testing.T) {
		logService.Log("info", "测试信息日志")
		logService.Log("error", "测试错误日志")
		logService.Log("warn", "测试警告日志")

		logs := logService.GetLogs()
		assert.Len(t, logs, 3, "应该有3条日志")

		// 检查日志内容
		assert.Equal(t, "info", logs[0].Level, "第一条日志级别应该正确")
		assert.Equal(t, "测试信息日志", logs[0].Message, "第一条日志消息应该正确")

		assert.Equal(t, "error", logs[1].Level, "第二条日志级别应该正确")
		assert.Equal(t, "测试错误日志", logs[1].Message, "第二条日志消息应该正确")
	})

	// 测试按级别获取日志
	t.Run("TestGetLogsByLevel", func(t *testing.T) {
		errorLogs := logService.GetLogsByLevel("error")
		assert.Len(t, errorLogs, 1, "应该有1条错误日志")
		assert.Equal(t, "测试错误日志", errorLogs[0].Message, "错误日志消息应该正确")

		infoLogs := logService.GetLogsByLevel("info")
		assert.Len(t, infoLogs, 1, "应该有1条信息日志")

		debugLogs := logService.GetLogsByLevel("debug")
		assert.Len(t, debugLogs, 0, "不应该有调试日志")
	})

	// 测试清空日志
	t.Run("TestClearLogs", func(t *testing.T) {
		assert.Equal(t, 3, logService.GetLogCount(), "清空前应该有3条日志")

		logService.ClearLogs()
		assert.Equal(t, 0, logService.GetLogCount(), "清空后应该没有日志")

		logs := logService.GetLogs()
		assert.Len(t, logs, 0, "清空后日志列表应该为空")
	})
}

// TestActionTypes 测试动作类型常量
func TestActionTypes(t *testing.T) {
	// 测试基础动作类型
	basicActions := []ActionType{
		ActionTypeLog,
		ActionTypeBanIP,
		ActionTypeBanDomain,
		ActionTypeBlockRequest,
		ActionTypeModifyRequest,
		ActionTypeModifyResponse,
		ActionTypeCacheResponse,
		ActionTypeScript,
	}

	for _, actionType := range basicActions {
		assert.NotEmpty(t, string(actionType), "动作类型不应该为空")
		assert.True(t, len(string(actionType)) > 2, "动作类型应该有意义")
	}

	// 测试扩展动作类型
	extendedActions := []ActionType{
		ActionTypeRetry,
		ActionTypeRetrySame,
		ActionTypeSaveToPool,
		ActionTypeCache,
		ActionTypeRequestURL,
		ActionTypeBanIPDomain,
		ActionTypeNullResponse,
		ActionTypeBypassProxy,
	}

	for _, actionType := range extendedActions {
		assert.NotEmpty(t, string(actionType), "扩展动作类型不应该为空")
		assert.True(t, len(string(actionType)) > 2, "扩展动作类型应该有意义")
	}
}

// TestTriggerTypes 测试触发器类型常量
func TestTriggerTypes(t *testing.T) {
	triggerTypes := []TriggerType{
		TriggerTypeStatus,
		TriggerTypeBody,
		TriggerTypeMaxRequestTime,
		TriggerTypeConnTimeOut,
		TriggerTypeMinRequestTime,
		TriggerTypeURL,
		TriggerTypeDomain,
		TriggerTypeCombined,
		TriggerTypeCustom,
		TriggerTypeRequestBody,
		TriggerTypeRequestHeader,
		TriggerTypeResponseHeader,
	}

	for _, triggerType := range triggerTypes {
		assert.NotEmpty(t, string(triggerType), "触发器类型不应该为空")
		assert.True(t, len(string(triggerType)) > 2, "触发器类型应该有意义")
	}
}

// TestProcessStages 测试处理阶段常量
func TestProcessStages(t *testing.T) {
	stages := []ProcessStage{
		ProcessStagePreRequest,
		ProcessStagePostHeader,
		ProcessStagePostBody,
		ProcessStagePreResponse,
	}

	for _, stage := range stages {
		assert.NotEmpty(t, string(stage), "处理阶段不应该为空")
		assert.True(t, strings.Contains(string(stage), "_"), "处理阶段应该包含下划线")
	}
}

// TestExtendedActionExecution 测试扩展动作执行
func TestExtendedActionExecution(t *testing.T) {
	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	// 测试重试动作
	t.Run("TestRetryAction", func(t *testing.T) {
		t.Log("开始测试重试动作")
		ctx := context.Background()
		params := map[string]interface{}{
			"max_retries": 3,
			"delay":       "1s",
			"reason":      "服务器错误重试",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeRetry), params)
		assert.NoError(t, err, "重试动作应该执行成功")

		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 1, "应该有一个执行结果")

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "重试动作应该执行成功")
		assert.Equal(t, 3, lastResult.Metadata["max_retries"], "最大重试次数应该正确")
		assert.Equal(t, "1s", lastResult.Metadata["delay"], "重试延迟应该正确")
		assert.True(t, lastResult.Metadata["retry_with_new_proxy"].(bool), "应该使用新代理重试")

		t.Logf("重试动作执行成功，元数据: %+v", lastResult.Metadata)
	})

	// 测试缓存动作
	t.Run("TestCacheAction", func(t *testing.T) {
		t.Log("开始测试缓存动作")
		ctx := context.Background()
		params := map[string]interface{}{
			"key":       "test-cache-key",
			"value":     "test-cache-value",
			"ttl":       3600,
			"namespace": "test",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeCache), params)
		assert.NoError(t, err, "缓存动作应该执行成功")

		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 2, "应该有两个执行结果")

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "缓存动作应该执行成功")
		assert.Equal(t, "test-cache-key", lastResult.Metadata["key"], "缓存键应该正确")
		assert.Equal(t, "test-cache-value", lastResult.Metadata["value"], "缓存值应该正确")
		assert.True(t, lastResult.Metadata["cached"].(bool), "应该标记为已缓存")

		t.Logf("缓存动作执行成功，元数据: %+v", lastResult.Metadata)
	})

	// 测试脚本执行动作
	t.Run("TestScriptAction", func(t *testing.T) {
		t.Log("开始测试脚本执行动作")
		ctx := context.Background()
		params := map[string]interface{}{
			"language": "javascript",
			"code":     "console.log('Hello from FlexProxy script');",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeScript), params)
		assert.NoError(t, err, "脚本执行动作应该执行成功")

		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 3, "应该有三个执行结果")

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "脚本执行动作应该执行成功")
		assert.Equal(t, "javascript", lastResult.Metadata["language"], "脚本语言应该正确")
		assert.True(t, lastResult.Metadata["executed"].(bool), "应该标记为已执行")

		t.Logf("脚本执行动作成功，执行时间: %dms", lastResult.Duration)
	})

	// 测试URL请求动作
	t.Run("TestRequestURLAction", func(t *testing.T) {
		t.Log("开始测试URL请求动作")
		ctx := context.Background()
		params := map[string]interface{}{
			"url":     "http://example.com/api/test",
			"method":  "POST",
			"timeout": "30s",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeRequestURL), params)
		assert.NoError(t, err, "URL请求动作应该执行成功")

		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 4, "应该有四个执行结果")

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "URL请求动作应该执行成功")
		assert.Equal(t, "http://example.com/api/test", lastResult.Metadata["url"], "请求URL应该正确")
		assert.Equal(t, "POST", lastResult.Metadata["method"], "请求方法应该正确")
		assert.True(t, lastResult.Metadata["requested"].(bool), "应该标记为已请求")

		t.Logf("URL请求动作成功，请求URL: %s", lastResult.Metadata["url"])
	})
}

// TestAdvancedTriggerScenarios 测试高级触发器场景
func TestAdvancedTriggerScenarios(t *testing.T) {
	manager := NewMockTriggerManager()

	// 测试最大请求时间触发器
	t.Run("TestMaxRequestTimeTrigger", func(t *testing.T) {
		t.Log("开始测试最大请求时间触发器")

		trigger := &MockMaxRequestTimeTrigger{
			MaxTime:      5000, // 5秒
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "warn",
						"message": "请求时间过长",
					},
				},
				{
					Type: ActionTypeRetry,
					Params: map[string]interface{}{
						"max_retries": 2,
						"delay":       "500ms",
					},
				},
			},
		}
		manager.AddTrigger(trigger)

		// 测试超时情况
		req, _ := http.NewRequest("GET", "http://slow-server.com/api", nil)
		resp := &http.Response{StatusCode: 200}
		requestTime := 6 * time.Second // 超过5秒限制

		matched := trigger.Match(req, resp, requestTime)
		assert.True(t, matched, "超过最大请求时间应该匹配触发器")

		actions := trigger.GetActions()
		assert.Len(t, actions, 2, "应该有两个动作")
		assert.Equal(t, ActionTypeLog, actions[0].Type, "第一个动作应该是日志")
		assert.Equal(t, ActionTypeRetry, actions[1].Type, "第二个动作应该是重试")

		t.Logf("最大请求时间触发器匹配成功，请求时间: %v", requestTime)

		// 测试正常情况
		requestTime = 3 * time.Second // 未超过5秒限制
		matched = trigger.Match(req, resp, requestTime)
		assert.False(t, matched, "未超过最大请求时间不应该匹配触发器")

		t.Logf("正常请求时间不匹配触发器，请求时间: %v", requestTime)
	})

	// 测试响应体触发器
	t.Run("TestBodyTrigger", func(t *testing.T) {
		t.Log("开始测试响应体触发器")

		trigger := &MockBodyTrigger{
			Pattern:      "error",
			Priority:     2,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "响应体包含错误信息",
					},
				},
				{
					Type: ActionTypeModifyResponse,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Error-Detected": "true",
							},
						},
					},
				},
			},
		}
		manager.AddTrigger(trigger)

		req, _ := http.NewRequest("GET", "http://api.example.com/data", nil)
		resp := &http.Response{
			StatusCode: 200,
			Body:       http.NoBody, // 添加Body以避免nil检查失败
		}

		// 简化的匹配逻辑测试 - 我们的Mock实现检查pattern是否包含"error"
		matched := trigger.Match(req, resp, 100*time.Millisecond)
		assert.True(t, matched, "包含错误模式的响应体应该匹配") // 基于我们的简化实现

		actions := trigger.GetActions()
		assert.Len(t, actions, 2, "应该有两个动作")

		t.Logf("响应体触发器测试完成，匹配结果: %v", matched)
	})
}

// TestConcurrentActionExecution 测试并发动作执行
func TestConcurrentActionExecution(t *testing.T) {
	t.Log("开始测试并发动作执行")

	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	const numGoroutines = 10
	const actionsPerGoroutine = 5

	// 并发执行动作
	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer func() { done <- true }()

			ctx := context.Background()
			for j := 0; j < actionsPerGoroutine; j++ {
				params := map[string]interface{}{
					"level":   "info",
					"message": fmt.Sprintf("并发测试 goroutine-%d action-%d", goroutineID, j),
				}

				err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
				if err != nil {
					t.Errorf("Goroutine %d, Action %d 执行失败: %v", goroutineID, j, err)
				}
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// 验证结果
	results := actionManager.GetExecutedActions()
	expectedCount := numGoroutines * actionsPerGoroutine
	assert.Equal(t, expectedCount, len(results), "应该有正确数量的执行结果")

	// 验证所有动作都成功执行
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	assert.Equal(t, expectedCount, successCount, "所有动作都应该执行成功")

	// 验证日志记录
	logs := logService.GetLogs()
	assert.Equal(t, expectedCount, len(logs), "应该有正确数量的日志记录")

	t.Logf("并发测试完成: %d个goroutine, 每个执行%d个动作, 总共%d个动作全部成功",
		numGoroutines, actionsPerGoroutine, expectedCount)
}

// TestRealWorldScenarios 测试真实世界场景
func TestRealWorldScenarios(t *testing.T) {
	t.Log("开始测试真实世界场景")

	// 场景1: API网关场景
	t.Run("APIGatewayScenario", func(t *testing.T) {
		t.Log("测试API网关场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加API请求触发器
		apiTrigger := &MockURLTrigger{
			Pattern:      "/api/",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "API请求检测",
					},
				},
				{
					Type: ActionTypeModifyRequest,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-API-Gateway": "FlexProxy",
								"X-Request-ID":  "req-12345",
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(apiTrigger)

		// 模拟API请求
		req, _ := http.NewRequest("POST", "http://api.example.com/api/users", strings.NewReader(`{"name":"test"}`))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer token123")

		// 处理PreRequest阶段
		actions := triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		assert.Len(t, actions, 2, "API请求应该触发2个动作")

		// 执行触发的动作
		ctx := context.Background()
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "动作执行应该成功")
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 2, "应该有2个执行结果")

		logs := logService.GetLogs()
		assert.Len(t, logs, 1, "应该有1条日志记录")
		assert.Equal(t, "API请求检测", logs[0].Message, "日志消息应该正确")

		t.Logf("API网关场景测试完成，处理了%d个动作", len(results))
	})

	// 场景2: 安全防护场景
	t.Run("SecurityProtectionScenario", func(t *testing.T) {
		t.Log("测试安全防护场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加恶意User-Agent检测触发器
		securityTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "User-Agent",
			Pattern:      ".*[Bb]ot.*",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "warn",
						"message": "检测到可疑User-Agent",
					},
				},
				{
					Type: ActionTypeBanIP,
					Params: map[string]interface{}{
						"ip":       "*************",
						"duration": 3600,
						"reason":   "可疑User-Agent",
					},
				},
				{
					Type: ActionTypeBlockRequest,
					Params: map[string]interface{}{
						"status_code": 403,
						"reason":      "安全防护阻止",
					},
				},
			},
		}
		triggerManager.AddTrigger(securityTrigger)

		// 模拟恶意请求
		req, _ := http.NewRequest("GET", "http://example.com/admin", nil)
		req.Header.Set("User-Agent", "AttackBot/1.0")
		req.RemoteAddr = "*************:12345"

		// 处理PreRequest阶段
		actions := triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		assert.Len(t, actions, 3, "恶意请求应该触发3个安全动作")

		// 执行安全动作
		ctx := context.Background()
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "安全动作执行应该成功")
		}

		// 验证安全防护结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 3, "应该有3个安全动作执行结果")

		// 检查IP封禁动作
		banResult := results[1] // 第二个是IP封禁动作
		assert.True(t, banResult.Success, "IP封禁动作应该成功")
		assert.Equal(t, "*************", banResult.Metadata["ip"], "封禁IP应该正确")

		logs := logService.GetLogs()
		assert.Len(t, logs, 1, "应该有1条安全警告日志")
		assert.Equal(t, "warn", logs[0].Level, "日志级别应该是警告")

		t.Logf("安全防护场景测试完成，阻止了恶意请求并封禁IP: %s", banResult.Metadata["ip"])
	})

	// 场景3: 负载均衡和错误恢复场景
	t.Run("LoadBalancingScenario", func(t *testing.T) {
		t.Log("测试负载均衡和错误恢复场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加服务器错误触发器
		errorTrigger := &MockStatusTrigger{
			Codes:        []int{500, 502, 503, 504},
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "服务器错误，启动负载均衡",
					},
				},
				{
					Type: ActionTypeRetry,
					Params: map[string]interface{}{
						"max_retries": 3,
						"delay":       "1s",
						"reason":      "服务器错误重试",
					},
				},
				{
					Type: ActionTypeCacheResponse,
					Params: map[string]interface{}{
						"ttl":     300,
						"key":     "fallback_response",
						"enabled": true,
					},
				},
			},
		}
		triggerManager.AddTrigger(errorTrigger)

		// 模拟服务器错误响应
		req, _ := http.NewRequest("GET", "http://backend-server.com/api/data", nil)
		resp := &http.Response{
			StatusCode: 503,
			Header:     make(http.Header),
		}
		resp.Header.Set("Content-Type", "application/json")

		// 处理PostBody阶段
		actions := triggerManager.ProcessTriggers(ProcessStagePostBody, req, resp, 2*time.Second)
		assert.Len(t, actions, 3, "服务器错误应该触发3个恢复动作")

		// 执行恢复动作
		ctx := context.Background()
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "恢复动作执行应该成功")
		}

		// 验证负载均衡结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 3, "应该有3个恢复动作执行结果")

		// 检查重试动作
		retryResult := results[1] // 第二个是重试动作
		assert.True(t, retryResult.Success, "重试动作应该成功")
		assert.Equal(t, 3, retryResult.Metadata["max_retries"], "最大重试次数应该正确")
		assert.True(t, retryResult.Metadata["retry_with_new_proxy"].(bool), "应该使用新代理重试")

		// 检查缓存动作
		cacheResult := results[2] // 第三个是缓存动作
		assert.True(t, cacheResult.Success, "缓存动作应该成功")
		assert.Equal(t, "fallback_response", cacheResult.Metadata["cache_key"], "缓存键应该正确")

		logs := logService.GetLogs()
		assert.Len(t, logs, 1, "应该有1条错误日志")
		assert.Equal(t, "error", logs[0].Level, "日志级别应该是错误")

		t.Logf("负载均衡场景测试完成，处理了服务器错误并启动了%d次重试",
			retryResult.Metadata["max_retries"])
	})
}

// TestErrorHandlingScenarios 测试错误处理场景
func TestErrorHandlingScenarios(t *testing.T) {
	t.Log("开始测试错误处理场景")

	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	// 测试参数缺失错误
	t.Run("TestMissingParameters", func(t *testing.T) {
		t.Log("测试参数缺失错误处理")

		ctx := context.Background()

		// IP封禁动作缺少IP参数
		err := actionManager.ExecuteAction(ctx, string(ActionTypeBanIP), map[string]interface{}{
			"duration": 3600,
			"reason":   "测试",
		})
		assert.Error(t, err, "缺少IP参数应该返回错误")
		assert.Contains(t, err.Error(), "IP地址不能为空", "错误消息应该正确")

		// 域名封禁动作缺少域名参数
		err = actionManager.ExecuteAction(ctx, string(ActionTypeBanDomain), map[string]interface{}{
			"duration": 3600,
			"reason":   "测试",
		})
		assert.Error(t, err, "缺少域名参数应该返回错误")
		assert.Contains(t, err.Error(), "域名不能为空", "错误消息应该正确")

		// 脚本执行动作缺少代码参数
		err = actionManager.ExecuteAction(ctx, string(ActionTypeScript), map[string]interface{}{
			"language": "javascript",
		})
		assert.Error(t, err, "缺少脚本代码应该返回错误")
		assert.Contains(t, err.Error(), "脚本代码不能为空", "错误消息应该正确")

		t.Log("参数缺失错误处理测试完成")
	})

	// 测试无效参数错误
	t.Run("TestInvalidParameters", func(t *testing.T) {
		t.Log("测试无效参数错误处理")

		ctx := context.Background()

		// URL请求动作缺少URL参数
		err := actionManager.ExecuteAction(ctx, string(ActionTypeRequestURL), map[string]interface{}{
			"method":  "GET",
			"timeout": "10s",
		})
		assert.Error(t, err, "缺少URL参数应该返回错误")
		assert.Contains(t, err.Error(), "URL不能为空", "错误消息应该正确")

		// 缓存动作缺少键参数
		err = actionManager.ExecuteAction(ctx, string(ActionTypeCache), map[string]interface{}{
			"value": "test-value",
			"ttl":   3600,
		})
		assert.Error(t, err, "缺少缓存键应该返回错误")
		assert.Contains(t, err.Error(), "缓存键不能为空", "错误消息应该正确")

		t.Log("无效参数错误处理测试完成")
	})

	// 测试执行结果记录
	t.Run("TestExecutionResultRecording", func(t *testing.T) {
		t.Log("测试执行结果记录")

		// 创建新的ActionManager以避免之前测试的影响
		newLogService := NewMockLogService()
		newActionManager := NewMockActionManager(newLogService)

		ctx := context.Background()

		// 执行一个成功的动作
		err := newActionManager.ExecuteAction(ctx, string(ActionTypeLog), map[string]interface{}{
			"level":   "info",
			"message": "测试消息",
		})
		assert.NoError(t, err, "日志动作应该执行成功")

		// 执行一个失败的动作
		err = newActionManager.ExecuteAction(ctx, string(ActionTypeBanIP), map[string]interface{}{
			"duration": 3600,
			// 缺少IP参数
		})
		assert.Error(t, err, "缺少参数的动作应该失败")

		// 检查执行结果
		results := newActionManager.GetExecutedActions()
		assert.Len(t, results, 2, "应该有2个执行结果")

		// 检查成功的结果
		successResult := results[0]
		assert.True(t, successResult.Success, "第一个动作应该成功")
		assert.Empty(t, successResult.Error, "成功的动作不应该有错误")
		assert.GreaterOrEqual(t, successResult.Duration, int64(0), "执行时间应该大于等于0")

		// 检查失败的结果
		failResult := results[1]
		assert.False(t, failResult.Success, "第二个动作应该失败")
		assert.NotEmpty(t, failResult.Error, "失败的动作应该有错误信息")
		assert.Contains(t, failResult.Error, "IP地址不能为空", "错误信息应该正确")

		t.Logf("执行结果记录测试完成，成功: %v, 失败: %v",
			successResult.Success, failResult.Success)
	})
}

// TestPerformanceMetrics 测试性能指标
func TestPerformanceMetrics(t *testing.T) {
	t.Log("开始测试性能指标")

	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	// 测试动作执行性能
	t.Run("TestActionExecutionPerformance", func(t *testing.T) {
		t.Log("测试动作执行性能")

		ctx := context.Background()
		const numActions = 1000

		start := time.Now()

		// 执行大量动作
		for i := 0; i < numActions; i++ {
			params := map[string]interface{}{
				"level":   "info",
				"message": fmt.Sprintf("性能测试消息 %d", i),
			}

			err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
			assert.NoError(t, err, "动作执行应该成功")
		}

		duration := time.Since(start)

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, numActions, "应该有正确数量的执行结果")

		// 计算性能指标
		avgDuration := duration.Nanoseconds() / int64(numActions)
		throughput := float64(numActions) / duration.Seconds()

		t.Logf("性能测试结果:")
		t.Logf("  总动作数: %d", numActions)
		t.Logf("  总执行时间: %v", duration)
		t.Logf("  平均执行时间: %d ns", avgDuration)
		t.Logf("  吞吐量: %.2f actions/sec", throughput)

		// 性能断言
		assert.Less(t, avgDuration, int64(1000000), "平均执行时间应该小于1ms") // 1ms = 1,000,000 ns
		assert.Greater(t, throughput, 100.0, "吞吐量应该大于100 actions/sec")
	})

	// 测试触发器匹配性能
	t.Run("TestTriggerMatchingPerformance", func(t *testing.T) {
		t.Log("测试触发器匹配性能")

		manager := NewMockTriggerManager()

		// 添加多个触发器
		for i := 0; i < 10; i++ {
			trigger := &MockStatusTrigger{
				Codes:        []int{500 + i},
				Priority:     i,
				ProcessStage: ProcessStagePostBody,
				Actions: []ActionConfig{
					{
						Type: ActionTypeLog,
						Params: map[string]interface{}{
							"level":   "info",
							"message": fmt.Sprintf("触发器 %d", i),
						},
					},
				},
			}
			manager.AddTrigger(trigger)
		}

		// 创建测试请求和响应
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		resp := &http.Response{StatusCode: 505} // 匹配最后一个触发器

		const numMatches = 10000
		start := time.Now()

		// 执行大量匹配
		for i := 0; i < numMatches; i++ {
			actions := manager.ProcessTriggers(ProcessStagePostBody, req, resp, 100*time.Millisecond)
			assert.Len(t, actions, 1, "应该匹配一个触发器")
		}

		duration := time.Since(start)

		// 计算性能指标
		avgDuration := duration.Nanoseconds() / int64(numMatches)
		throughput := float64(numMatches) / duration.Seconds()

		t.Logf("触发器匹配性能测试结果:")
		t.Logf("  总匹配数: %d", numMatches)
		t.Logf("  总执行时间: %v", duration)
		t.Logf("  平均匹配时间: %d ns", avgDuration)
		t.Logf("  匹配吞吐量: %.2f matches/sec", throughput)

		// 性能断言
		assert.Less(t, avgDuration, int64(100000), "平均匹配时间应该小于0.1ms") // 0.1ms = 100,000 ns
		assert.Greater(t, throughput, 1000.0, "匹配吞吐量应该大于1000 matches/sec")
	})
}

// TestStressScenarios 测试压力场景
func TestStressScenarios(t *testing.T) {
	t.Log("开始测试压力场景")

	// 测试高并发动作执行
	t.Run("TestHighConcurrencyActions", func(t *testing.T) {
		t.Log("测试高并发动作执行")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		const numGoroutines = 50
		const actionsPerGoroutine = 20

		var results = make(chan error, numGoroutines*actionsPerGoroutine)
		start := time.Now()

		// 启动大量并发goroutine
		for i := 0; i < numGoroutines; i++ {
			go func(goroutineID int) {
				ctx := context.Background()
				for j := 0; j < actionsPerGoroutine; j++ {
					params := map[string]interface{}{
						"level":   "info",
						"message": fmt.Sprintf("压力测试 G%d-A%d", goroutineID, j),
					}

					err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
					results <- err
				}
			}(i)
		}

		// 收集所有结果
		successCount := 0
		errorCount := 0
		for i := 0; i < numGoroutines*actionsPerGoroutine; i++ {
			err := <-results
			if err == nil {
				successCount++
			} else {
				errorCount++
				t.Logf("动作执行错误: %v", err)
			}
		}

		duration := time.Since(start)

		// 验证结果
		totalActions := numGoroutines * actionsPerGoroutine
		assert.Equal(t, totalActions, successCount+errorCount, "总动作数应该正确")
		assert.Equal(t, totalActions, successCount, "所有动作都应该成功")
		assert.Equal(t, 0, errorCount, "不应该有错误")

		// 验证执行结果
		executedActions := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(executedActions), "执行结果数量应该正确")

		// 验证日志记录
		logs := logService.GetLogs()
		assert.Equal(t, totalActions, len(logs), "日志记录数量应该正确")

		// 计算性能指标
		throughput := float64(totalActions) / duration.Seconds()

		t.Logf("高并发压力测试结果:")
		t.Logf("  并发数: %d goroutines", numGoroutines)
		t.Logf("  每个goroutine动作数: %d", actionsPerGoroutine)
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  成功数: %d", successCount)
		t.Logf("  错误数: %d", errorCount)
		t.Logf("  总执行时间: %v", duration)
		t.Logf("  吞吐量: %.2f actions/sec", throughput)

		// 性能断言
		assert.Greater(t, throughput, 500.0, "高并发吞吐量应该大于500 actions/sec")
	})

	// 测试内存使用
	t.Run("TestMemoryUsage", func(t *testing.T) {
		t.Log("测试内存使用")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		// 记录初始内存
		var m1 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		// 执行大量动作
		ctx := context.Background()
		const numActions = 5000

		for i := 0; i < numActions; i++ {
			params := map[string]interface{}{
				"level":   "info",
				"message": fmt.Sprintf("内存测试消息 %d", i),
			}

			err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
			assert.NoError(t, err, "动作执行应该成功")
		}

		// 记录执行后内存
		var m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m2)

		// 计算内存使用
		allocDiff := m2.Alloc - m1.Alloc
		totalAllocDiff := m2.TotalAlloc - m1.TotalAlloc

		t.Logf("内存使用测试结果:")
		t.Logf("  执行动作数: %d", numActions)
		t.Logf("  当前分配内存差: %d bytes", allocDiff)
		t.Logf("  总分配内存差: %d bytes", totalAllocDiff)
		t.Logf("  平均每动作内存: %.2f bytes", float64(totalAllocDiff)/float64(numActions))

		// 内存使用断言
		avgMemoryPerAction := float64(totalAllocDiff) / float64(numActions)
		assert.Less(t, avgMemoryPerAction, 1000.0, "平均每动作内存使用应该小于1KB")

		// 清理并验证内存释放
		actionManager.ClearExecutedActions()
		logService.ClearLogs()

		var m3 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m3)

		t.Logf("清理后内存: %d bytes", m3.Alloc)
	})
}

// TestExtremeScenarios 测试极限场景
func TestExtremeScenarios(t *testing.T) {
	t.Log("开始测试极限场景")

	// 测试超大数据量处理
	t.Run("TestMassiveDataProcessing", func(t *testing.T) {
		t.Log("测试超大数据量处理")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		const massiveCount = 50000 // 5万个动作
		start := time.Now()

		ctx := context.Background()
		for i := 0; i < massiveCount; i++ {
			params := map[string]interface{}{
				"level":   "info",
				"message": fmt.Sprintf("大数据测试消息 %d", i),
			}

			err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
			assert.NoError(t, err, "大数据量处理应该成功")

			// 每1000个动作输出一次进度
			if (i+1)%1000 == 0 {
				t.Logf("已处理 %d/%d 动作", i+1, massiveCount)
			}
		}

		duration := time.Since(start)

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, massiveCount, "应该有正确数量的执行结果")

		logs := logService.GetLogs()
		assert.Len(t, logs, massiveCount, "应该有正确数量的日志记录")

		// 计算性能指标
		throughput := float64(massiveCount) / duration.Seconds()
		avgTime := duration.Nanoseconds() / int64(massiveCount)

		t.Logf("超大数据量处理结果:")
		t.Logf("  处理动作数: %d", massiveCount)
		t.Logf("  总处理时间: %v", duration)
		t.Logf("  平均处理时间: %d ns", avgTime)
		t.Logf("  处理吞吐量: %.2f actions/sec", throughput)

		// 性能断言
		assert.Greater(t, throughput, 10000.0, "大数据量处理吞吐量应该大于10K actions/sec")
		assert.Less(t, avgTime, int64(100000), "平均处理时间应该小于0.1ms")
	})

	// 测试极限并发场景
	t.Run("TestExtremeConcurrency", func(t *testing.T) {
		t.Log("测试极限并发场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		const extremeGoroutines = 200 // 200个并发goroutine
		const actionsPerGoroutine = 50

		var results = make(chan error, extremeGoroutines*actionsPerGoroutine)
		start := time.Now()

		// 启动极限并发
		for i := 0; i < extremeGoroutines; i++ {
			go func(goroutineID int) {
				ctx := context.Background()
				for j := 0; j < actionsPerGoroutine; j++ {
					params := map[string]interface{}{
						"level":   "info",
						"message": fmt.Sprintf("极限并发 G%d-A%d", goroutineID, j),
					}

					err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
					results <- err
				}
			}(i)
		}

		// 收集所有结果
		successCount := 0
		errorCount := 0
		totalActions := extremeGoroutines * actionsPerGoroutine

		for i := 0; i < totalActions; i++ {
			err := <-results
			if err == nil {
				successCount++
			} else {
				errorCount++
			}
		}

		duration := time.Since(start)

		// 验证结果
		assert.Equal(t, totalActions, successCount+errorCount, "总动作数应该正确")
		assert.Equal(t, totalActions, successCount, "所有动作都应该成功")
		assert.Equal(t, 0, errorCount, "不应该有错误")

		// 验证数据一致性
		executedActions := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(executedActions), "执行结果数量应该正确")

		logs := logService.GetLogs()
		assert.Equal(t, totalActions, len(logs), "日志记录数量应该正确")

		// 计算性能指标
		throughput := float64(totalActions) / duration.Seconds()

		t.Logf("极限并发测试结果:")
		t.Logf("  并发数: %d goroutines", extremeGoroutines)
		t.Logf("  每个goroutine动作数: %d", actionsPerGoroutine)
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  成功数: %d", successCount)
		t.Logf("  错误数: %d", errorCount)
		t.Logf("  总执行时间: %v", duration)
		t.Logf("  极限并发吞吐量: %.2f actions/sec", throughput)

		// 性能断言
		assert.Greater(t, throughput, 50000.0, "极限并发吞吐量应该大于50K actions/sec")
	})

	// 测试内存压力场景
	t.Run("TestMemoryPressure", func(t *testing.T) {
		t.Log("测试内存压力场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		// 记录初始内存
		var m1 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		const pressureActions = 100000 // 10万个动作
		ctx := context.Background()

		// 创建大量动作，每个动作包含大量数据
		for i := 0; i < pressureActions; i++ {
			// 创建包含大量数据的参数
			largeData := strings.Repeat(fmt.Sprintf("数据块%d-", i), 10) // 每个动作约100字节数据
			params := map[string]interface{}{
				"level":     "info",
				"message":   fmt.Sprintf("内存压力测试 %d", i),
				"large_data": largeData,
				"metadata": map[string]interface{}{
					"timestamp": time.Now().Unix(),
					"id":        i,
					"category":  "pressure_test",
				},
			}

			err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
			assert.NoError(t, err, "内存压力测试动作应该成功")

			// 每10000个动作检查一次内存
			if (i+1)%10000 == 0 {
				var m runtime.MemStats
				runtime.ReadMemStats(&m)
				t.Logf("处理 %d 动作后内存使用: %d MB", i+1, m.Alloc/1024/1024)
			}
		}

		// 记录峰值内存
		var m2 runtime.MemStats
		runtime.ReadMemStats(&m2)

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, pressureActions, "应该有正确数量的执行结果")

		logs := logService.GetLogs()
		assert.Len(t, logs, pressureActions, "应该有正确数量的日志记录")

		// 计算内存使用
		peakMemory := m2.Alloc - m1.Alloc
		avgMemoryPerAction := float64(peakMemory) / float64(pressureActions)

		t.Logf("内存压力测试结果:")
		t.Logf("  处理动作数: %d", pressureActions)
		t.Logf("  峰值内存增长: %d MB", peakMemory/1024/1024)
		t.Logf("  平均每动作内存: %.2f bytes", avgMemoryPerAction)

		// 内存使用断言
		assert.Less(t, avgMemoryPerAction, 2000.0, "平均每动作内存使用应该小于2KB")

		// 清理并验证内存释放
		actionManager.ClearExecutedActions()
		logService.ClearLogs()

		var m3 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m3)

		memoryReleased := m2.Alloc - m3.Alloc
		releaseRatio := float64(memoryReleased) / float64(peakMemory) * 100

		t.Logf("内存释放情况:")
		t.Logf("  释放内存: %d MB", memoryReleased/1024/1024)
		t.Logf("  释放比例: %.2f%%", releaseRatio)

		// 内存释放断言
		assert.Greater(t, releaseRatio, 50.0, "内存释放比例应该大于50%")
	})
}

// TestCommonBusinessScenarios 测试常见业务场景
func TestCommonBusinessScenarios(t *testing.T) {
	t.Log("开始测试常见业务场景")

	// 测试电商网站场景
	t.Run("TestECommerceScenario", func(t *testing.T) {
		t.Log("测试电商网站场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加商品查询API触发器
		productAPITrigger := &MockURLTrigger{
			Pattern:      "/api/products",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "商品API访问",
						"category": "ecommerce",
					},
				},
				{
					Type: ActionTypeModifyRequest,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Service": "product-service",
								"X-Version": "v1.0",
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(productAPITrigger)

		// 添加订单API触发器
		orderAPITrigger := &MockURLTrigger{
			Pattern:      "/api/orders",
			Priority:     2,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "订单API访问",
						"category": "ecommerce",
					},
				},
				{
					Type: ActionTypeCacheResponse,
					Params: map[string]interface{}{
						"ttl":     1800, // 30分钟缓存
						"key":     "order_cache",
						"enabled": true,
					},
				},
			},
		}
		triggerManager.AddTrigger(orderAPITrigger)

		// 添加支付失败触发器
		paymentErrorTrigger := &MockStatusTrigger{
			Codes:        []int{402, 403, 500},
			Priority:     3,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "支付处理失败",
						"category": "payment",
					},
				},
				{
					Type: ActionTypeRetry,
					Params: map[string]interface{}{
						"max_retries": 3,
						"delay":       "2s",
						"reason":      "支付失败重试",
					},
				},
			},
		}
		triggerManager.AddTrigger(paymentErrorTrigger)

		// 模拟电商场景请求
		scenarios := []struct {
			name     string
			request  *http.Request
			response *http.Response
			stage    ProcessStage
		}{
			{
				name:    "商品查询",
				request: func() *http.Request { req, _ := http.NewRequest("GET", "http://shop.com/api/products?category=electronics", nil); return req }(),
				stage:   ProcessStagePreRequest,
			},
			{
				name:    "订单查询",
				request: func() *http.Request { req, _ := http.NewRequest("GET", "http://shop.com/api/orders/12345", nil); return req }(),
				stage:   ProcessStagePreRequest,
			},
			{
				name:     "支付失败",
				request:  func() *http.Request { req, _ := http.NewRequest("POST", "http://shop.com/api/payment", nil); return req }(),
				response: &http.Response{StatusCode: 402},
				stage:    ProcessStagePostBody,
			},
		}

		ctx := context.Background()
		totalActions := 0

		for _, scenario := range scenarios {
			t.Logf("处理场景: %s", scenario.name)

			actions := triggerManager.ProcessTriggers(scenario.stage, scenario.request, scenario.response, 100*time.Millisecond)

			for _, action := range actions {
				err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
				assert.NoError(t, err, fmt.Sprintf("场景 %s 的动作执行应该成功", scenario.name))
				totalActions++
			}
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(results), "执行结果数量应该正确")

		logs := logService.GetLogs()
		assert.Greater(t, len(logs), 0, "应该有日志记录")

		// 验证特定日志
		ecommerceLogs := logService.GetLogsByLevel("info")
		paymentLogs := logService.GetLogsByLevel("error")

		t.Logf("电商场景测试完成:")
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  信息日志数: %d", len(ecommerceLogs))
		t.Logf("  错误日志数: %d", len(paymentLogs))
	})

	// 测试社交媒体场景
	t.Run("TestSocialMediaScenario", func(t *testing.T) {
		t.Log("测试社交媒体场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加内容审核触发器
		contentModerationTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "Content-Type",
			Pattern:      "multipart/form-data",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "内容上传检测",
						"category": "content_moderation",
					},
				},
				{
					Type: ActionTypeScript,
					Params: map[string]interface{}{
						"language": "javascript",
						"code":     "console.log('执行内容审核脚本');",
					},
				},
			},
		}
		triggerManager.AddTrigger(contentModerationTrigger)

		// 添加垃圾信息检测触发器
		spamDetectionTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "User-Agent",
			Pattern:      ".*[Ss]pam.*",
			Priority:     2,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "warn",
						"message": "检测到垃圾信息发送者",
						"category": "spam_detection",
					},
				},
				{
					Type: ActionTypeBanIP,
					Params: map[string]interface{}{
						"ip":       "*************",
						"duration": 7200, // 2小时
						"reason":   "垃圾信息发送",
					},
				},
			},
		}
		triggerManager.AddTrigger(spamDetectionTrigger)

		// 添加高频访问限制触发器
		rateLimitTrigger := &MockMaxRequestTimeTrigger{
			MaxTime:      100, // 100ms内的请求视为高频
			Priority:     3,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "warn",
						"message": "高频访问检测",
						"category": "rate_limit",
					},
				},
				{
					Type: ActionTypeBlockRequest,
					Params: map[string]interface{}{
						"status_code": 429,
						"reason":      "请求频率过高",
					},
				},
			},
		}
		triggerManager.AddTrigger(rateLimitTrigger)

		// 模拟社交媒体场景
		scenarios := []struct {
			name        string
			request     *http.Request
			response    *http.Response
			stage       ProcessStage
			requestTime time.Duration
		}{
			{
				name: "图片上传",
				request: func() *http.Request {
					req, _ := http.NewRequest("POST", "http://social.com/api/upload", nil)
					req.Header.Set("Content-Type", "multipart/form-data")
					return req
				}(),
				stage: ProcessStagePreRequest,
			},
			{
				name: "垃圾信息发送",
				request: func() *http.Request {
					req, _ := http.NewRequest("POST", "http://social.com/api/posts", nil)
					req.Header.Set("User-Agent", "SpamBot/1.0")
					return req
				}(),
				stage: ProcessStagePreRequest,
			},
			{
				name:        "高频访问",
				request:     func() *http.Request { req, _ := http.NewRequest("GET", "http://social.com/api/feed", nil); return req }(),
				response:    &http.Response{StatusCode: 200},
				stage:       ProcessStagePostBody,
				requestTime: 50 * time.Millisecond, // 快速请求
			},
		}

		ctx := context.Background()
		totalActions := 0

		for _, scenario := range scenarios {
			t.Logf("处理场景: %s", scenario.name)

			actions := triggerManager.ProcessTriggers(scenario.stage, scenario.request, scenario.response, scenario.requestTime)

			for _, action := range actions {
				err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
				assert.NoError(t, err, fmt.Sprintf("场景 %s 的动作执行应该成功", scenario.name))
				totalActions++
			}
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(results), "执行结果数量应该正确")

		logs := logService.GetLogs()
		assert.Greater(t, len(logs), 0, "应该有日志记录")

		// 验证特定类型的日志
		infoLogs := logService.GetLogsByLevel("info")
		warnLogs := logService.GetLogsByLevel("warn")

		t.Logf("社交媒体场景测试完成:")
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  信息日志数: %d", len(infoLogs))
		t.Logf("  警告日志数: %d", len(warnLogs))
	})
}

// TestBoundaryAndEdgeCases 测试边界条件和边缘情况
func TestBoundaryAndEdgeCases(t *testing.T) {
	t.Log("开始测试边界条件和边缘情况")

	// 测试空值和nil处理
	t.Run("TestNullAndEmptyValues", func(t *testing.T) {
		t.Log("测试空值和nil处理")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 测试nil请求
		actions := triggerManager.ProcessTriggers(ProcessStagePreRequest, nil, nil, 0)
		assert.Len(t, actions, 0, "nil请求不应该触发任何动作")

		// 测试空URL触发器
		emptyURLTrigger := &MockURLTrigger{
			Pattern:      "",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions:      []ActionConfig{},
		}
		triggerManager.AddTrigger(emptyURLTrigger)

		req, _ := http.NewRequest("GET", "http://example.com", nil)
		actions = triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		assert.Len(t, actions, 0, "空模式触发器不应该匹配")

		// 测试空参数动作执行
		ctx := context.Background()
		err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), nil)
		assert.NoError(t, err, "空参数的日志动作应该使用默认值")

		err = actionManager.ExecuteAction(ctx, string(ActionTypeLog), map[string]interface{}{})
		assert.NoError(t, err, "空参数映射的日志动作应该使用默认值")

		// 测试空字符串参数
		err = actionManager.ExecuteAction(ctx, string(ActionTypeLog), map[string]interface{}{
			"level":   "",
			"message": "",
		})
		assert.NoError(t, err, "空字符串参数应该使用默认值")

		t.Log("空值和nil处理测试完成")
	})

	// 测试极限参数值
	t.Run("TestExtremeParameterValues", func(t *testing.T) {
		t.Log("测试极限参数值")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		ctx := context.Background()

		// 测试极大的数值参数
		err := actionManager.ExecuteAction(ctx, string(ActionTypeBanIP), map[string]interface{}{
			"ip":       "*************",
			"duration": 999999999, // 极大的封禁时间
			"reason":   "极限测试",
		})
		assert.NoError(t, err, "极大数值参数应该被接受")

		// 测试极小的数值参数
		err = actionManager.ExecuteAction(ctx, string(ActionTypeBanIP), map[string]interface{}{
			"ip":       "*************",
			"duration": 1, // 极小的封禁时间
			"reason":   "极限测试",
		})
		assert.NoError(t, err, "极小数值参数应该被接受")

		// 测试超长字符串参数
		longString := strings.Repeat("A", 10000) // 10KB字符串
		err = actionManager.ExecuteAction(ctx, string(ActionTypeLog), map[string]interface{}{
			"level":   "info",
			"message": longString,
		})
		assert.NoError(t, err, "超长字符串参数应该被处理")

		// 测试特殊字符参数
		specialChars := "!@#$%^&*()_+-=[]{}|;':\",./<>?`~测试中文🎉🚀"
		err = actionManager.ExecuteAction(ctx, string(ActionTypeLog), map[string]interface{}{
			"level":   "info",
			"message": specialChars,
		})
		assert.NoError(t, err, "特殊字符参数应该被处理")

		// 测试复杂嵌套参数
		complexParams := map[string]interface{}{
			"level":   "info",
			"message": "复杂参数测试",
			"metadata": map[string]interface{}{
				"nested": map[string]interface{}{
					"deep": map[string]interface{}{
						"value": []interface{}{1, 2, 3, "test", true},
					},
				},
			},
		}
		err = actionManager.ExecuteAction(ctx, string(ActionTypeLog), complexParams)
		assert.NoError(t, err, "复杂嵌套参数应该被处理")

		t.Log("极限参数值测试完成")
	})

	// 测试异常网络条件
	t.Run("TestAbnormalNetworkConditions", func(t *testing.T) {
		t.Log("测试异常网络条件")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 添加超时触发器
		timeoutTrigger := &MockMaxRequestTimeTrigger{
			MaxTime:      1000, // 1秒
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "请求超时",
					},
				},
				{
					Type: ActionTypeRetry,
					Params: map[string]interface{}{
						"max_retries": 5,
						"delay":       "3s",
						"reason":      "网络超时重试",
					},
				},
			},
		}
		triggerManager.AddTrigger(timeoutTrigger)

		// 模拟各种异常网络条件
		networkConditions := []struct {
			name        string
			requestTime time.Duration
			statusCode  int
		}{
			{"极慢网络", 5 * time.Second, 200},
			{"网络中断", 10 * time.Second, 0},
			{"连接超时", 30 * time.Second, 0},
			{"DNS解析失败", 2 * time.Second, 0},
		}

		ctx := context.Background()
		totalActions := 0

		for _, condition := range networkConditions {
			t.Logf("测试网络条件: %s", condition.name)

			req, _ := http.NewRequest("GET", "http://slow-server.com/api", nil)
			resp := &http.Response{StatusCode: condition.statusCode}

			actions := triggerManager.ProcessTriggers(ProcessStagePostBody, req, resp, condition.requestTime)

			for _, action := range actions {
				err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
				assert.NoError(t, err, fmt.Sprintf("网络条件 %s 的动作执行应该成功", condition.name))
				totalActions++
			}
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Greater(t, len(results), 0, "异常网络条件应该触发动作")

		errorLogs := logService.GetLogsByLevel("error")
		assert.Greater(t, len(errorLogs), 0, "应该有错误日志记录")

		t.Logf("异常网络条件测试完成:")
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  错误日志数: %d", len(errorLogs))
	})

	// 测试资源耗尽场景
	t.Run("TestResourceExhaustionScenarios", func(t *testing.T) {
		t.Log("测试资源耗尽场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)

		// 模拟内存不足场景
		t.Run("MemoryExhaustion", func(t *testing.T) {
			ctx := context.Background()

			// 创建大量大对象来模拟内存压力
			const largeObjectCount = 1000
			for i := 0; i < largeObjectCount; i++ {
				largeData := make([]byte, 1024*1024) // 1MB数据
				for j := range largeData {
					largeData[j] = byte(i % 256)
				}

				params := map[string]interface{}{
					"level":     "info",
					"message":   fmt.Sprintf("内存压力测试 %d", i),
					"large_data": largeData,
				}

				err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
				assert.NoError(t, err, "内存压力下的动作执行应该成功")

				// 每100个对象检查一次内存
				if (i+1)%100 == 0 {
					var m runtime.MemStats
					runtime.ReadMemStats(&m)
					t.Logf("创建 %d 大对象后内存: %d MB", i+1, m.Alloc/1024/1024)
				}
			}

			// 强制垃圾回收
			runtime.GC()

			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			t.Logf("垃圾回收后内存: %d MB", m.Alloc/1024/1024)
		})

		// 模拟CPU密集型场景
		t.Run("CPUIntensiveOperations", func(t *testing.T) {
			ctx := context.Background()
			start := time.Now()

			const cpuIntensiveCount = 10000
			for i := 0; i < cpuIntensiveCount; i++ {
				// 模拟CPU密集型操作
				complexData := make(map[string]interface{})
				for j := 0; j < 100; j++ {
					complexData[fmt.Sprintf("key_%d_%d", i, j)] = fmt.Sprintf("value_%d_%d", i, j)
				}

				params := map[string]interface{}{
					"level":        "info",
					"message":      fmt.Sprintf("CPU密集型测试 %d", i),
					"complex_data": complexData,
				}

				err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
				assert.NoError(t, err, "CPU密集型操作应该成功")
			}

			duration := time.Since(start)
			throughput := float64(cpuIntensiveCount) / duration.Seconds()

			t.Logf("CPU密集型操作结果:")
			t.Logf("  操作数: %d", cpuIntensiveCount)
			t.Logf("  总时间: %v", duration)
			t.Logf("  吞吐量: %.2f ops/sec", throughput)
		})

		t.Log("资源耗尽场景测试完成")
	})
}

// TestComplexIntegrationScenarios 测试复杂集成场景
func TestComplexIntegrationScenarios(t *testing.T) {
	t.Log("开始测试复杂集成场景")

	// 测试多层代理链场景
	t.Run("TestMultiLayerProxyChain", func(t *testing.T) {
		t.Log("测试多层代理链场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 第一层：入口代理
		entryProxyTrigger := &MockURLTrigger{
			Pattern:      "/",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "入口代理处理",
						"layer":   "entry",
					},
				},
				{
					Type: ActionTypeModifyRequest,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Proxy-Layer": "entry",
								"X-Entry-Time":  time.Now().Format(time.RFC3339),
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(entryProxyTrigger)

		// 第二层：负载均衡代理
		loadBalancerTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "X-Proxy-Layer",
			Pattern:      "entry",
			Priority:     2,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "负载均衡代理处理",
						"layer":   "load_balancer",
					},
				},
				{
					Type: ActionTypeModifyRequest,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Load-Balancer": "active",
								"X-Backend-Server": "server-1",
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(loadBalancerTrigger)

		// 第三层：缓存代理
		cacheTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "X-Load-Balancer",
			Pattern:      "active",
			Priority:     3,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "缓存代理处理",
						"layer":   "cache",
					},
				},
				{
					Type: ActionTypeCacheResponse,
					Params: map[string]interface{}{
						"ttl":     3600,
						"key":     "multi_layer_cache",
						"enabled": true,
					},
				},
			},
		}
		triggerManager.AddTrigger(cacheTrigger)

		// 模拟请求通过多层代理
		req, _ := http.NewRequest("GET", "http://api.example.com/data", nil)
		ctx := context.Background()
		totalActions := 0

		// 第一层处理
		actions := triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "第一层代理动作应该成功")
			totalActions++
		}

		// 模拟添加第一层的头部
		req.Header.Set("X-Proxy-Layer", "entry")
		req.Header.Set("X-Entry-Time", time.Now().Format(time.RFC3339))

		// 第二层处理
		actions = triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "第二层代理动作应该成功")
			totalActions++
		}

		// 模拟添加第二层的头部
		req.Header.Set("X-Load-Balancer", "active")
		req.Header.Set("X-Backend-Server", "server-1")

		// 第三层处理
		actions = triggerManager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		for _, action := range actions {
			err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
			assert.NoError(t, err, "第三层代理动作应该成功")
			totalActions++
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(results), "多层代理动作数量应该正确")

		logs := logService.GetLogs()
		assert.GreaterOrEqual(t, len(logs), 3, "应该至少有3层代理的日志")

		// 验证每层的日志 - 由于触发器可能重复匹配，我们验证至少有每层的日志
		layerLogs := make(map[string]int)
		for _, log := range logs {
			if strings.Contains(log.Message, "入口代理") {
				layerLogs["entry"]++
			} else if strings.Contains(log.Message, "负载均衡") {
				layerLogs["load_balancer"]++
			} else if strings.Contains(log.Message, "缓存代理") {
				layerLogs["cache"]++
			}
		}

		assert.GreaterOrEqual(t, layerLogs["entry"], 1, "应该至少有1条入口代理日志")
		assert.GreaterOrEqual(t, layerLogs["load_balancer"], 1, "应该至少有1条负载均衡日志")
		assert.GreaterOrEqual(t, layerLogs["cache"], 1, "应该至少有1条缓存代理日志")

		t.Logf("多层代理链测试完成:")
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  处理层数: %d", len(layerLogs))
	})

	// 测试故障转移和恢复场景
	t.Run("TestFailoverAndRecoveryScenario", func(t *testing.T) {
		t.Log("测试故障转移和恢复场景")

		logService := NewMockLogService()
		actionManager := NewMockActionManager(logService)
		triggerManager := NewMockTriggerManager()

		// 主服务器故障检测
		primaryServerFailTrigger := &MockStatusTrigger{
			Codes:        []int{500, 502, 503},
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "主服务器故障检测",
						"server":  "primary",
					},
				},
				{
					Type: ActionTypeRetry,
					Params: map[string]interface{}{
						"max_retries": 2,
						"delay":       "1s",
						"reason":      "主服务器故障",
					},
				},
				{
					Type: ActionTypeModifyRequest,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Failover": "backup-server",
								"X-Retry-Count": "1",
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(primaryServerFailTrigger)

		// 备用服务器故障检测
		backupServerFailTrigger := &MockRequestHeaderTrigger{
			HeaderName:   "X-Failover",
			Pattern:      "backup-server",
			Priority:     2,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "备用服务器也故障",
						"server":  "backup",
					},
				},
				{
					Type: ActionTypeCacheResponse,
					Params: map[string]interface{}{
						"ttl":     300, // 5分钟缓存
						"key":     "emergency_cache",
						"enabled": true,
					},
				},
			},
		}
		triggerManager.AddTrigger(backupServerFailTrigger)

		// 服务器恢复检测
		serverRecoveryTrigger := &MockStatusTrigger{
			Codes:        []int{200, 201},
			Priority:     3,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "服务器恢复正常",
						"server":  "recovered",
					},
				},
				{
					Type: ActionTypeModifyResponse,
					Params: map[string]interface{}{
						"headers": map[string]interface{}{
							"add": map[string]string{
								"X-Server-Status": "healthy",
								"X-Recovery-Time": time.Now().Format(time.RFC3339),
							},
						},
					},
				},
			},
		}
		triggerManager.AddTrigger(serverRecoveryTrigger)

		// 模拟故障转移场景
		scenarios := []struct {
			name       string
			request    *http.Request
			response   *http.Response
			stage      ProcessStage
			expectLogs int
		}{
			{
				name:     "主服务器故障",
				request:  func() *http.Request { req, _ := http.NewRequest("GET", "http://primary.com/api", nil); return req }(),
				response: &http.Response{StatusCode: 500},
				stage:    ProcessStagePostBody,
				expectLogs: 1,
			},
			{
				name: "备用服务器故障",
				request: func() *http.Request {
					req, _ := http.NewRequest("GET", "http://backup.com/api", nil)
					req.Header.Set("X-Failover", "backup-server")
					return req
				}(),
				response: &http.Response{StatusCode: 503},
				stage:    ProcessStagePostBody,
				expectLogs: 1,
			},
			{
				name:     "服务器恢复",
				request:  func() *http.Request { req, _ := http.NewRequest("GET", "http://primary.com/api", nil); return req }(),
				response: &http.Response{StatusCode: 200},
				stage:    ProcessStagePostBody,
				expectLogs: 1,
			},
		}

		ctx := context.Background()
		totalActions := 0

		for _, scenario := range scenarios {
			t.Logf("处理故障转移场景: %s", scenario.name)

			actions := triggerManager.ProcessTriggers(scenario.stage, scenario.request, scenario.response, 100*time.Millisecond)

			for _, action := range actions {
				err := actionManager.ExecuteAction(ctx, string(action.Type), action.Params)
				assert.NoError(t, err, fmt.Sprintf("场景 %s 的动作执行应该成功", scenario.name))
				totalActions++
			}
		}

		// 验证结果
		results := actionManager.GetExecutedActions()
		assert.Equal(t, totalActions, len(results), "故障转移动作数量应该正确")

		logs := logService.GetLogs()
		assert.Greater(t, len(logs), 0, "应该有故障转移日志")

		// 验证不同类型的日志
		errorLogs := logService.GetLogsByLevel("error")
		infoLogs := logService.GetLogsByLevel("info")

		assert.Greater(t, len(errorLogs), 0, "应该有错误日志")
		assert.Greater(t, len(infoLogs), 0, "应该有恢复日志")

		t.Logf("故障转移和恢复场景测试完成:")
		t.Logf("  总动作数: %d", totalActions)
		t.Logf("  错误日志数: %d", len(errorLogs))
		t.Logf("  信息日志数: %d", len(infoLogs))
	})
}
