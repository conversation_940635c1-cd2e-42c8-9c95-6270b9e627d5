package modules

import (
	"context"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestMockActionManager 测试Mock动作管理器
func TestMockActionManager(t *testing.T) {
	logService := NewMockLogService()
	actionManager := NewMockActionManager(logService)

	// 测试日志动作
	t.Run("TestLogAction", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"level":   "info",
			"message": "测试日志消息",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeLog), params)
		assert.NoError(t, err, "日志动作应该执行成功")

		// 检查执行结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 1, "应该有一个执行结果")
		assert.True(t, results[0].Success, "动作应该执行成功")

		// 检查日志记录
		logs := logService.GetLogs()
		assert.Len(t, logs, 1, "应该有一条日志记录")
		assert.Equal(t, "info", logs[0].Level, "日志级别应该正确")
		assert.Equal(t, "测试日志消息", logs[0].Message, "日志消息应该正确")
	})

	// 测试IP封禁动作
	t.Run("TestBanIPAction", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"ip":       "*************",
			"duration": 3600,
			"reason":   "测试封禁",
		}

		err := actionManager.ExecuteAction(ctx, string(ActionTypeBanIP), params)
		assert.NoError(t, err, "IP封禁动作应该执行成功")

		// 检查执行结果
		results := actionManager.GetExecutedActions()
		assert.Len(t, results, 2, "应该有两个执行结果") // 包括之前的日志动作

		lastResult := results[len(results)-1]
		assert.True(t, lastResult.Success, "动作应该执行成功")
		assert.Contains(t, lastResult.Metadata, "ip", "元数据应该包含IP")
		assert.Equal(t, "*************", lastResult.Metadata["ip"], "IP应该正确")
	})

	// 测试无效动作类型
	t.Run("TestInvalidActionType", func(t *testing.T) {
		ctx := context.Background()
		err := actionManager.ExecuteAction(ctx, "invalid_action", map[string]interface{}{})
		assert.Error(t, err, "无效动作类型应该返回错误")
		assert.Contains(t, err.Error(), "未知的动作类型", "错误消息应该正确")
	})
}

// TestMockTriggers 测试Mock触发器
func TestMockTriggers(t *testing.T) {
	// 测试状态码触发器
	t.Run("TestStatusTrigger", func(t *testing.T) {
		trigger := &MockStatusTrigger{
			Codes:        []int{500, 502, 503},
			Priority:     1,
			ProcessStage: ProcessStagePostBody,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "error",
						"message": "服务器错误",
					},
				},
			},
		}

		// 创建测试请求和响应
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		resp := &http.Response{StatusCode: 500}

		// 测试匹配
		matched := trigger.Match(req, resp, 100*time.Millisecond)
		assert.True(t, matched, "状态码500应该匹配触发器")

		// 测试不匹配
		resp.StatusCode = 200
		matched = trigger.Match(req, resp, 100*time.Millisecond)
		assert.False(t, matched, "状态码200不应该匹配触发器")

		// 测试获取动作
		actions := trigger.GetActions()
		assert.Len(t, actions, 1, "应该有一个动作")
		assert.Equal(t, ActionTypeLog, actions[0].Type, "动作类型应该正确")
	})

	// 测试URL触发器
	t.Run("TestURLTrigger", func(t *testing.T) {
		trigger := &MockURLTrigger{
			Pattern:      "/api/",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeLog,
					Params: map[string]interface{}{
						"level":   "info",
						"message": "API访问",
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "包含'/api/'的URL应该匹配")

		// 测试不匹配
		req, _ = http.NewRequest("GET", "http://example.com/home", nil)
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "不包含'/api/'的URL不应该匹配")
	})

	// 测试域名触发器
	t.Run("TestDomainTrigger", func(t *testing.T) {
		trigger := &MockDomainTrigger{
			Pattern:      "malicious-site.com",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeBanDomain,
					Params: map[string]interface{}{
						"domain":   "malicious-site.com",
						"duration": 86400,
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://malicious-site.com/attack", nil)
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "恶意域名应该匹配")

		// 测试不匹配
		req, _ = http.NewRequest("GET", "http://example.com/safe", nil)
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "安全域名不应该匹配")
	})

	// 测试请求头触发器
	t.Run("TestRequestHeaderTrigger", func(t *testing.T) {
		trigger := &MockRequestHeaderTrigger{
			HeaderName:   "User-Agent",
			Pattern:      ".*Bot.*",
			Priority:     1,
			ProcessStage: ProcessStagePreRequest,
			Actions: []ActionConfig{
				{
					Type: ActionTypeBanIP,
					Params: map[string]interface{}{
						"reason": "Bot检测",
					},
				},
			},
		}

		// 测试匹配
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set("User-Agent", "AttackBot/1.0")
		matched := trigger.Match(req, nil, 0)
		assert.True(t, matched, "包含'Bot'的User-Agent应该匹配")

		// 测试不匹配
		req.Header.Set("User-Agent", "Mozilla/5.0")
		matched = trigger.Match(req, nil, 0)
		assert.False(t, matched, "正常的User-Agent不应该匹配")
	})
}

// TestMockTriggerManager 测试Mock触发器管理器
func TestMockTriggerManager(t *testing.T) {
	manager := NewMockTriggerManager()

	// 添加状态码触发器
	statusTrigger := &MockStatusTrigger{
		Codes:        []int{500},
		Priority:     1,
		ProcessStage: ProcessStagePostBody,
		Actions: []ActionConfig{
			{
				Type: ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "error",
					"message": "服务器错误",
				},
			},
		},
	}
	manager.AddTrigger(statusTrigger)

	// 添加URL触发器
	urlTrigger := &MockURLTrigger{
		Pattern:      "/api/",
		Priority:     2,
		ProcessStage: ProcessStagePreRequest,
		Actions: []ActionConfig{
			{
				Type: ActionTypeModifyRequest,
				Params: map[string]interface{}{
					"headers": map[string]interface{}{
						"add": map[string]string{
							"X-API-Request": "true",
						},
					},
				},
			},
		},
	}
	manager.AddTrigger(urlTrigger)

	// 测试处理触发器
	t.Run("TestProcessTriggers", func(t *testing.T) {
		// 测试PreRequest阶段
		req, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
		actions := manager.ProcessTriggers(ProcessStagePreRequest, req, nil, 0)
		assert.Len(t, actions, 1, "PreRequest阶段应该有一个触发的动作")
		assert.Equal(t, ActionTypeModifyRequest, actions[0].Type, "应该是修改请求动作")

		// 测试PostBody阶段
		resp := &http.Response{StatusCode: 500}
		actions = manager.ProcessTriggers(ProcessStagePostBody, req, resp, 100*time.Millisecond)
		assert.Len(t, actions, 1, "PostBody阶段应该有一个触发的动作")
		assert.Equal(t, ActionTypeLog, actions[0].Type, "应该是日志动作")

		// 测试无匹配的阶段
		actions = manager.ProcessTriggers(ProcessStagePostHeader, req, resp, 100*time.Millisecond)
		assert.Len(t, actions, 0, "PostHeader阶段不应该有触发的动作")
	})
}

// TestMockLogService 测试Mock日志服务
func TestMockLogService(t *testing.T) {
	logService := NewMockLogService()

	// 测试日志记录
	t.Run("TestLogging", func(t *testing.T) {
		logService.Log("info", "测试信息日志")
		logService.Log("error", "测试错误日志")
		logService.Log("warn", "测试警告日志")

		logs := logService.GetLogs()
		assert.Len(t, logs, 3, "应该有3条日志")

		// 检查日志内容
		assert.Equal(t, "info", logs[0].Level, "第一条日志级别应该正确")
		assert.Equal(t, "测试信息日志", logs[0].Message, "第一条日志消息应该正确")

		assert.Equal(t, "error", logs[1].Level, "第二条日志级别应该正确")
		assert.Equal(t, "测试错误日志", logs[1].Message, "第二条日志消息应该正确")
	})

	// 测试按级别获取日志
	t.Run("TestGetLogsByLevel", func(t *testing.T) {
		errorLogs := logService.GetLogsByLevel("error")
		assert.Len(t, errorLogs, 1, "应该有1条错误日志")
		assert.Equal(t, "测试错误日志", errorLogs[0].Message, "错误日志消息应该正确")

		infoLogs := logService.GetLogsByLevel("info")
		assert.Len(t, infoLogs, 1, "应该有1条信息日志")

		debugLogs := logService.GetLogsByLevel("debug")
		assert.Len(t, debugLogs, 0, "不应该有调试日志")
	})

	// 测试清空日志
	t.Run("TestClearLogs", func(t *testing.T) {
		assert.Equal(t, 3, logService.GetLogCount(), "清空前应该有3条日志")

		logService.ClearLogs()
		assert.Equal(t, 0, logService.GetLogCount(), "清空后应该没有日志")

		logs := logService.GetLogs()
		assert.Len(t, logs, 0, "清空后日志列表应该为空")
	})
}

// TestActionTypes 测试动作类型常量
func TestActionTypes(t *testing.T) {
	// 测试基础动作类型
	basicActions := []ActionType{
		ActionTypeLog,
		ActionTypeBanIP,
		ActionTypeBanDomain,
		ActionTypeBlockRequest,
		ActionTypeModifyRequest,
		ActionTypeModifyResponse,
		ActionTypeCacheResponse,
		ActionTypeScript,
	}

	for _, actionType := range basicActions {
		assert.NotEmpty(t, string(actionType), "动作类型不应该为空")
		assert.True(t, strings.Contains(string(actionType), "_") || len(string(actionType)) > 3, "动作类型应该有意义")
	}

	// 测试扩展动作类型
	extendedActions := []ActionType{
		ActionTypeRetry,
		ActionTypeRetrySame,
		ActionTypeSaveToPool,
		ActionTypeCache,
		ActionTypeRequestURL,
		ActionTypeBanIPDomain,
		ActionTypeNullResponse,
		ActionTypeBypassProxy,
	}

	for _, actionType := range extendedActions {
		assert.NotEmpty(t, string(actionType), "扩展动作类型不应该为空")
		assert.True(t, strings.Contains(string(actionType), "_") || len(string(actionType)) > 3, "扩展动作类型应该有意义")
	}
}

// TestTriggerTypes 测试触发器类型常量
func TestTriggerTypes(t *testing.T) {
	triggerTypes := []TriggerType{
		TriggerTypeStatus,
		TriggerTypeBody,
		TriggerTypeMaxRequestTime,
		TriggerTypeConnTimeOut,
		TriggerTypeMinRequestTime,
		TriggerTypeURL,
		TriggerTypeDomain,
		TriggerTypeCombined,
		TriggerTypeCustom,
		TriggerTypeRequestBody,
		TriggerTypeRequestHeader,
		TriggerTypeResponseHeader,
	}

	for _, triggerType := range triggerTypes {
		assert.NotEmpty(t, string(triggerType), "触发器类型不应该为空")
		assert.True(t, len(string(triggerType)) > 2, "触发器类型应该有意义")
	}
}

// TestProcessStages 测试处理阶段常量
func TestProcessStages(t *testing.T) {
	stages := []ProcessStage{
		ProcessStagePreRequest,
		ProcessStagePostHeader,
		ProcessStagePostBody,
		ProcessStagePreResponse,
	}

	for _, stage := range stages {
		assert.NotEmpty(t, string(stage), "处理阶段不应该为空")
		assert.True(t, strings.Contains(string(stage), "_"), "处理阶段应该包含下划线")
	}
}
