// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
)

// LoggingIntegrationTestSuite Logging 模块集成测试套件
type LoggingIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	testUtils    *framework.TestUtils
	logFiles     []string
	logLevels    []string
	logFormats   []string
}

// SetupSuite 测试套件初始化
func (s *LoggingIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.logFiles = make([]string, 0)
	s.logLevels = []string{"debug", "info", "warn", "error", "fatal"}
	s.logFormats = []string{"text", "json"}
	
	s.T().Logf("Logging 集成测试环境初始化完成")
}

// TestLogLevels 测试不同日志级别
func (s *LoggingIntegrationTestSuite) TestLogLevels() {
	testName := "TestLogLevels"
	s.AddLog(testName, "开始测试不同日志级别")
	
	for _, level := range s.logLevels {
		s.Run(fmt.Sprintf("LogLevel_%s", level), func() {
			// 创建日志级别配置
			configContent := s.createLogLevelConfig(level)
			configFile, err := s.GetEnvironment().CreateConfigFile(
				fmt.Sprintf("logging_level_%s.yaml", level), configContent)
			s.Require().NoError(err, fmt.Sprintf("创建日志级别 %s 配置失败", level))
			
			s.AddLog(testName, fmt.Sprintf("测试日志级别: %s，配置文件: %s", level, configFile))
			
			// 模拟日志级别测试
			s.simulateLogLevelTest(testName, level)
		})
	}
	
	s.AddLog(testName, "不同日志级别测试完成")
}

// TestLogFormats 测试不同日志格式
func (s *LoggingIntegrationTestSuite) TestLogFormats() {
	testName := "TestLogFormats"
	s.AddLog(testName, "开始测试不同日志格式")
	
	for _, format := range s.logFormats {
		s.Run(fmt.Sprintf("LogFormat_%s", format), func() {
			// 创建日志格式配置
			configContent := s.createLogFormatConfig(format)
			configFile, err := s.GetEnvironment().CreateConfigFile(
				fmt.Sprintf("logging_format_%s.yaml", format), configContent)
			s.Require().NoError(err, fmt.Sprintf("创建日志格式 %s 配置失败", format))
			
			s.AddLog(testName, fmt.Sprintf("测试日志格式: %s，配置文件: %s", format, configFile))
			
			// 模拟日志格式测试
			s.simulateLogFormatTest(testName, format)
		})
	}
	
	s.AddLog(testName, "不同日志格式测试完成")
}

// TestLogRotation 测试日志轮转
func (s *LoggingIntegrationTestSuite) TestLogRotation() {
	testName := "TestLogRotation"
	s.AddLog(testName, "开始测试日志轮转")
	
	// 创建日志轮转配置
	configContent := s.createLogRotationConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("logging_rotation.yaml", configContent)
	s.Require().NoError(err, "创建日志轮转配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟日志轮转测试
	s.simulateLogRotationTest(testName)
	
	s.AddLog(testName, "日志轮转测试完成")
}

// TestTraceIDCorrelation 测试追踪 ID 关联
func (s *LoggingIntegrationTestSuite) TestTraceIDCorrelation() {
	testName := "TestTraceIDCorrelation"
	s.AddLog(testName, "开始测试追踪 ID 关联")
	
	// 创建追踪 ID 配置
	configContent := s.createTraceIDConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("logging_traceid.yaml", configContent)
	s.Require().NoError(err, "创建追踪 ID 配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟追踪 ID 测试
	s.simulateTraceIDTest(testName)
	
	s.AddLog(testName, "追踪 ID 关联测试完成")
}

// TestStructuredLogging 测试结构化日志
func (s *LoggingIntegrationTestSuite) TestStructuredLogging() {
	testName := "TestStructuredLogging"
	s.AddLog(testName, "开始测试结构化日志")
	
	// 创建结构化日志配置
	configContent := s.createStructuredLoggingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("logging_structured.yaml", configContent)
	s.Require().NoError(err, "创建结构化日志配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟结构化日志测试
	s.simulateStructuredLoggingTest(testName)
	
	s.AddLog(testName, "结构化日志测试完成")
}

// TestLogPerformance 测试日志性能
func (s *LoggingIntegrationTestSuite) TestLogPerformance() {
	testName := "TestLogPerformance"
	s.AddLog(testName, "开始测试日志性能")
	
	// 创建性能测试配置
	configContent := s.createPerformanceConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("logging_performance.yaml", configContent)
	s.Require().NoError(err, "创建日志性能配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟性能测试
	s.simulatePerformanceTest(testName)
	
	s.AddLog(testName, "日志性能测试完成")
}

// 模拟测试方法

// simulateLogLevelTest 模拟日志级别测试
func (s *LoggingIntegrationTestSuite) simulateLogLevelTest(testName, level string) {
	start := time.Now()
	
	s.AddLog(testName, fmt.Sprintf("模拟 %s 级别日志输出", level))
	
	// 创建测试日志文件
	logFile := filepath.Join(s.GetLogDir(), fmt.Sprintf("test_%s.log", level))
	s.logFiles = append(s.logFiles, logFile)
	
	// 模拟不同级别的日志消息
	logMessages := map[string][]string{
		"debug": {"调试信息: 变量值检查", "调试信息: 函数调用跟踪"},
		"info":  {"信息: 服务启动完成", "信息: 请求处理成功"},
		"warn":  {"警告: 连接超时重试", "警告: 配置参数异常"},
		"error": {"错误: 数据库连接失败", "错误: 文件读取异常"},
		"fatal": {"致命错误: 系统崩溃", "致命错误: 内存不足"},
	}
	
	messages := logMessages[level]
	if messages == nil {
		messages = []string{fmt.Sprintf("%s 级别测试消息", level)}
	}
	
	logCount := 0
	for _, message := range messages {
		s.AddLog(testName, fmt.Sprintf("写入日志: [%s] %s", strings.ToUpper(level), message))
		logCount++
		time.Sleep(5 * time.Millisecond)
	}
	
	// 模拟创建日志文件
	testContent := fmt.Sprintf("[%s] %s 级别测试日志\n", time.Now().Format("2006-01-02 15:04:05"), level)
	err := s.testUtils.WriteFile(logFile, []byte(testContent))
	s.Require().NoError(err, "写入测试日志文件失败")
	
	// 记录指标
	s.RecordMetric(testName, "request_count", int64(logCount))
	s.RecordMetric(testName, "success_count", int64(logCount))
	s.RecordMetric(testName, "avg_response_time", 5*time.Millisecond)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("%s 级别日志测试完成，耗时: %v，日志数: %d", 
		level, duration, logCount))
	
	// 验证日志文件创建
	s.Assert().True(s.testUtils.FileExists(logFile), "日志文件应该被创建")
}

// simulateLogFormatTest 模拟日志格式测试
func (s *LoggingIntegrationTestSuite) simulateLogFormatTest(testName, format string) {
	start := time.Now()
	
	s.AddLog(testName, fmt.Sprintf("模拟 %s 格式日志输出", format))
	
	// 创建测试日志文件
	logFile := filepath.Join(s.GetLogDir(), fmt.Sprintf("test_format_%s.log", format))
	s.logFiles = append(s.logFiles, logFile)
	
	// 模拟不同格式的日志内容
	var testContent string
	switch format {
	case "json":
		testContent = `{"timestamp":"2024-01-01T12:00:00Z","level":"info","message":"JSON格式测试日志","component":"test","trace_id":"test-123"}` + "\n"
	case "text":
		testContent = "2024-01-01 12:00:00 [INFO] [test] [trace_id=test-123] TEXT格式测试日志\n"
	default:
		testContent = fmt.Sprintf("[%s] %s 格式测试日志\n", time.Now().Format("2006-01-02 15:04:05"), format)
	}
	
	// 写入测试日志
	err := s.testUtils.WriteFile(logFile, []byte(testContent))
	s.Require().NoError(err, "写入测试日志文件失败")
	
	s.AddLog(testName, fmt.Sprintf("写入 %s 格式日志: %s", format, strings.TrimSpace(testContent)))
	
	// 验证日志格式
	content, err := s.testUtils.ReadFile(logFile)
	s.Require().NoError(err, "读取日志文件失败")
	
	contentStr := string(content)
	switch format {
	case "json":
		s.Assert().Contains(contentStr, `"level":"info"`, "JSON 格式应该包含级别字段")
		s.Assert().Contains(contentStr, `"trace_id":"test-123"`, "JSON 格式应该包含追踪 ID")
	case "text":
		s.Assert().Contains(contentStr, "[INFO]", "TEXT 格式应该包含级别标识")
		s.Assert().Contains(contentStr, "trace_id=test-123", "TEXT 格式应该包含追踪 ID")
	}
	
	// 记录指标
	s.RecordMetric(testName, "request_count", int64(1))
	s.RecordMetric(testName, "success_count", int64(1))
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("%s 格式日志测试完成，耗时: %v", format, duration))
}

// simulateLogRotationTest 模拟日志轮转测试
func (s *LoggingIntegrationTestSuite) simulateLogRotationTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟日志轮转机制")

	// 创建主日志文件
	logFile := filepath.Join(s.GetLogDir(), "rotation_test.log")
	s.logFiles = append(s.logFiles, logFile)

	// 模拟写入大量日志触发轮转
	totalLogs := 100
	rotationCount := 0

	for i := 0; i < totalLogs; i++ {
		logMessage := fmt.Sprintf("日志消息 %d: 这是一条测试日志，用于触发日志轮转机制", i+1)
		s.AddLog(testName, fmt.Sprintf("写入日志 %d", i+1))

		// 模拟日志轮转（每20条日志轮转一次）
		if i > 0 && i%20 == 0 {
			rotationCount++
			rotatedFile := fmt.Sprintf("%s.%d", logFile, rotationCount)
			s.logFiles = append(s.logFiles, rotatedFile)

			s.AddLog(testName, fmt.Sprintf("触发日志轮转，创建文件: %s", rotatedFile))

			// 创建轮转后的日志文件
			rotatedContent := fmt.Sprintf("轮转日志文件 %d\n", rotationCount)
			err := s.testUtils.WriteFile(rotatedFile, []byte(rotatedContent))
			s.Require().NoError(err, "创建轮转日志文件失败")
		}

		time.Sleep(2 * time.Millisecond)
	}

	// 创建主日志文件
	mainContent := "主日志文件内容\n"
	err := s.testUtils.WriteFile(logFile, []byte(mainContent))
	s.Require().NoError(err, "创建主日志文件失败")

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(totalLogs))
	s.RecordMetric(testName, "success_count", int64(totalLogs))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("日志轮转测试完成，耗时: %v，轮转次数: %d", duration, rotationCount))

	// 验证轮转效果
	s.Assert().Greater(rotationCount, 0, "应该发生日志轮转")
	s.Assert().True(s.testUtils.FileExists(logFile), "主日志文件应该存在")
}

// simulateTraceIDTest 模拟追踪 ID 测试
func (s *LoggingIntegrationTestSuite) simulateTraceIDTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟追踪 ID 关联")

	// 创建追踪日志文件
	logFile := filepath.Join(s.GetLogDir(), "traceid_test.log")
	s.logFiles = append(s.logFiles, logFile)

	// 模拟多个请求的追踪 ID
	traceIDs := []string{
		"trace-001-abc123",
		"trace-002-def456",
		"trace-003-ghi789",
	}

	logEntries := make([]string, 0)

	for i, traceID := range traceIDs {
		s.AddLog(testName, fmt.Sprintf("处理请求 %d，追踪 ID: %s", i+1, traceID))

		// 模拟同一个请求的多个日志条目
		requestLogs := []string{
			fmt.Sprintf("[INFO] [%s] 请求开始处理", traceID),
			fmt.Sprintf("[DEBUG] [%s] 验证请求参数", traceID),
			fmt.Sprintf("[INFO] [%s] 调用业务逻辑", traceID),
			fmt.Sprintf("[INFO] [%s] 请求处理完成", traceID),
		}

		for _, logEntry := range requestLogs {
			s.AddLog(testName, fmt.Sprintf("写入日志: %s", logEntry))
			logEntries = append(logEntries, logEntry)
			time.Sleep(3 * time.Millisecond)
		}
	}

	// 写入追踪日志文件
	content := strings.Join(logEntries, "\n") + "\n"
	err := s.testUtils.WriteFile(logFile, []byte(content))
	s.Require().NoError(err, "写入追踪日志文件失败")

	// 验证追踪 ID 关联
	fileContent, err := s.testUtils.ReadFile(logFile)
	s.Require().NoError(err, "读取追踪日志文件失败")

	contentStr := string(fileContent)
	for _, traceID := range traceIDs {
		s.Assert().Contains(contentStr, traceID, fmt.Sprintf("日志应该包含追踪 ID: %s", traceID))
	}

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(len(logEntries)))
	s.RecordMetric(testName, "success_count", int64(len(logEntries)))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("追踪 ID 测试完成，耗时: %v，日志条目: %d",
		duration, len(logEntries)))
}

// simulateStructuredLoggingTest 模拟结构化日志测试
func (s *LoggingIntegrationTestSuite) simulateStructuredLoggingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟结构化日志")

	// 创建结构化日志文件
	logFile := filepath.Join(s.GetLogDir(), "structured_test.log")
	s.logFiles = append(s.logFiles, logFile)

	// 模拟结构化日志条目
	structuredLogs := []string{
		`{"timestamp":"2024-01-01T12:00:00Z","level":"info","component":"server","action":"start","message":"服务器启动","port":8080}`,
		`{"timestamp":"2024-01-01T12:00:01Z","level":"info","component":"proxy","action":"request","message":"处理代理请求","client_ip":"*************","target":"example.com"}`,
		`{"timestamp":"2024-01-01T12:00:02Z","level":"warn","component":"cache","action":"evict","message":"缓存项过期","key":"dns:example.com","ttl":300}`,
		`{"timestamp":"2024-01-01T12:00:03Z","level":"error","component":"dns","action":"resolve","message":"DNS解析失败","domain":"invalid.test","error":"no such host"}`,
	}

	for i, logEntry := range structuredLogs {
		s.AddLog(testName, fmt.Sprintf("写入结构化日志 %d", i+1))
		time.Sleep(2 * time.Millisecond)
	}

	// 写入结构化日志文件
	content := strings.Join(structuredLogs, "\n") + "\n"
	err := s.testUtils.WriteFile(logFile, []byte(content))
	s.Require().NoError(err, "写入结构化日志文件失败")

	// 验证结构化格式
	fileContent, err := s.testUtils.ReadFile(logFile)
	s.Require().NoError(err, "读取结构化日志文件失败")

	contentStr := string(fileContent)
	s.Assert().Contains(contentStr, `"component":"server"`, "应该包含组件字段")
	s.Assert().Contains(contentStr, `"action":"start"`, "应该包含动作字段")
	s.Assert().Contains(contentStr, `"level":"info"`, "应该包含级别字段")

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(len(structuredLogs)))
	s.RecordMetric(testName, "success_count", int64(len(structuredLogs)))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("结构化日志测试完成，耗时: %v，日志条目: %d",
		duration, len(structuredLogs)))
}

// simulatePerformanceTest 模拟性能测试
func (s *LoggingIntegrationTestSuite) simulatePerformanceTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟日志性能测试")

	// 创建性能测试日志文件
	logFile := filepath.Join(s.GetLogDir(), "performance_test.log")
	s.logFiles = append(s.logFiles, logFile)

	// 模拟高频日志写入
	logCount := 1000
	batchSize := 100

	for batch := 0; batch < logCount/batchSize; batch++ {
		batchStart := time.Now()

		for i := 0; i < batchSize; i++ {
			logIndex := batch*batchSize + i + 1
			s.AddLog(testName, fmt.Sprintf("高频日志 %d", logIndex))
			// 不添加延迟，测试真实性能
		}

		batchDuration := time.Since(batchStart)
		s.AddLog(testName, fmt.Sprintf("批次 %d 完成，耗时: %v", batch+1, batchDuration))
	}

	// 创建性能测试日志文件
	perfContent := fmt.Sprintf("性能测试日志，总计 %d 条日志\n", logCount)
	err := s.testUtils.WriteFile(logFile, []byte(perfContent))
	s.Require().NoError(err, "写入性能测试日志文件失败")

	duration := time.Since(start)
	throughput := float64(logCount) / duration.Seconds()

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(logCount))
	s.RecordMetric(testName, "success_count", int64(logCount))
	s.RecordMetric(testName, "throughput_rps", throughput)
	s.RecordMetric(testName, "avg_response_time", duration/time.Duration(logCount))

	s.AddLog(testName, fmt.Sprintf("性能测试完成，耗时: %v，吞吐量: %.2f logs/sec",
		duration, throughput))

	// 验证性能指标
	s.Assert().Greater(throughput, 100.0, "日志吞吐量应该大于 100 logs/sec")
}

// TearDownSuite 测试套件清理
func (s *LoggingIntegrationTestSuite) TearDownSuite() {
	// 清理测试日志文件
	for _, logFile := range s.logFiles {
		if s.testUtils.FileExists(logFile) {
			os.Remove(logFile)
			s.T().Logf("清理日志文件: %s", logFile)
		}
	}

	s.IntegrationTestSuite.TearDownSuite()
}

// 配置生成方法

// createLogLevelConfig 创建日志级别配置
func (s *LoggingIntegrationTestSuite) createLogLevelConfig(level string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "%s"
  format: "text"
  file: "%s/test_%s.log"
  max_size: 10
  max_backups: 3
  max_age: 7
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080
  read_timeout: "10s"
  write_timeout: "10s"
`, level, s.GetLogDir(), level)
}

// createLogFormatConfig 创建日志格式配置
func (s *LoggingIntegrationTestSuite) createLogFormatConfig(format string) string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "info"
  format: "%s"
  file: "%s/test_format_%s.log"
  max_size: 10
  max_backups: 3
  max_age: 7
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080
`, format, s.GetLogDir(), format)
}

// createLogRotationConfig 创建日志轮转配置
func (s *LoggingIntegrationTestSuite) createLogRotationConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "info"
  format: "text"
  file: "%s/rotation_test.log"
  max_size: 1  # 小文件大小触发轮转
  max_backups: 5
  max_age: 1
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080
`, s.GetLogDir())
}

// createTraceIDConfig 创建追踪 ID 配置
func (s *LoggingIntegrationTestSuite) createTraceIDConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "debug"
  format: "text"
  file: "%s/traceid_test.log"
  max_size: 10
  max_backups: 3
  max_age: 7
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080

monitoring:
  enabled: true
  port: 19094
  path: "/metrics"
  interval: "5s"
`, s.GetLogDir())
}

// createStructuredLoggingConfig 创建结构化日志配置
func (s *LoggingIntegrationTestSuite) createStructuredLoggingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "info"
  format: "json"
  file: "%s/structured_test.log"
  max_size: 10
  max_backups: 3
  max_age: 7
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080
`, s.GetLogDir())
}

// createPerformanceConfig 创建性能测试配置
func (s *LoggingIntegrationTestSuite) createPerformanceConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

logging:
  enabled: true
  level: "info"
  format: "text"
  file: "%s/performance_test.log"
  max_size: 50  # 大文件用于性能测试
  max_backups: 2
  max_age: 1
  time_format: "2006-01-02T15:04:05.000Z07:00"

server:
  host: "127.0.0.1"
  port: 18080

monitoring:
  enabled: true
  port: 19095
  path: "/metrics"
  interval: "1s"  # 频繁监控用于性能测试
`, s.GetLogDir())
}

// TestLoggingIntegration 运行 Logging 集成测试
func TestLoggingIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(LoggingIntegrationTestSuite))
}
