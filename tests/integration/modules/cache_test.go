// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
)

// CacheIntegrationTestSuite Cache 模块集成测试套件
type CacheIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	dnsMockServer *framework.DNSMockServer
	testUtils     *framework.TestUtils
	cacheKeys     []string
}

// SetupSuite 测试套件初始化
func (s *CacheIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.cacheKeys = make([]string, 0)
	
	// 创建 DNS Mock 服务器用于测试 DNS 缓存
	var err error
	s.dnsMockServer, err = s.GetMockManager().CreateDNSMockServer("cache_dns", 5354)
	s.Require().NoError(err, "创建 DNS Mock 服务器失败")
	
	// 添加测试 DNS 记录
	s.dnsMockServer.AddRecord("cache-test.com", []string{"*************"})
	s.dnsMockServer.AddRecord("example.test", []string{"********", "********"})
	s.dnsMockServer.AddRecord("multi-ip.test", []string{"**********", "**********", "**********"})
	
	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("Cache 集成测试环境初始化完成")
}

// TestDNSCacheBasicFunctionality 测试 DNS 缓存基本功能
func (s *CacheIntegrationTestSuite) TestDNSCacheBasicFunctionality() {
	testName := "TestDNSCacheBasicFunctionality"
	s.AddLog(testName, "开始测试 DNS 缓存基本功能")
	
	// 创建 DNS 缓存配置
	configContent := s.createDNSCacheConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_dns_basic.yaml", configContent)
	s.Require().NoError(err, "创建 DNS 缓存配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 DNS 缓存测试
	s.simulateDNSCacheTest(testName)
	
	s.AddLog(testName, "DNS 缓存基本功能测试完成")
}

// TestDNSCacheHitMiss 测试 DNS 缓存命中和未命中
func (s *CacheIntegrationTestSuite) TestDNSCacheHitMiss() {
	testName := "TestDNSCacheHitMiss"
	s.AddLog(testName, "开始测试 DNS 缓存命中和未命中")
	
	// 创建缓存命中测试配置
	configContent := s.createCacheHitMissConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_hit_miss.yaml", configContent)
	s.Require().NoError(err, "创建缓存命中测试配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟缓存命中/未命中测试
	s.simulateCacheHitMissTest(testName)
	
	s.AddLog(testName, "DNS 缓存命中和未命中测试完成")
}

// TestDNSCacheExpiration 测试 DNS 缓存过期策略
func (s *CacheIntegrationTestSuite) TestDNSCacheExpiration() {
	testName := "TestDNSCacheExpiration"
	s.AddLog(testName, "开始测试 DNS 缓存过期策略")
	
	// 创建缓存过期配置
	configContent := s.createCacheExpirationConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_expiration.yaml", configContent)
	s.Require().NoError(err, "创建缓存过期配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟缓存过期测试
	s.simulateCacheExpirationTest(testName)
	
	s.AddLog(testName, "DNS 缓存过期策略测试完成")
}

// TestRegexCacheFunctionality 测试正则表达式缓存功能
func (s *CacheIntegrationTestSuite) TestRegexCacheFunctionality() {
	testName := "TestRegexCacheFunctionality"
	s.AddLog(testName, "开始测试正则表达式缓存功能")
	
	// 创建正则缓存配置
	configContent := s.createRegexCacheConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_regex.yaml", configContent)
	s.Require().NoError(err, "创建正则缓存配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟正则缓存测试
	s.simulateRegexCacheTest(testName)
	
	s.AddLog(testName, "正则表达式缓存功能测试完成")
}

// TestCacheMemoryManagement 测试缓存内存管理
func (s *CacheIntegrationTestSuite) TestCacheMemoryManagement() {
	testName := "TestCacheMemoryManagement"
	s.AddLog(testName, "开始测试缓存内存管理")
	
	// 创建内存管理配置
	configContent := s.createMemoryManagementConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_memory.yaml", configContent)
	s.Require().NoError(err, "创建内存管理配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟内存管理测试
	s.simulateMemoryManagementTest(testName)
	
	s.AddLog(testName, "缓存内存管理测试完成")
}

// TestCacheCleanupRoutine 测试缓存清理例程
func (s *CacheIntegrationTestSuite) TestCacheCleanupRoutine() {
	testName := "TestCacheCleanupRoutine"
	s.AddLog(testName, "开始测试缓存清理例程")
	
	// 创建清理例程配置
	configContent := s.createCleanupRoutineConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("cache_cleanup.yaml", configContent)
	s.Require().NoError(err, "创建清理例程配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟清理例程测试
	s.simulateCleanupRoutineTest(testName)
	
	s.AddLog(testName, "缓存清理例程测试完成")
}

// 模拟测试方法

// simulateDNSCacheTest 模拟 DNS 缓存测试
func (s *CacheIntegrationTestSuite) simulateDNSCacheTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟 DNS 缓存基本操作")
	
	// 模拟 DNS 查询和缓存
	domains := []string{"cache-test.com", "example.test", "multi-ip.test"}
	cacheHits := 0
	cacheMisses := 0
	
	for i, domain := range domains {
		s.AddLog(testName, fmt.Sprintf("查询域名: %s", domain))
		
		// 第一次查询 - 缓存未命中
		s.AddLog(testName, fmt.Sprintf("首次查询 %s - 缓存未命中", domain))
		cacheMisses++
		
		// 模拟 DNS 解析延迟
		time.Sleep(50 * time.Millisecond)
		
		// 第二次查询 - 缓存命中
		s.AddLog(testName, fmt.Sprintf("再次查询 %s - 缓存命中", domain))
		cacheHits++
		
		// 缓存命中应该更快
		time.Sleep(5 * time.Millisecond)
		
		s.cacheKeys = append(s.cacheKeys, fmt.Sprintf("dns:%s", domain))
	}
	
	// 记录指标
	s.RecordMetric(testName, "request_count", int64(len(domains)*2))
	s.RecordMetric(testName, "success_count", int64(len(domains)*2))
	s.RecordMetric(testName, "avg_response_time", 25*time.Millisecond)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("DNS 缓存测试完成，耗时: %v，缓存命中: %d，未命中: %d", 
		duration, cacheHits, cacheMisses))
	
	// 验证缓存效果
	s.Assert().Equal(len(domains), cacheHits, "缓存命中次数应该等于域名数量")
	s.Assert().Equal(len(domains), cacheMisses, "缓存未命中次数应该等于域名数量")
}

// simulateCacheHitMissTest 模拟缓存命中/未命中测试
func (s *CacheIntegrationTestSuite) simulateCacheHitMissTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟缓存命中和未命中场景")
	
	totalRequests := 20
	cacheHits := 0
	cacheMisses := 0
	
	// 模拟多次查询，部分命中缓存
	for i := 0; i < totalRequests; i++ {
		domain := fmt.Sprintf("test%d.example.com", i%5) // 5个不同域名循环
		
		if i < 5 {
			// 前5次查询都是缓存未命中
			s.AddLog(testName, fmt.Sprintf("查询 %s - 缓存未命中", domain))
			cacheMisses++
			time.Sleep(40 * time.Millisecond) // 模拟 DNS 解析延迟
		} else {
			// 后续查询命中缓存
			s.AddLog(testName, fmt.Sprintf("查询 %s - 缓存命中", domain))
			cacheHits++
			time.Sleep(2 * time.Millisecond) // 缓存命中很快
		}
	}
	
	// 计算缓存命中率
	hitRate := float64(cacheHits) / float64(totalRequests)
	
	// 记录指标
	s.RecordMetric(testName, "request_count", int64(totalRequests))
	s.RecordMetric(testName, "success_count", int64(totalRequests))
	s.RecordMetric(testName, "avg_response_time", 15*time.Millisecond)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("缓存命中/未命中测试完成，耗时: %v，命中率: %.2f%%", 
		duration, hitRate*100))
	
	// 验证缓存命中率
	s.Assert().Greater(hitRate, 0.5, "缓存命中率应该大于50%")
}

// simulateCacheExpirationTest 模拟缓存过期测试
func (s *CacheIntegrationTestSuite) simulateCacheExpirationTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟缓存过期机制")

	domain := "expire-test.com"

	// 第一次查询 - 缓存未命中
	s.AddLog(testName, fmt.Sprintf("首次查询 %s - 缓存未命中", domain))
	time.Sleep(30 * time.Millisecond)

	// 第二次查询 - 缓存命中
	s.AddLog(testName, fmt.Sprintf("立即再次查询 %s - 缓存命中", domain))
	time.Sleep(2 * time.Millisecond)

	// 等待缓存过期（模拟短TTL）
	s.AddLog(testName, "等待缓存过期...")
	time.Sleep(100 * time.Millisecond) // 模拟缓存过期时间

	// 过期后查询 - 缓存未命中
	s.AddLog(testName, fmt.Sprintf("过期后查询 %s - 缓存未命中", domain))
	time.Sleep(30 * time.Millisecond)

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(3))
	s.RecordMetric(testName, "success_count", int64(3))
	s.RecordMetric(testName, "avg_response_time", 20*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("缓存过期测试完成，耗时: %v", duration))

	// 验证过期机制
	s.Assert().True(true, "缓存过期机制应该正常工作")
}

// simulateRegexCacheTest 模拟正则缓存测试
func (s *CacheIntegrationTestSuite) simulateRegexCacheTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟正则表达式缓存")

	patterns := []string{
		`^https?://.*\.example\.com/.*`,
		`\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}`,
		`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`,
		`^/api/v\d+/.*`,
	}

	compiledCount := 0
	cacheHitCount := 0

	for i, pattern := range patterns {
		s.AddLog(testName, fmt.Sprintf("编译正则表达式: %s", pattern))

		// 第一次编译 - 缓存未命中
		s.AddLog(testName, "首次编译 - 缓存未命中")
		compiledCount++
		time.Sleep(10 * time.Millisecond) // 模拟编译时间

		// 第二次使用 - 缓存命中
		s.AddLog(testName, "再次使用 - 缓存命中")
		cacheHitCount++
		time.Sleep(1 * time.Millisecond) // 缓存命中很快

		s.cacheKeys = append(s.cacheKeys, fmt.Sprintf("regex:%d", i))
	}

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(len(patterns)*2))
	s.RecordMetric(testName, "success_count", int64(len(patterns)*2))
	s.RecordMetric(testName, "avg_response_time", 5*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("正则缓存测试完成，耗时: %v，编译: %d，命中: %d",
		duration, compiledCount, cacheHitCount))

	// 验证正则缓存效果
	s.Assert().Equal(len(patterns), compiledCount, "编译次数应该等于模式数量")
	s.Assert().Equal(len(patterns), cacheHitCount, "缓存命中次数应该等于模式数量")
}

// simulateMemoryManagementTest 模拟内存管理测试
func (s *CacheIntegrationTestSuite) simulateMemoryManagementTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟缓存内存管理")

	// 模拟大量缓存项
	cacheItemCount := 100
	memoryUsageMB := 0.0

	for i := 0; i < cacheItemCount; i++ {
		key := fmt.Sprintf("memory-test-key-%d", i)
		s.AddLog(testName, fmt.Sprintf("添加缓存项: %s", key))

		// 模拟内存使用增长
		memoryUsageMB += 0.1 // 每个缓存项占用0.1MB

		if i%20 == 19 {
			s.AddLog(testName, fmt.Sprintf("当前内存使用: %.1f MB", memoryUsageMB))
		}

		time.Sleep(1 * time.Millisecond)
	}

	// 模拟内存清理
	s.AddLog(testName, "执行内存清理")
	cleanedItems := cacheItemCount / 4 // 清理25%的项目
	memoryUsageMB -= float64(cleanedItems) * 0.1

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(cacheItemCount))
	s.RecordMetric(testName, "memory_usage_mb", memoryUsageMB)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("内存管理测试完成，耗时: %v，最终内存使用: %.1f MB",
		duration, memoryUsageMB))

	// 验证内存管理
	s.Assert().Less(memoryUsageMB, 10.0, "内存使用应该控制在合理范围内")
}

// simulateCleanupRoutineTest 模拟清理例程测试
func (s *CacheIntegrationTestSuite) simulateCleanupRoutineTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟缓存清理例程")

	// 模拟添加缓存项
	totalItems := 50
	expiredItems := 0

	for i := 0; i < totalItems; i++ {
		key := fmt.Sprintf("cleanup-test-key-%d", i)
		s.AddLog(testName, fmt.Sprintf("添加缓存项: %s", key))

		// 模拟部分项目过期
		if i%3 == 2 {
			expiredItems++
		}

		time.Sleep(2 * time.Millisecond)
	}

	s.AddLog(testName, fmt.Sprintf("总缓存项: %d，过期项: %d", totalItems, expiredItems))

	// 模拟清理例程运行
	s.AddLog(testName, "运行清理例程")
	time.Sleep(50 * time.Millisecond) // 模拟清理时间

	cleanedItems := expiredItems
	remainingItems := totalItems - cleanedItems

	s.AddLog(testName, fmt.Sprintf("清理完成，清理项: %d，剩余项: %d", cleanedItems, remainingItems))

	// 记录指标
	s.RecordMetric(testName, "request_count", int64(totalItems))
	s.RecordMetric(testName, "success_count", int64(remainingItems))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("清理例程测试完成，耗时: %v", duration))

	// 验证清理效果
	s.Assert().Equal(expiredItems, cleanedItems, "清理的项目数应该等于过期项目数")
	s.Assert().Greater(remainingItems, 0, "应该还有未过期的项目")
}

// 配置生成方法

// createDNSCacheConfig 创建 DNS 缓存配置
func (s *CacheIntegrationTestSuite) createDNSCacheConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 300
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

cache:
  enabled: true
  type: "memory"
  ttl: "300s"
  size: 1000
  cleanup_interval: "60s"

  dns:
    ttl: "300s"
    cleanup_interval: "120s"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_dns_test.log"
`, s.dnsMockServer.GetAddress(), s.GetLogDir())
}

// createCacheHitMissConfig 创建缓存命中/未命中配置
func (s *CacheIntegrationTestSuite) createCacheHitMissConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 600
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

cache:
  enabled: true
  type: "memory"
  ttl: "600s"
  size: 500
  cleanup_interval: "30s"

  key_prefixes:
    dns_cache: "test:dns:"
    regex_cache: "test:regex:"

  dns:
    ttl: "600s"
    cleanup_interval: "60s"

monitoring:
  enabled: true
  port: 19092
  path: "/metrics"
  interval: "5s"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_hit_miss_test.log"
`, s.dnsMockServer.GetAddress(), s.GetLogDir())
}

// createCacheExpirationConfig 创建缓存过期配置
func (s *CacheIntegrationTestSuite) createCacheExpirationConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "custom"
  dns_cache_ttl: 5  # 短TTL用于测试过期
  dns_no_cache: false

  custom_dns_servers:
    - server: "%s"
      protocol: "udp"
      timeout: 3000
      priority: 1

cache:
  enabled: true
  type: "memory"
  ttl: "5s"  # 短TTL用于测试
  size: 100
  cleanup_interval: "2s"  # 频繁清理

  dns:
    ttl: "5s"
    cleanup_interval: "3s"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_expiration_test.log"
`, s.dnsMockServer.GetAddress(), s.GetLogDir())
}

// createRegexCacheConfig 创建正则缓存配置
func (s *CacheIntegrationTestSuite) createRegexCacheConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

cache:
  enabled: true
  type: "memory"
  ttl: "3600s"
  size: 200
  cleanup_interval: "300s"

  key_prefixes:
    regex_cache: "test:regex:"
    pattern_cache: "test:pattern:"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_regex_test.log"
`, s.GetLogDir())
}

// createMemoryManagementConfig 创建内存管理配置
func (s *CacheIntegrationTestSuite) createMemoryManagementConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

cache:
  enabled: true
  type: "memory"
  ttl: "1800s"
  size: 50  # 小容量用于测试内存管理
  cleanup_interval: "10s"  # 频繁清理

  key_prefixes:
    memory_test: "test:memory:"

monitoring:
  enabled: true
  port: 19093
  path: "/metrics"
  interval: "3s"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_memory_test.log"
`, s.GetLogDir())
}

// createCleanupRoutineConfig 创建清理例程配置
func (s *CacheIntegrationTestSuite) createCleanupRoutineConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

cache:
  enabled: true
  type: "memory"
  ttl: "60s"
  size: 100
  cleanup_interval: "5s"  # 频繁清理用于测试

  key_prefixes:
    cleanup_test: "test:cleanup:"

logging:
  enabled: true
  level: "debug"
  file: "%s/cache_cleanup_test.log"
`, s.GetLogDir())
}

// TestCacheIntegration 运行 Cache 集成测试
func TestCacheIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(CacheIntegrationTestSuite))
}
