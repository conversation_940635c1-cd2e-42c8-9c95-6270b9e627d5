// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"testing"
	"time"

	"github.com/flexp/flexp/tests/integration/framework"
	"github.com/stretchr/testify/suite"
)

// MonitoringIntegrationTestSuite Monitoring 模块集成测试套件
type MonitoringIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	httpMockServer *framework.HTTPMockServer
	testUtils      *framework.TestUtils
	monitoringPort int
	metricsData    map[string]interface{}
	healthChecks   map[string]bool
}

// SetupSuite 测试套件初始化
func (s *MonitoringIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.metricsData = make(map[string]interface{})
	s.healthChecks = make(map[string]bool)
	
	// 分配监控端口
	port, err := s.testUtils.FindFreePort()
	s.Require().NoError(err, "分配监控端口失败")
	s.monitoringPort = port
	
	// 创建 HTTP Mock 服务器用于健康检查测试
	s.httpMockServer, err = s.GetMockManager().CreateHTTPMockServer("monitoring_target", 0)
	s.Require().NoError(err, "创建 HTTP Mock 服务器失败")
	
	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("Monitoring 集成测试环境初始化完成，监控端口: %d", s.monitoringPort)
}

// TestMetricsCollection 测试指标收集
func (s *MonitoringIntegrationTestSuite) TestMetricsCollection() {
	testName := "TestMetricsCollection"
	s.AddLog(testName, "开始测试指标收集")
	
	// 创建指标收集配置
	configContent := s.createMetricsCollectionConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_metrics.yaml", configContent)
	s.Require().NoError(err, "创建指标收集配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟指标收集测试
	s.simulateMetricsCollectionTest(testName)
	
	s.AddLog(testName, "指标收集测试完成")
}

// TestHealthChecks 测试健康检查
func (s *MonitoringIntegrationTestSuite) TestHealthChecks() {
	testName := "TestHealthChecks"
	s.AddLog(testName, "开始测试健康检查")
	
	// 创建健康检查配置
	configContent := s.createHealthCheckConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_health.yaml", configContent)
	s.Require().NoError(err, "创建健康检查配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟健康检查测试
	s.simulateHealthCheckTest(testName)
	
	s.AddLog(testName, "健康检查测试完成")
}

// TestSystemMetrics 测试系统指标
func (s *MonitoringIntegrationTestSuite) TestSystemMetrics() {
	testName := "TestSystemMetrics"
	s.AddLog(testName, "开始测试系统指标")
	
	// 创建系统指标配置
	configContent := s.createSystemMetricsConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_system.yaml", configContent)
	s.Require().NoError(err, "创建系统指标配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟系统指标测试
	s.simulateSystemMetricsTest(testName)
	
	s.AddLog(testName, "系统指标测试完成")
}

// TestProxyMetrics 测试代理指标
func (s *MonitoringIntegrationTestSuite) TestProxyMetrics() {
	testName := "TestProxyMetrics"
	s.AddLog(testName, "开始测试代理指标")
	
	// 创建代理指标配置
	configContent := s.createProxyMetricsConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_proxy.yaml", configContent)
	s.Require().NoError(err, "创建代理指标配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟代理指标测试
	s.simulateProxyMetricsTest(testName)
	
	s.AddLog(testName, "代理指标测试完成")
}

// TestHTTPMonitoringServer 测试 HTTP 监控服务器
func (s *MonitoringIntegrationTestSuite) TestHTTPMonitoringServer() {
	testName := "TestHTTPMonitoringServer"
	s.AddLog(testName, "开始测试 HTTP 监控服务器")
	
	// 创建 HTTP 监控服务器配置
	configContent := s.createHTTPMonitoringConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_http.yaml", configContent)
	s.Require().NoError(err, "创建 HTTP 监控服务器配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 HTTP 监控服务器测试
	s.simulateHTTPMonitoringTest(testName)
	
	s.AddLog(testName, "HTTP 监控服务器测试完成")
}

// TestAlertingSystem 测试告警系统
func (s *MonitoringIntegrationTestSuite) TestAlertingSystem() {
	testName := "TestAlertingSystem"
	s.AddLog(testName, "开始测试告警系统")
	
	// 创建告警系统配置
	configContent := s.createAlertingConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("monitoring_alerting.yaml", configContent)
	s.Require().NoError(err, "创建告警系统配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟告警系统测试
	s.simulateAlertingTest(testName)
	
	s.AddLog(testName, "告警系统测试完成")
}

// 模拟测试方法

// simulateMetricsCollectionTest 模拟指标收集测试
func (s *MonitoringIntegrationTestSuite) simulateMetricsCollectionTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟指标收集")
	
	// 模拟收集各种指标
	metrics := map[string]interface{}{
		"http_requests_total":     1000,
		"http_request_duration":   0.25,
		"proxy_connections_active": 50,
		"cache_hit_ratio":         0.85,
		"dns_queries_total":       500,
		"memory_usage_bytes":      1024 * 1024 * 128, // 128MB
		"cpu_usage_percent":       45.5,
		"goroutines_count":        25,
	}
	
	collectedCount := 0
	for metricName, value := range metrics {
		s.AddLog(testName, fmt.Sprintf("收集指标: %s = %v", metricName, value))
		s.metricsData[metricName] = value
		collectedCount++
		time.Sleep(10 * time.Millisecond)
	}
	
	// 模拟指标聚合
	s.AddLog(testName, "执行指标聚合")
	avgResponseTime := 0.25
	totalRequests := 1000
	errorRate := 0.02
	
	s.metricsData["avg_response_time"] = avgResponseTime
	s.metricsData["total_requests"] = totalRequests
	s.metricsData["error_rate"] = errorRate
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(collectedCount))
	s.RecordMetric(testName, "success_count", int64(collectedCount))
	s.RecordMetric(testName, "avg_response_time", time.Duration(avgResponseTime*1000)*time.Millisecond)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("指标收集测试完成，耗时: %v，收集指标数: %d", 
		duration, collectedCount))
	
	// 验证指标收集
	s.Assert().Equal(len(metrics)+3, len(s.metricsData), "应该收集所有指标")
	s.Assert().Contains(s.metricsData, "http_requests_total", "应该包含 HTTP 请求总数指标")
	s.Assert().Contains(s.metricsData, "memory_usage_bytes", "应该包含内存使用指标")
}

// simulateHealthCheckTest 模拟健康检查测试
func (s *MonitoringIntegrationTestSuite) simulateHealthCheckTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟健康检查")
	
	// 模拟多个组件的健康检查
	components := []string{
		"server",
		"proxy_manager", 
		"cache_service",
		"dns_service",
		"monitoring_service",
	}
	
	healthyCount := 0
	unhealthyCount := 0
	
	for i, component := range components {
		s.AddLog(testName, fmt.Sprintf("检查组件健康状态: %s", component))
		
		// 模拟健康检查结果（大部分健康）
		isHealthy := i != 2 // 第3个组件模拟不健康
		s.healthChecks[component] = isHealthy
		
		if isHealthy {
			healthyCount++
			s.AddLog(testName, fmt.Sprintf("组件 %s: 健康", component))
		} else {
			unhealthyCount++
			s.AddLog(testName, fmt.Sprintf("组件 %s: 不健康", component))
		}
		
		time.Sleep(20 * time.Millisecond) // 模拟健康检查时间
	}
	
	// 计算整体健康状态
	overallHealth := float64(healthyCount) / float64(len(components))
	s.AddLog(testName, fmt.Sprintf("整体健康状态: %.2f%% (%d/%d)", 
		overallHealth*100, healthyCount, len(components)))
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(components)))
	s.RecordMetric(testName, "success_count", int64(healthyCount))
	s.RecordMetric(testName, "failure_count", int64(unhealthyCount))
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("健康检查测试完成，耗时: %v", duration))
	
	// 验证健康检查
	s.Assert().Greater(healthyCount, unhealthyCount, "健康组件应该多于不健康组件")
	s.Assert().Equal(len(components), len(s.healthChecks), "应该检查所有组件")
}

// simulateSystemMetricsTest 模拟系统指标测试
func (s *MonitoringIntegrationTestSuite) simulateSystemMetricsTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟系统指标收集")

	// 模拟系统指标收集
	systemMetrics := map[string]interface{}{
		"cpu_usage_percent":    65.5,
		"memory_usage_percent": 78.2,
		"disk_usage_percent":   45.0,
		"network_in_bytes":     1024 * 1024 * 50,  // 50MB
		"network_out_bytes":    1024 * 1024 * 30,  // 30MB
		"load_average_1m":      1.25,
		"load_average_5m":      1.15,
		"load_average_15m":     1.05,
		"open_files":           256,
		"tcp_connections":      128,
	}

	for metricName, value := range systemMetrics {
		s.AddLog(testName, fmt.Sprintf("收集系统指标: %s = %v", metricName, value))
		s.metricsData[metricName] = value
		time.Sleep(5 * time.Millisecond)
	}

	// 模拟系统资源告警检查
	cpuUsage := systemMetrics["cpu_usage_percent"].(float64)
	memoryUsage := systemMetrics["memory_usage_percent"].(float64)

	alerts := make([]string, 0)
	if cpuUsage > 80 {
		alerts = append(alerts, "CPU使用率过高")
	}
	if memoryUsage > 90 {
		alerts = append(alerts, "内存使用率过高")
	}

	s.AddLog(testName, fmt.Sprintf("系统告警检查完成，触发告警: %d 个", len(alerts)))

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(systemMetrics)))
	s.RecordMetric(testName, "success_count", int64(len(systemMetrics)))
	s.RecordMetric(testName, "cpu_usage_percent", cpuUsage)
	s.RecordMetric(testName, "memory_usage_mb", memoryUsage)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("系统指标测试完成，耗时: %v", duration))

	// 验证系统指标
	s.Assert().Contains(s.metricsData, "cpu_usage_percent", "应该包含 CPU 使用率")
	s.Assert().Contains(s.metricsData, "memory_usage_percent", "应该包含内存使用率")
	s.Assert().LessOrEqual(len(alerts), 2, "告警数量应该在合理范围内")
}

// simulateProxyMetricsTest 模拟代理指标测试
func (s *MonitoringIntegrationTestSuite) simulateProxyMetricsTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟代理指标收集")

	// 模拟代理相关指标
	proxyMetrics := map[string]interface{}{
		"proxy_requests_total":      2500,
		"proxy_requests_success":    2350,
		"proxy_requests_failed":     150,
		"proxy_response_time_avg":   0.35,
		"proxy_response_time_p95":   0.85,
		"proxy_response_time_p99":   1.25,
		"proxy_connections_active":  75,
		"proxy_connections_total":   1200,
		"proxy_bandwidth_in":        1024 * 1024 * 100, // 100MB
		"proxy_bandwidth_out":       1024 * 1024 * 95,  // 95MB
		"proxy_pool_size":           20,
		"proxy_pool_healthy":        18,
		"proxy_rotation_count":      45,
		"proxy_failover_count":      3,
	}

	for metricName, value := range proxyMetrics {
		s.AddLog(testName, fmt.Sprintf("收集代理指标: %s = %v", metricName, value))
		s.metricsData[metricName] = value
		time.Sleep(8 * time.Millisecond)
	}

	// 计算代理性能指标
	totalRequests := proxyMetrics["proxy_requests_total"].(int)
	successRequests := proxyMetrics["proxy_requests_success"].(int)
	failedRequests := proxyMetrics["proxy_requests_failed"].(int)

	successRate := float64(successRequests) / float64(totalRequests)
	errorRate := float64(failedRequests) / float64(totalRequests)

	s.metricsData["proxy_success_rate"] = successRate
	s.metricsData["proxy_error_rate"] = errorRate

	s.AddLog(testName, fmt.Sprintf("代理成功率: %.2f%%, 错误率: %.2f%%",
		successRate*100, errorRate*100))

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(proxyMetrics)))
	s.RecordMetric(testName, "success_count", int64(len(proxyMetrics)))
	s.RecordMetric(testName, "error_rate", errorRate)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("代理指标测试完成，耗时: %v", duration))

	// 验证代理指标
	s.Assert().Contains(s.metricsData, "proxy_requests_total", "应该包含代理请求总数")
	s.Assert().Contains(s.metricsData, "proxy_success_rate", "应该包含代理成功率")
	s.Assert().Greater(successRate, 0.8, "代理成功率应该大于80%")
}

// simulateHTTPMonitoringTest 模拟 HTTP 监控服务器测试
func (s *MonitoringIntegrationTestSuite) simulateHTTPMonitoringTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 HTTP 监控服务器")

	// 模拟启动 HTTP 监控服务器
	monitoringURL := fmt.Sprintf("http://127.0.0.1:%d", s.monitoringPort)
	s.AddLog(testName, fmt.Sprintf("启动监控服务器: %s", monitoringURL))

	// 模拟监控端点
	endpoints := []string{
		"/metrics",
		"/health",
		"/status",
		"/stats",
	}

	successfulRequests := 0
	for _, endpoint := range endpoints {
		url := monitoringURL + endpoint
		s.AddLog(testName, fmt.Sprintf("测试监控端点: %s", url))

		// 模拟 HTTP 请求（实际环境中会发送真实请求）
		// 这里模拟请求成功
		s.AddLog(testName, fmt.Sprintf("端点 %s 响应正常", endpoint))
		successfulRequests++

		time.Sleep(15 * time.Millisecond)
	}

	// 模拟指标导出
	s.AddLog(testName, "导出 Prometheus 格式指标")
	prometheusMetrics := []string{
		"# HELP http_requests_total Total HTTP requests",
		"# TYPE http_requests_total counter",
		"http_requests_total{method=\"GET\",status=\"200\"} 1000",
		"# HELP proxy_connections_active Active proxy connections",
		"# TYPE proxy_connections_active gauge",
		"proxy_connections_active 75",
	}

	for _, metric := range prometheusMetrics {
		s.AddLog(testName, fmt.Sprintf("导出指标: %s", metric))
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(endpoints)))
	s.RecordMetric(testName, "success_count", int64(successfulRequests))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("HTTP 监控服务器测试完成，耗时: %v", duration))

	// 验证 HTTP 监控服务器
	s.Assert().Equal(len(endpoints), successfulRequests, "所有监控端点都应该响应正常")
}

// simulateAlertingTest 模拟告警系统测试
func (s *MonitoringIntegrationTestSuite) simulateAlertingTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟告警系统")

	// 模拟告警规则
	alertRules := []struct {
		name      string
		condition string
		threshold float64
		value     float64
		triggered bool
	}{
		{"CPU使用率过高", "cpu_usage > 80", 80.0, 85.5, true},
		{"内存使用率过高", "memory_usage > 90", 90.0, 75.2, false},
		{"错误率过高", "error_rate > 0.05", 0.05, 0.02, false},
		{"响应时间过长", "response_time > 1.0", 1.0, 1.25, true},
		{"代理连接数过多", "proxy_connections > 100", 100.0, 75.0, false},
	}

	triggeredAlerts := 0
	for _, rule := range alertRules {
		s.AddLog(testName, fmt.Sprintf("检查告警规则: %s", rule.name))
		s.AddLog(testName, fmt.Sprintf("条件: %s, 阈值: %.2f, 当前值: %.2f",
			rule.condition, rule.threshold, rule.value))

		if rule.triggered {
			triggeredAlerts++
			s.AddLog(testName, fmt.Sprintf("🚨 告警触发: %s", rule.name))

			// 模拟发送告警通知
			s.AddLog(testName, fmt.Sprintf("发送告警通知: %s", rule.name))
		} else {
			s.AddLog(testName, fmt.Sprintf("✅ 规则正常: %s", rule.name))
		}

		time.Sleep(10 * time.Millisecond)
	}

	// 模拟告警历史记录
	s.AddLog(testName, fmt.Sprintf("记录告警历史，触发告警: %d 个", triggeredAlerts))

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(alertRules)))
	s.RecordMetric(testName, "success_count", int64(len(alertRules)))
	s.RecordMetric(testName, "failure_count", int64(triggeredAlerts))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("告警系统测试完成，耗时: %v，触发告警: %d 个",
		duration, triggeredAlerts))

	// 验证告警系统
	s.Assert().Greater(len(alertRules), 0, "应该有告警规则")
	s.Assert().LessOrEqual(triggeredAlerts, len(alertRules), "触发的告警不应该超过规则总数")
}

// 配置生成方法

// createMetricsCollectionConfig 创建指标收集配置
func (s *MonitoringIntegrationTestSuite) createMetricsCollectionConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "5s"

  metrics:
    enabled: true
    namespace: "flexproxy_test"
    subsystem: "integration"

  labels:
    service: "flexproxy-test"
    version: "test"
    environment: "integration"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
  file: "%s/monitoring_metrics_test.log"
`, s.monitoringPort, s.GetLogDir())
}

// createHealthCheckConfig 创建健康检查配置
func (s *MonitoringIntegrationTestSuite) createHealthCheckConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "3s"

  health_checks:
    enabled: true
    interval: "10s"
    timeout: "5s"
    endpoints:
      - name: "target_server"
        url: "http://%s/health"
        method: "GET"
        expected_status: 200
      - name: "local_service"
        url: "http://127.0.0.1:18080/status"
        method: "GET"
        expected_status: 200

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/monitoring_health_test.log"
`, s.monitoringPort, s.httpMockServer.GetAddress(), s.GetLogDir())
}

// createSystemMetricsConfig 创建系统指标配置
func (s *MonitoringIntegrationTestSuite) createSystemMetricsConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "2s"

  system_metrics:
    enabled: true
    collect_interval: "5s"
    metrics:
      - cpu_usage
      - memory_usage
      - disk_usage
      - network_io
      - load_average
      - open_files
      - tcp_connections

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
  file: "%s/monitoring_system_test.log"
`, s.monitoringPort, s.GetLogDir())
}

// createProxyMetricsConfig 创建代理指标配置
func (s *MonitoringIntegrationTestSuite) createProxyMetricsConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  proxy_file: "./test_data/proxies.txt"
  ip_rotation_mode: "sequential"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "3s"

  proxy_metrics:
    enabled: true
    collect_interval: "5s"
    metrics:
      - requests_total
      - requests_success
      - requests_failed
      - response_time
      - connections_active
      - bandwidth
      - pool_status
      - rotation_count
      - failover_count

proxy:
  enabled: true
  strategy: "round_robin"
  max_retries: 2
  retry_interval: "500ms"
  pool_size: 20

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
  file: "%s/monitoring_proxy_test.log"
`, s.monitoringPort, s.GetLogDir())
}

// createHTTPMonitoringConfig 创建 HTTP 监控配置
func (s *MonitoringIntegrationTestSuite) createHTTPMonitoringConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "1s"

  http_server:
    enabled: true
    endpoints:
      - path: "/metrics"
        handler: "prometheus"
      - path: "/health"
        handler: "health_check"
      - path: "/status"
        handler: "status"
      - path: "/stats"
        handler: "statistics"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "debug"
  file: "%s/monitoring_http_test.log"
`, s.monitoringPort, s.GetLogDir())
}

// createAlertingConfig 创建告警配置
func (s *MonitoringIntegrationTestSuite) createAlertingConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

monitoring:
  enabled: true
  port: %d
  path: "/metrics"
  interval: "5s"

  alerting:
    enabled: true
    check_interval: "10s"
    rules:
      - name: "cpu_usage_high"
        condition: "cpu_usage_percent > 80"
        severity: "warning"
        message: "CPU使用率过高: {{.value}}%%"
      - name: "memory_usage_high"
        condition: "memory_usage_percent > 90"
        severity: "critical"
        message: "内存使用率过高: {{.value}}%%"
      - name: "error_rate_high"
        condition: "error_rate > 0.05"
        severity: "warning"
        message: "错误率过高: {{.value}}"
      - name: "response_time_high"
        condition: "avg_response_time > 1.0"
        severity: "warning"
        message: "响应时间过长: {{.value}}s"

    notifications:
      - type: "log"
        enabled: true
      - type: "webhook"
        enabled: false
        url: "http://localhost:9093/webhook"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
  file: "%s/monitoring_alerting_test.log"
`, s.monitoringPort, s.GetLogDir())
}

// TestMonitoringIntegration 运行 Monitoring 集成测试
func TestMonitoringIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(MonitoringIntegrationTestSuite))
}
