package modules

// ActionType 动作类型常量
type ActionType string

// 基础动作类型 (8种)
const (
	ActionTypeLog            ActionType = "log"             // 日志记录动作
	ActionTypeBanIP          ActionType = "banip"           // IP封禁动作
	ActionTypeBanDomain      ActionType = "ban_domain"      // 域名封禁动作
	ActionTypeBlockRequest   ActionType = "block_request"   // 请求阻止动作
	ActionTypeModifyRequest  ActionType = "modify_request"  // 请求修改动作
	ActionTypeModifyResponse ActionType = "modify_response" // 响应修改动作
	ActionTypeCacheResponse  ActionType = "cache_response"  // 响应缓存动作
	ActionTypeScript         ActionType = "script"          // 脚本执行动作
)

// 扩展动作类型 (8种)
const (
	ActionTypeRetry        ActionType = "retry"          // 新代理重试动作
	ActionTypeRetrySame    ActionType = "retry_same"     // 相同代理重试动作
	ActionTypeSaveToPool   ActionType = "save_to_pool"   // 保存到代理池动作
	ActionTypeCache        ActionType = "cache"          // 缓存动作
	ActionTypeRequestURL   ActionType = "request_url"    // URL请求动作
	ActionTypeBanIPDomain  ActionType = "banipdomain"    // IP域名封禁动作
	ActionTypeNullResponse ActionType = "null_response"  // 空响应动作
	ActionTypeBypassProxy  ActionType = "bypass_proxy"   // 绕过代理动作
)

// TriggerType 触发器类型常量
type TriggerType string

// 触发器类型 (12种)
const (
	TriggerTypeStatus         TriggerType = "status"          // HTTP状态码触发器
	TriggerTypeBody           TriggerType = "body"            // 响应内容触发器
	TriggerTypeMaxRequestTime TriggerType = "max_request_time" // 最大请求时间触发器
	TriggerTypeConnTimeOut    TriggerType = "conn_time_out"   // 连接超时触发器
	TriggerTypeMinRequestTime TriggerType = "min_request_time" // 最小请求时间触发器
	TriggerTypeURL            TriggerType = "url"             // URL匹配触发器
	TriggerTypeDomain         TriggerType = "domain"          // 域名匹配触发器
	TriggerTypeCombined       TriggerType = "combined"        // 组合条件触发器
	TriggerTypeCustom         TriggerType = "custom"          // 自定义触发器
	TriggerTypeRequestBody    TriggerType = "request_body"    // 请求内容触发器
	TriggerTypeRequestHeader  TriggerType = "request_header"  // 请求头触发器
	TriggerTypeResponseHeader TriggerType = "response_header" // 响应头触发器
)

// ProcessStage 处理阶段常量
type ProcessStage string

const (
	ProcessStagePreRequest  ProcessStage = "pre_request"  // 请求前处理
	ProcessStagePostHeader  ProcessStage = "post_header"  // 响应头后处理
	ProcessStagePostBody    ProcessStage = "post_body"    // 响应体后处理
	ProcessStagePreResponse ProcessStage = "pre_response" // 响应前处理
)

// ContentFormat 内容格式常量
type ContentFormat string

const (
	FormatJSON       ContentFormat = "json"        // JSON格式
	FormatXML        ContentFormat = "xml"         // XML格式
	FormatHTML       ContentFormat = "html"        // HTML格式
	FormatText       ContentFormat = "text"        // 文本格式
	FormatURLEncoded ContentFormat = "urlencoded"  // URL编码格式
	FormatBinary     ContentFormat = "binary"      // 二进制格式
)

// LogLevel 日志级别常量
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug" // 调试级别
	LogLevelInfo  LogLevel = "info"  // 信息级别
	LogLevelWarn  LogLevel = "warn"  // 警告级别
	LogLevelError LogLevel = "error" // 错误级别
	LogLevelFatal LogLevel = "fatal" // 致命级别
)

// HTTPMethod HTTP方法常量
type HTTPMethod string

const (
	MethodGET     HTTPMethod = "GET"     // GET方法
	MethodPOST    HTTPMethod = "POST"    // POST方法
	MethodPUT     HTTPMethod = "PUT"     // PUT方法
	MethodDELETE  HTTPMethod = "DELETE"  // DELETE方法
	MethodPATCH   HTTPMethod = "PATCH"   // PATCH方法
	MethodHEAD    HTTPMethod = "HEAD"    // HEAD方法
	MethodOPTIONS HTTPMethod = "OPTIONS" // OPTIONS方法
)

// HTTPStatusCode HTTP状态码常量
type HTTPStatusCode int

const (
	StatusOK                  HTTPStatusCode = 200 // 成功
	StatusCreated             HTTPStatusCode = 201 // 已创建
	StatusNoContent           HTTPStatusCode = 204 // 无内容
	StatusMovedPermanently    HTTPStatusCode = 301 // 永久重定向
	StatusFound               HTTPStatusCode = 302 // 临时重定向
	StatusBadRequest          HTTPStatusCode = 400 // 错误请求
	StatusUnauthorized        HTTPStatusCode = 401 // 未授权
	StatusForbidden           HTTPStatusCode = 403 // 禁止访问
	StatusNotFound            HTTPStatusCode = 404 // 未找到
	StatusMethodNotAllowed    HTTPStatusCode = 405 // 方法不允许
	StatusRequestTimeout      HTTPStatusCode = 408 // 请求超时
	StatusInternalServerError HTTPStatusCode = 500 // 内部服务器错误
	StatusBadGateway          HTTPStatusCode = 502 // 网关错误
	StatusServiceUnavailable  HTTPStatusCode = 503 // 服务不可用
	StatusGatewayTimeout      HTTPStatusCode = 504 // 网关超时
)

// ActionExecutionResult 动作执行结果
type ActionExecutionResult struct {
	Success   bool                   `json:"success"`
	Error     string                 `json:"error,omitempty"`
	Duration  int64                  `json:"duration_ms"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timestamp int64                  `json:"timestamp"`
}

// TriggerMatchResult 触发器匹配结果
type TriggerMatchResult struct {
	Matched     bool                   `json:"matched"`
	TriggerType TriggerType            `json:"trigger_type"`
	Priority    int                    `json:"priority"`
	Stage       ProcessStage           `json:"stage"`
	Actions     []ActionConfig         `json:"actions"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   int64                  `json:"timestamp"`
}

// ActionConfig 动作配置
type ActionConfig struct {
	Type        ActionType             `json:"type" yaml:"type"`
	Params      map[string]interface{} `json:"params" yaml:"params"`
	Description string                 `json:"description,omitempty" yaml:"description,omitempty"`
	Enabled     bool                   `json:"enabled" yaml:"enabled"`
	Priority    int                    `json:"priority" yaml:"priority"`
}

// TriggerConfig 触发器配置
type TriggerConfig struct {
	Type         TriggerType    `json:"type" yaml:"type"`
	Stage        ProcessStage   `json:"stage" yaml:"stage"`
	Priority     int            `json:"priority" yaml:"priority"`
	Enabled      bool           `json:"enabled" yaml:"enabled"`
	Conditions   []Condition    `json:"conditions" yaml:"conditions"`
	Actions      []ActionConfig `json:"actions" yaml:"actions"`
	Description  string         `json:"description,omitempty" yaml:"description,omitempty"`
}

// Condition 条件配置
type Condition struct {
	Type     string      `json:"type" yaml:"type"`
	Field    string      `json:"field,omitempty" yaml:"field,omitempty"`
	Operator string      `json:"operator" yaml:"operator"`
	Value    interface{} `json:"value" yaml:"value"`
	Pattern  string      `json:"pattern,omitempty" yaml:"pattern,omitempty"`
}

// EventSequence 事件序列配置
type EventSequence struct {
	Name        string          `json:"name" yaml:"name"`
	Description string          `json:"description,omitempty" yaml:"description,omitempty"`
	Enabled     bool            `json:"enabled" yaml:"enabled"`
	Triggers    []TriggerConfig `json:"triggers" yaml:"triggers"`
	Actions     []ActionConfig  `json:"actions" yaml:"actions"`
}

// TestScenario 测试场景配置
type TestScenario struct {
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Setup        map[string]interface{} `json:"setup"`
	Input        TestInput              `json:"input"`
	Expected     TestExpected           `json:"expected"`
	Cleanup      map[string]interface{} `json:"cleanup,omitempty"`
}

// TestInput 测试输入
type TestInput struct {
	Request     TestRequest            `json:"request"`
	Response    TestResponse           `json:"response,omitempty"`
	Context     map[string]interface{} `json:"context,omitempty"`
	RequestTime int64                  `json:"request_time_ms,omitempty"`
}

// TestRequest 测试请求
type TestRequest struct {
	Method  string            `json:"method"`
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers,omitempty"`
	Body    string            `json:"body,omitempty"`
}

// TestResponse 测试响应
type TestResponse struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers,omitempty"`
	Body       string            `json:"body,omitempty"`
}

// TestExpected 测试期望结果
type TestExpected struct {
	TriggersMatched []string                 `json:"triggers_matched"`
	ActionsExecuted []string                 `json:"actions_executed"`
	ModifiedRequest *TestRequest             `json:"modified_request,omitempty"`
	ModifiedResponse *TestResponse           `json:"modified_response,omitempty"`
	LogMessages     []string                 `json:"log_messages,omitempty"`
	Errors          []string                 `json:"errors,omitempty"`
	Metrics         map[string]interface{}   `json:"metrics,omitempty"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	ActionExecutionTime   map[ActionType]int64   `json:"action_execution_time_ms"`
	TriggerDetectionTime  map[TriggerType]int64  `json:"trigger_detection_time_ms"`
	TotalProcessingTime   int64                  `json:"total_processing_time_ms"`
	MemoryUsage          int64                  `json:"memory_usage_bytes"`
	ConcurrentRequests   int                    `json:"concurrent_requests"`
	ThroughputPerSecond  float64                `json:"throughput_per_second"`
}

// TestReport 测试报告
type TestReport struct {
	TestSuite       string                        `json:"test_suite"`
	StartTime       int64                         `json:"start_time"`
	EndTime         int64                         `json:"end_time"`
	Duration        int64                         `json:"duration_ms"`
	TotalTests      int                           `json:"total_tests"`
	PassedTests     int                           `json:"passed_tests"`
	FailedTests     int                           `json:"failed_tests"`
	SkippedTests    int                           `json:"skipped_tests"`
	Coverage        TestCoverage                  `json:"coverage"`
	Performance     PerformanceMetrics            `json:"performance"`
	TestResults     []TestResult                  `json:"test_results"`
	Errors          []string                      `json:"errors,omitempty"`
}

// TestCoverage 测试覆盖率
type TestCoverage struct {
	ActionsCovered   []ActionType  `json:"actions_covered"`
	TriggersCovered  []TriggerType `json:"triggers_covered"`
	ActionsTotal     int           `json:"actions_total"`
	TriggersTotal    int           `json:"triggers_total"`
	ActionsCoverage  float64       `json:"actions_coverage_percent"`
	TriggersCoverage float64       `json:"triggers_coverage_percent"`
	OverallCoverage  float64       `json:"overall_coverage_percent"`
}

// TestResult 单个测试结果
type TestResult struct {
	TestName    string                 `json:"test_name"`
	Status      string                 `json:"status"` // "passed", "failed", "skipped"
	Duration    int64                  `json:"duration_ms"`
	Error       string                 `json:"error,omitempty"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Timestamp   int64                  `json:"timestamp"`
}

// 默认配置值
const (
	DefaultActionTimeout     = 30000 // 30秒
	DefaultTriggerTimeout    = 5000  // 5秒
	DefaultMaxRetries        = 3
	DefaultRetryDelay        = 1000  // 1秒
	DefaultCacheTTL          = 3600  // 1小时
	DefaultBanDuration       = 86400 // 24小时
	DefaultMaxRequestTime    = 30000 // 30秒
	DefaultMinRequestTime    = 100   // 100毫秒
	DefaultConcurrentLimit   = 1000
	DefaultMemoryLimit       = 1024 * 1024 * 1024 // 1GB
)

// 测试配置
const (
	TestTimeout              = 60000 // 60秒
	TestConcurrentGoroutines = 100
	TestOperationsPerGoroutine = 10
	TestMaxMemoryUsage       = 100 * 1024 * 1024 // 100MB
	TestMinThroughput        = 100.0             // 100 req/s
)
