package modules

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"
)

// MockActionManager Mock动作管理器
type MockActionManager struct {
	executedActions []ActionExecutionResult
	mutex           sync.RWMutex
	logService      *MockLogService
}

// NewMockActionManager 创建Mock动作管理器
func NewMockActionManager(logService *MockLogService) *MockActionManager {
	return &MockActionManager{
		executedActions: make([]ActionExecutionResult, 0),
		logService:      logService,
	}
}

// ExecuteAction 执行动作
func (m *MockActionManager) ExecuteAction(ctx context.Context, actionType string, params map[string]interface{}) error {
	start := time.Now()
	
	var err error
	metadata := make(map[string]interface{})
	
	switch ActionType(actionType) {
	case ActionTypeLog:
		err = m.executeLogAction(params, metadata)
	case ActionTypeBanIP:
		err = m.executeBanIPAction(params, metadata)
	case ActionTypeBanDomain:
		err = m.executeBanDomainAction(params, metadata)
	case ActionTypeBlockRequest:
		err = m.executeBlockRequestAction(params, metadata)
	case ActionTypeModifyRequest:
		err = m.executeModifyRequestAction(params, metadata)
	case ActionTypeModifyResponse:
		err = m.executeModifyResponseAction(params, metadata)
	case ActionTypeCacheResponse:
		err = m.executeCacheResponseAction(params, metadata)
	case ActionTypeScript:
		err = m.executeScriptAction(params, metadata)
	case ActionTypeRetry:
		err = m.executeRetryAction(params, metadata)
	case ActionTypeRetrySame:
		err = m.executeRetrySameAction(params, metadata)
	case ActionTypeSaveToPool:
		err = m.executeSaveToPoolAction(params, metadata)
	case ActionTypeCache:
		err = m.executeCacheAction(params, metadata)
	case ActionTypeRequestURL:
		err = m.executeRequestURLAction(params, metadata)
	case ActionTypeBanIPDomain:
		err = m.executeBanIPDomainAction(params, metadata)
	case ActionTypeNullResponse:
		err = m.executeNullResponseAction(params, metadata)
	case ActionTypeBypassProxy:
		err = m.executeBypassProxyAction(params, metadata)
	default:
		err = fmt.Errorf("未知的动作类型: %s", actionType)
	}
	
	duration := time.Since(start)
	
	result := ActionExecutionResult{
		Success:   err == nil,
		Duration:  duration.Milliseconds(),
		Metadata:  metadata,
		Timestamp: time.Now().Unix(),
	}
	
	if err != nil {
		result.Error = err.Error()
	}
	
	m.mutex.Lock()
	m.executedActions = append(m.executedActions, result)
	m.mutex.Unlock()
	
	return err
}

// GetExecutedActions 获取已执行的动作
func (m *MockActionManager) GetExecutedActions() []ActionExecutionResult {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	result := make([]ActionExecutionResult, len(m.executedActions))
	copy(result, m.executedActions)
	return result
}

// ClearExecutedActions 清空已执行的动作记录
func (m *MockActionManager) ClearExecutedActions() {
	m.mutex.Lock()
	m.executedActions = m.executedActions[:0]
	m.mutex.Unlock()
}

// executeLogAction 执行日志动作
func (m *MockActionManager) executeLogAction(params map[string]interface{}, metadata map[string]interface{}) error {
	level, _ := params["level"].(string)
	message, _ := params["message"].(string)
	
	if level == "" {
		level = "info"
	}
	if message == "" {
		message = "默认日志消息"
	}
	
	metadata["level"] = level
	metadata["message"] = message
	
	if m.logService != nil {
		m.logService.Log(level, message)
	}
	
	return nil
}

// executeBanIPAction 执行IP封禁动作
func (m *MockActionManager) executeBanIPAction(params map[string]interface{}, metadata map[string]interface{}) error {
	ip, _ := params["ip"].(string)
	duration, _ := params["duration"].(int)
	reason, _ := params["reason"].(string)
	
	if ip == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	
	metadata["ip"] = ip
	metadata["duration"] = duration
	metadata["reason"] = reason
	metadata["banned_until"] = time.Now().Add(time.Duration(duration) * time.Second).Unix()
	
	return nil
}

// executeBanDomainAction 执行域名封禁动作
func (m *MockActionManager) executeBanDomainAction(params map[string]interface{}, metadata map[string]interface{}) error {
	domain, _ := params["domain"].(string)
	duration, _ := params["duration"].(int)
	reason, _ := params["reason"].(string)
	
	if domain == "" {
		return fmt.Errorf("域名不能为空")
	}
	
	metadata["domain"] = domain
	metadata["duration"] = duration
	metadata["reason"] = reason
	metadata["banned_until"] = time.Now().Add(time.Duration(duration) * time.Second).Unix()
	
	return nil
}

// executeBlockRequestAction 执行请求阻止动作
func (m *MockActionManager) executeBlockRequestAction(params map[string]interface{}, metadata map[string]interface{}) error {
	reason, _ := params["reason"].(string)
	statusCode, _ := params["status_code"].(int)
	
	if statusCode == 0 {
		statusCode = 403
	}
	
	metadata["reason"] = reason
	metadata["status_code"] = statusCode
	metadata["blocked"] = true
	
	return nil
}

// executeModifyRequestAction 执行请求修改动作
func (m *MockActionManager) executeModifyRequestAction(params map[string]interface{}, metadata map[string]interface{}) error {
	headers, _ := params["headers"].(map[string]interface{})
	body, _ := params["body"].(map[string]interface{})
	
	modifications := make(map[string]interface{})
	
	if headers != nil {
		modifications["headers"] = headers
	}
	
	if body != nil {
		modifications["body"] = body
	}
	
	metadata["modifications"] = modifications
	metadata["modified"] = true
	
	return nil
}

// executeModifyResponseAction 执行响应修改动作
func (m *MockActionManager) executeModifyResponseAction(params map[string]interface{}, metadata map[string]interface{}) error {
	headers, _ := params["headers"].(map[string]interface{})
	body, _ := params["body"].(map[string]interface{})
	statusCode, _ := params["status_code"].(int)
	
	modifications := make(map[string]interface{})
	
	if headers != nil {
		modifications["headers"] = headers
	}
	
	if body != nil {
		modifications["body"] = body
	}
	
	if statusCode != 0 {
		modifications["status_code"] = statusCode
	}
	
	metadata["modifications"] = modifications
	metadata["modified"] = true
	
	return nil
}

// executeCacheResponseAction 执行响应缓存动作
func (m *MockActionManager) executeCacheResponseAction(params map[string]interface{}, metadata map[string]interface{}) error {
	ttl, _ := params["ttl"].(int)
	key, _ := params["key"].(string)
	enabled, _ := params["enabled"].(bool)
	
	if key == "" {
		key = fmt.Sprintf("cache-key-%d", time.Now().Unix())
	}
	
	if ttl == 0 {
		ttl = 3600
	}
	
	metadata["cache_key"] = key
	metadata["ttl"] = ttl
	metadata["enabled"] = enabled
	metadata["cached_until"] = time.Now().Add(time.Duration(ttl) * time.Second).Unix()
	
	return nil
}

// executeScriptAction 执行脚本动作
func (m *MockActionManager) executeScriptAction(params map[string]interface{}, metadata map[string]interface{}) error {
	language, _ := params["language"].(string)
	code, _ := params["code"].(string)
	
	if language == "" {
		language = "javascript"
	}
	
	if code == "" {
		return fmt.Errorf("脚本代码不能为空")
	}
	
	metadata["language"] = language
	metadata["code"] = code
	metadata["executed"] = true
	
	// 模拟脚本执行时间
	time.Sleep(10 * time.Millisecond)
	
	return nil
}

// executeRetryAction 执行重试动作
func (m *MockActionManager) executeRetryAction(params map[string]interface{}, metadata map[string]interface{}) error {
	maxRetries, _ := params["max_retries"].(int)
	delay, _ := params["delay"].(string)
	reason, _ := params["reason"].(string)
	
	if maxRetries == 0 {
		maxRetries = 3
	}
	
	if delay == "" {
		delay = "1s"
	}
	
	metadata["max_retries"] = maxRetries
	metadata["delay"] = delay
	metadata["reason"] = reason
	metadata["retry_with_new_proxy"] = true
	
	return nil
}

// executeRetrySameAction 执行相同代理重试动作
func (m *MockActionManager) executeRetrySameAction(params map[string]interface{}, metadata map[string]interface{}) error {
	maxRetries, _ := params["max_retries"].(int)
	delay, _ := params["delay"].(string)
	reason, _ := params["reason"].(string)
	
	if maxRetries == 0 {
		maxRetries = 2
	}
	
	if delay == "" {
		delay = "500ms"
	}
	
	metadata["max_retries"] = maxRetries
	metadata["delay"] = delay
	metadata["reason"] = reason
	metadata["retry_with_same_proxy"] = true
	
	return nil
}

// executeSaveToPoolAction 执行保存到代理池动作
func (m *MockActionManager) executeSaveToPoolAction(params map[string]interface{}, metadata map[string]interface{}) error {
	proxyURL, _ := params["proxy_url"].(string)
	quality, _ := params["quality"].(string)
	tags, _ := params["tags"].([]string)
	
	if proxyURL == "" {
		return fmt.Errorf("代理URL不能为空")
	}
	
	metadata["proxy_url"] = proxyURL
	metadata["quality"] = quality
	metadata["tags"] = tags
	metadata["saved_to_pool"] = true
	
	return nil
}

// executeCacheAction 执行缓存动作
func (m *MockActionManager) executeCacheAction(params map[string]interface{}, metadata map[string]interface{}) error {
	key, _ := params["key"].(string)
	value := params["value"]
	ttl, _ := params["ttl"].(int)
	namespace, _ := params["namespace"].(string)
	
	if key == "" {
		return fmt.Errorf("缓存键不能为空")
	}
	
	if ttl == 0 {
		ttl = 3600
	}
	
	metadata["key"] = key
	metadata["value"] = value
	metadata["ttl"] = ttl
	metadata["namespace"] = namespace
	metadata["cached"] = true
	
	return nil
}

// executeRequestURLAction 执行URL请求动作
func (m *MockActionManager) executeRequestURLAction(params map[string]interface{}, metadata map[string]interface{}) error {
	url, _ := params["url"].(string)
	method, _ := params["method"].(string)
	timeout, _ := params["timeout"].(string)
	
	if url == "" {
		return fmt.Errorf("URL不能为空")
	}
	
	if method == "" {
		method = "GET"
	}
	
	if timeout == "" {
		timeout = "10s"
	}
	
	metadata["url"] = url
	metadata["method"] = method
	metadata["timeout"] = timeout
	metadata["requested"] = true
	
	// 模拟HTTP请求时间
	time.Sleep(50 * time.Millisecond)
	
	return nil
}

// executeBanIPDomainAction 执行IP域名封禁动作
func (m *MockActionManager) executeBanIPDomainAction(params map[string]interface{}, metadata map[string]interface{}) error {
	target, _ := params["target"].(string)
	targetType, _ := params["type"].(string)
	duration, _ := params["duration"].(int)
	reason, _ := params["reason"].(string)
	
	if target == "" {
		return fmt.Errorf("封禁目标不能为空")
	}
	
	if targetType == "" {
		targetType = "ip"
	}
	
	metadata["target"] = target
	metadata["type"] = targetType
	metadata["duration"] = duration
	metadata["reason"] = reason
	metadata["banned"] = true
	
	return nil
}

// executeNullResponseAction 执行空响应动作
func (m *MockActionManager) executeNullResponseAction(params map[string]interface{}, metadata map[string]interface{}) error {
	statusCode, _ := params["status_code"].(int)
	headers, _ := params["headers"].(map[string]string)
	
	if statusCode == 0 {
		statusCode = 204
	}
	
	metadata["status_code"] = statusCode
	metadata["headers"] = headers
	metadata["null_response"] = true
	
	return nil
}

// executeBypassProxyAction 执行绕过代理动作
func (m *MockActionManager) executeBypassProxyAction(params map[string]interface{}, metadata map[string]interface{}) error {
	reason, _ := params["reason"].(string)
	targetURL, _ := params["target_url"].(string)
	timeout, _ := params["timeout"].(string)
	
	if targetURL == "" {
		return fmt.Errorf("目标URL不能为空")
	}
	
	if timeout == "" {
		timeout = "30s"
	}
	
	metadata["reason"] = reason
	metadata["target_url"] = targetURL
	metadata["timeout"] = timeout
	metadata["bypassed"] = true

	return nil
}

// MockTriggerManager Mock触发器管理器
type MockTriggerManager struct {
	triggers []MockTrigger
	mutex    sync.RWMutex
}

// NewMockTriggerManager 创建Mock触发器管理器
func NewMockTriggerManager() *MockTriggerManager {
	return &MockTriggerManager{
		triggers: make([]MockTrigger, 0),
	}
}

// AddTrigger 添加触发器
func (m *MockTriggerManager) AddTrigger(trigger MockTrigger) {
	m.mutex.Lock()
	m.triggers = append(m.triggers, trigger)
	m.mutex.Unlock()
}

// ProcessTriggers 处理触发器
func (m *MockTriggerManager) ProcessTriggers(stage ProcessStage, req *http.Request, resp *http.Response, requestTime time.Duration) []ActionConfig {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var actions []ActionConfig

	for _, trigger := range m.triggers {
		if trigger.GetStage() == stage && trigger.Match(req, resp, requestTime) {
			actions = append(actions, trigger.GetActions()...)
		}
	}

	return actions
}

// MockTrigger Mock触发器接口
type MockTrigger interface {
	Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool
	GetActions() []ActionConfig
	GetStage() ProcessStage
	GetPriority() int
}

// MockStatusTrigger Mock状态码触发器
type MockStatusTrigger struct {
	Codes        []int
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配状态码
func (t *MockStatusTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if resp == nil {
		return false
	}

	for _, code := range t.Codes {
		if resp.StatusCode == code {
			return true
		}
	}

	return false
}

// GetActions 获取动作
func (t *MockStatusTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockStatusTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockStatusTrigger) GetPriority() int {
	return t.Priority
}

// MockBodyTrigger Mock响应体触发器
type MockBodyTrigger struct {
	Pattern      string
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配响应体
func (t *MockBodyTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if resp == nil || resp.Body == nil {
		return false
	}

	// 这里简化处理，实际应该读取Body内容
	// 为了测试，我们假设Body包含特定模式
	return strings.Contains(t.Pattern, "error") // 简化的匹配逻辑
}

// GetActions 获取动作
func (t *MockBodyTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockBodyTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockBodyTrigger) GetPriority() int {
	return t.Priority
}

// MockMaxRequestTimeTrigger Mock最大请求时间触发器
type MockMaxRequestTimeTrigger struct {
	MaxTime      int64 // 毫秒
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配请求时间
func (t *MockMaxRequestTimeTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	return requestTime.Milliseconds() > t.MaxTime
}

// GetActions 获取动作
func (t *MockMaxRequestTimeTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockMaxRequestTimeTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockMaxRequestTimeTrigger) GetPriority() int {
	return t.Priority
}

// MockURLTrigger Mock URL触发器
type MockURLTrigger struct {
	Pattern      string
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配URL
func (t *MockURLTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if req == nil {
		return false
	}

	return strings.Contains(req.URL.Path, t.Pattern)
}

// GetActions 获取动作
func (t *MockURLTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockURLTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockURLTrigger) GetPriority() int {
	return t.Priority
}

// MockDomainTrigger Mock域名触发器
type MockDomainTrigger struct {
	Pattern      string
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配域名
func (t *MockDomainTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if req == nil {
		return false
	}

	return strings.Contains(req.URL.Host, t.Pattern)
}

// GetActions 获取动作
func (t *MockDomainTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockDomainTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockDomainTrigger) GetPriority() int {
	return t.Priority
}

// MockRequestHeaderTrigger Mock请求头触发器
type MockRequestHeaderTrigger struct {
	HeaderName   string
	Pattern      string
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配请求头
func (t *MockRequestHeaderTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if req == nil {
		return false
	}

	headerValue := req.Header.Get(t.HeaderName)
	if headerValue == "" {
		return false
	}

	matched, _ := regexp.MatchString(t.Pattern, headerValue)
	return matched
}

// GetActions 获取动作
func (t *MockRequestHeaderTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockRequestHeaderTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockRequestHeaderTrigger) GetPriority() int {
	return t.Priority
}

// MockResponseHeaderTrigger Mock响应头触发器
type MockResponseHeaderTrigger struct {
	HeaderName   string
	Pattern      string
	Priority     int
	ProcessStage ProcessStage
	Actions      []ActionConfig
}

// Match 匹配响应头
func (t *MockResponseHeaderTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool {
	if resp == nil {
		return false
	}

	headerValue := resp.Header.Get(t.HeaderName)
	if headerValue == "" {
		return false
	}

	matched, _ := regexp.MatchString(t.Pattern, headerValue)
	return matched
}

// GetActions 获取动作
func (t *MockResponseHeaderTrigger) GetActions() []ActionConfig {
	return t.Actions
}

// GetStage 获取处理阶段
func (t *MockResponseHeaderTrigger) GetStage() ProcessStage {
	return t.ProcessStage
}

// GetPriority 获取优先级
func (t *MockResponseHeaderTrigger) GetPriority() int {
	return t.Priority
}

// MockLogService Mock日志服务
type MockLogService struct {
	logs  []LogEntry
	mutex sync.RWMutex
}

// LogEntry 日志条目
type LogEntry struct {
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// NewMockLogService 创建Mock日志服务
func NewMockLogService() *MockLogService {
	return &MockLogService{
		logs: make([]LogEntry, 0),
	}
}

// Log 记录日志
func (m *MockLogService) Log(level, message string) {
	m.mutex.Lock()
	m.logs = append(m.logs, LogEntry{
		Level:     level,
		Message:   message,
		Timestamp: time.Now(),
	})
	m.mutex.Unlock()
}

// GetLogs 获取日志
func (m *MockLogService) GetLogs() []LogEntry {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make([]LogEntry, len(m.logs))
	copy(result, m.logs)
	return result
}

// ClearLogs 清空日志
func (m *MockLogService) ClearLogs() {
	m.mutex.Lock()
	m.logs = m.logs[:0]
	m.mutex.Unlock()
}

// GetLogsByLevel 按级别获取日志
func (m *MockLogService) GetLogsByLevel(level string) []LogEntry {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []LogEntry
	for _, log := range m.logs {
		if log.Level == level {
			result = append(result, log)
		}
	}
	return result
}

// GetLogCount 获取日志数量
func (m *MockLogService) GetLogCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.logs)
}
