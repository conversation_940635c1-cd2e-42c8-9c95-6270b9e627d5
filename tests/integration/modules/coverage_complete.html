
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>modules: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">/Users/<USER>/Documents/Project Development/Golang/FlexProxy/tests/integration/modules/actions_events_mocks.go (66.4%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package modules

import (
        "context"
        "fmt"
        "net/http"
        "regexp"
        "strings"
        "sync"
        "time"
)

// MockActionManager Mock动作管理器
type MockActionManager struct {
        executedActions []ActionExecutionResult
        mutex           sync.RWMutex
        logService      *MockLogService
}

// NewMockActionManager 创建Mock动作管理器
func NewMockActionManager(logService *MockLogService) *MockActionManager <span class="cov8" title="1">{
        return &amp;MockActionManager{
                executedActions: make([]ActionExecutionResult, 0),
                logService:      logService,
        }
}</span>

// ExecuteAction 执行动作
func (m *MockActionManager) ExecuteAction(ctx context.Context, actionType string, params map[string]interface{}) error <span class="cov8" title="1">{
        start := time.Now()
        
        var err error
        metadata := make(map[string]interface{})
        
        switch ActionType(actionType) </span>{
        case ActionTypeLog:<span class="cov8" title="1">
                err = m.executeLogAction(params, metadata)</span>
        case ActionTypeBanIP:<span class="cov8" title="1">
                err = m.executeBanIPAction(params, metadata)</span>
        case ActionTypeBanDomain:<span class="cov8" title="1">
                err = m.executeBanDomainAction(params, metadata)</span>
        case ActionTypeBlockRequest:<span class="cov8" title="1">
                err = m.executeBlockRequestAction(params, metadata)</span>
        case ActionTypeModifyRequest:<span class="cov8" title="1">
                err = m.executeModifyRequestAction(params, metadata)</span>
        case ActionTypeModifyResponse:<span class="cov8" title="1">
                err = m.executeModifyResponseAction(params, metadata)</span>
        case ActionTypeCacheResponse:<span class="cov8" title="1">
                err = m.executeCacheResponseAction(params, metadata)</span>
        case ActionTypeScript:<span class="cov8" title="1">
                err = m.executeScriptAction(params, metadata)</span>
        case ActionTypeRetry:<span class="cov8" title="1">
                err = m.executeRetryAction(params, metadata)</span>
        case ActionTypeRetrySame:<span class="cov0" title="0">
                err = m.executeRetrySameAction(params, metadata)</span>
        case ActionTypeSaveToPool:<span class="cov0" title="0">
                err = m.executeSaveToPoolAction(params, metadata)</span>
        case ActionTypeCache:<span class="cov8" title="1">
                err = m.executeCacheAction(params, metadata)</span>
        case ActionTypeRequestURL:<span class="cov8" title="1">
                err = m.executeRequestURLAction(params, metadata)</span>
        case ActionTypeBanIPDomain:<span class="cov0" title="0">
                err = m.executeBanIPDomainAction(params, metadata)</span>
        case ActionTypeNullResponse:<span class="cov0" title="0">
                err = m.executeNullResponseAction(params, metadata)</span>
        case ActionTypeBypassProxy:<span class="cov0" title="0">
                err = m.executeBypassProxyAction(params, metadata)</span>
        default:<span class="cov8" title="1">
                err = fmt.Errorf("未知的动作类型: %s", actionType)</span>
        }
        
        <span class="cov8" title="1">duration := time.Since(start)
        
        result := ActionExecutionResult{
                Success:   err == nil,
                Duration:  duration.Milliseconds(),
                Metadata:  metadata,
                Timestamp: time.Now().Unix(),
        }
        
        if err != nil </span><span class="cov8" title="1">{
                result.Error = err.Error()
        }</span>
        
        <span class="cov8" title="1">m.mutex.Lock()
        m.executedActions = append(m.executedActions, result)
        m.mutex.Unlock()
        
        return err</span>
}

// GetExecutedActions 获取已执行的动作
func (m *MockActionManager) GetExecutedActions() []ActionExecutionResult <span class="cov8" title="1">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()
        
        result := make([]ActionExecutionResult, len(m.executedActions))
        copy(result, m.executedActions)
        return result
}</span>

// ClearExecutedActions 清空已执行的动作记录
func (m *MockActionManager) ClearExecutedActions() <span class="cov8" title="1">{
        m.mutex.Lock()
        m.executedActions = m.executedActions[:0]
        m.mutex.Unlock()
}</span>

// executeLogAction 执行日志动作
func (m *MockActionManager) executeLogAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        level, _ := params["level"].(string)
        message, _ := params["message"].(string)
        
        if level == "" </span><span class="cov8" title="1">{
                level = "info"
        }</span>
        <span class="cov8" title="1">if message == "" </span><span class="cov8" title="1">{
                message = "默认日志消息"
        }</span>
        
        <span class="cov8" title="1">metadata["level"] = level
        metadata["message"] = message
        
        if m.logService != nil </span><span class="cov8" title="1">{
                m.logService.Log(level, message)
        }</span>
        
        <span class="cov8" title="1">return nil</span>
}

// executeBanIPAction 执行IP封禁动作
func (m *MockActionManager) executeBanIPAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        ip, _ := params["ip"].(string)
        duration, _ := params["duration"].(int)
        reason, _ := params["reason"].(string)
        
        if ip == "" </span><span class="cov8" title="1">{
                return fmt.Errorf("IP地址不能为空")
        }</span>
        
        <span class="cov8" title="1">metadata["ip"] = ip
        metadata["duration"] = duration
        metadata["reason"] = reason
        metadata["banned_until"] = time.Now().Add(time.Duration(duration) * time.Second).Unix()
        
        return nil</span>
}

// executeBanDomainAction 执行域名封禁动作
func (m *MockActionManager) executeBanDomainAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        domain, _ := params["domain"].(string)
        duration, _ := params["duration"].(int)
        reason, _ := params["reason"].(string)
        
        if domain == "" </span><span class="cov8" title="1">{
                return fmt.Errorf("域名不能为空")
        }</span>
        
        <span class="cov0" title="0">metadata["domain"] = domain
        metadata["duration"] = duration
        metadata["reason"] = reason
        metadata["banned_until"] = time.Now().Add(time.Duration(duration) * time.Second).Unix()
        
        return nil</span>
}

// executeBlockRequestAction 执行请求阻止动作
func (m *MockActionManager) executeBlockRequestAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        reason, _ := params["reason"].(string)
        statusCode, _ := params["status_code"].(int)
        
        if statusCode == 0 </span><span class="cov0" title="0">{
                statusCode = 403
        }</span>
        
        <span class="cov8" title="1">metadata["reason"] = reason
        metadata["status_code"] = statusCode
        metadata["blocked"] = true
        
        return nil</span>
}

// executeModifyRequestAction 执行请求修改动作
func (m *MockActionManager) executeModifyRequestAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        headers, _ := params["headers"].(map[string]interface{})
        body, _ := params["body"].(map[string]interface{})
        
        modifications := make(map[string]interface{})
        
        if headers != nil </span><span class="cov8" title="1">{
                modifications["headers"] = headers
        }</span>
        
        <span class="cov8" title="1">if body != nil </span><span class="cov0" title="0">{
                modifications["body"] = body
        }</span>
        
        <span class="cov8" title="1">metadata["modifications"] = modifications
        metadata["modified"] = true
        
        return nil</span>
}

// executeModifyResponseAction 执行响应修改动作
func (m *MockActionManager) executeModifyResponseAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        headers, _ := params["headers"].(map[string]interface{})
        body, _ := params["body"].(map[string]interface{})
        statusCode, _ := params["status_code"].(int)
        
        modifications := make(map[string]interface{})
        
        if headers != nil </span><span class="cov8" title="1">{
                modifications["headers"] = headers
        }</span>
        
        <span class="cov8" title="1">if body != nil </span><span class="cov0" title="0">{
                modifications["body"] = body
        }</span>
        
        <span class="cov8" title="1">if statusCode != 0 </span><span class="cov0" title="0">{
                modifications["status_code"] = statusCode
        }</span>
        
        <span class="cov8" title="1">metadata["modifications"] = modifications
        metadata["modified"] = true
        
        return nil</span>
}

// executeCacheResponseAction 执行响应缓存动作
func (m *MockActionManager) executeCacheResponseAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        ttl, _ := params["ttl"].(int)
        key, _ := params["key"].(string)
        enabled, _ := params["enabled"].(bool)
        
        if key == "" </span><span class="cov0" title="0">{
                key = fmt.Sprintf("cache-key-%d", time.Now().Unix())
        }</span>
        
        <span class="cov8" title="1">if ttl == 0 </span><span class="cov0" title="0">{
                ttl = 3600
        }</span>
        
        <span class="cov8" title="1">metadata["cache_key"] = key
        metadata["ttl"] = ttl
        metadata["enabled"] = enabled
        metadata["cached_until"] = time.Now().Add(time.Duration(ttl) * time.Second).Unix()
        
        return nil</span>
}

// executeScriptAction 执行脚本动作
func (m *MockActionManager) executeScriptAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        language, _ := params["language"].(string)
        code, _ := params["code"].(string)
        
        if language == "" </span><span class="cov0" title="0">{
                language = "javascript"
        }</span>
        
        <span class="cov8" title="1">if code == "" </span><span class="cov8" title="1">{
                return fmt.Errorf("脚本代码不能为空")
        }</span>
        
        <span class="cov8" title="1">metadata["language"] = language
        metadata["code"] = code
        metadata["executed"] = true
        
        // 模拟脚本执行时间
        time.Sleep(10 * time.Millisecond)
        
        return nil</span>
}

// executeRetryAction 执行重试动作
func (m *MockActionManager) executeRetryAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        maxRetries, _ := params["max_retries"].(int)
        delay, _ := params["delay"].(string)
        reason, _ := params["reason"].(string)
        
        if maxRetries == 0 </span><span class="cov0" title="0">{
                maxRetries = 3
        }</span>
        
        <span class="cov8" title="1">if delay == "" </span><span class="cov0" title="0">{
                delay = "1s"
        }</span>
        
        <span class="cov8" title="1">metadata["max_retries"] = maxRetries
        metadata["delay"] = delay
        metadata["reason"] = reason
        metadata["retry_with_new_proxy"] = true
        
        return nil</span>
}

// executeRetrySameAction 执行相同代理重试动作
func (m *MockActionManager) executeRetrySameAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov0" title="0">{
        maxRetries, _ := params["max_retries"].(int)
        delay, _ := params["delay"].(string)
        reason, _ := params["reason"].(string)
        
        if maxRetries == 0 </span><span class="cov0" title="0">{
                maxRetries = 2
        }</span>
        
        <span class="cov0" title="0">if delay == "" </span><span class="cov0" title="0">{
                delay = "500ms"
        }</span>
        
        <span class="cov0" title="0">metadata["max_retries"] = maxRetries
        metadata["delay"] = delay
        metadata["reason"] = reason
        metadata["retry_with_same_proxy"] = true
        
        return nil</span>
}

// executeSaveToPoolAction 执行保存到代理池动作
func (m *MockActionManager) executeSaveToPoolAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov0" title="0">{
        proxyURL, _ := params["proxy_url"].(string)
        quality, _ := params["quality"].(string)
        tags, _ := params["tags"].([]string)
        
        if proxyURL == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("代理URL不能为空")
        }</span>
        
        <span class="cov0" title="0">metadata["proxy_url"] = proxyURL
        metadata["quality"] = quality
        metadata["tags"] = tags
        metadata["saved_to_pool"] = true
        
        return nil</span>
}

// executeCacheAction 执行缓存动作
func (m *MockActionManager) executeCacheAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        key, _ := params["key"].(string)
        value := params["value"]
        ttl, _ := params["ttl"].(int)
        namespace, _ := params["namespace"].(string)
        
        if key == "" </span><span class="cov8" title="1">{
                return fmt.Errorf("缓存键不能为空")
        }</span>
        
        <span class="cov8" title="1">if ttl == 0 </span><span class="cov0" title="0">{
                ttl = 3600
        }</span>
        
        <span class="cov8" title="1">metadata["key"] = key
        metadata["value"] = value
        metadata["ttl"] = ttl
        metadata["namespace"] = namespace
        metadata["cached"] = true
        
        return nil</span>
}

// executeRequestURLAction 执行URL请求动作
func (m *MockActionManager) executeRequestURLAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov8" title="1">{
        url, _ := params["url"].(string)
        method, _ := params["method"].(string)
        timeout, _ := params["timeout"].(string)
        
        if url == "" </span><span class="cov8" title="1">{
                return fmt.Errorf("URL不能为空")
        }</span>
        
        <span class="cov8" title="1">if method == "" </span><span class="cov0" title="0">{
                method = "GET"
        }</span>
        
        <span class="cov8" title="1">if timeout == "" </span><span class="cov0" title="0">{
                timeout = "10s"
        }</span>
        
        <span class="cov8" title="1">metadata["url"] = url
        metadata["method"] = method
        metadata["timeout"] = timeout
        metadata["requested"] = true
        
        // 模拟HTTP请求时间
        time.Sleep(50 * time.Millisecond)
        
        return nil</span>
}

// executeBanIPDomainAction 执行IP域名封禁动作
func (m *MockActionManager) executeBanIPDomainAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov0" title="0">{
        target, _ := params["target"].(string)
        targetType, _ := params["type"].(string)
        duration, _ := params["duration"].(int)
        reason, _ := params["reason"].(string)
        
        if target == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("封禁目标不能为空")
        }</span>
        
        <span class="cov0" title="0">if targetType == "" </span><span class="cov0" title="0">{
                targetType = "ip"
        }</span>
        
        <span class="cov0" title="0">metadata["target"] = target
        metadata["type"] = targetType
        metadata["duration"] = duration
        metadata["reason"] = reason
        metadata["banned"] = true
        
        return nil</span>
}

// executeNullResponseAction 执行空响应动作
func (m *MockActionManager) executeNullResponseAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov0" title="0">{
        statusCode, _ := params["status_code"].(int)
        headers, _ := params["headers"].(map[string]string)
        
        if statusCode == 0 </span><span class="cov0" title="0">{
                statusCode = 204
        }</span>
        
        <span class="cov0" title="0">metadata["status_code"] = statusCode
        metadata["headers"] = headers
        metadata["null_response"] = true
        
        return nil</span>
}

// executeBypassProxyAction 执行绕过代理动作
func (m *MockActionManager) executeBypassProxyAction(params map[string]interface{}, metadata map[string]interface{}) error <span class="cov0" title="0">{
        reason, _ := params["reason"].(string)
        targetURL, _ := params["target_url"].(string)
        timeout, _ := params["timeout"].(string)
        
        if targetURL == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("目标URL不能为空")
        }</span>
        
        <span class="cov0" title="0">if timeout == "" </span><span class="cov0" title="0">{
                timeout = "30s"
        }</span>
        
        <span class="cov0" title="0">metadata["reason"] = reason
        metadata["target_url"] = targetURL
        metadata["timeout"] = timeout
        metadata["bypassed"] = true

        return nil</span>
}

// MockTriggerManager Mock触发器管理器
type MockTriggerManager struct {
        triggers []MockTrigger
        mutex    sync.RWMutex
}

// NewMockTriggerManager 创建Mock触发器管理器
func NewMockTriggerManager() *MockTriggerManager <span class="cov8" title="1">{
        return &amp;MockTriggerManager{
                triggers: make([]MockTrigger, 0),
        }
}</span>

// AddTrigger 添加触发器
func (m *MockTriggerManager) AddTrigger(trigger MockTrigger) <span class="cov8" title="1">{
        m.mutex.Lock()
        m.triggers = append(m.triggers, trigger)
        m.mutex.Unlock()
}</span>

// ProcessTriggers 处理触发器
func (m *MockTriggerManager) ProcessTriggers(stage ProcessStage, req *http.Request, resp *http.Response, requestTime time.Duration) []ActionConfig <span class="cov8" title="1">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()

        var actions []ActionConfig

        for _, trigger := range m.triggers </span><span class="cov8" title="1">{
                if trigger.GetStage() == stage &amp;&amp; trigger.Match(req, resp, requestTime) </span><span class="cov8" title="1">{
                        actions = append(actions, trigger.GetActions()...)
                }</span>
        }

        <span class="cov8" title="1">return actions</span>
}

// MockTrigger Mock触发器接口
type MockTrigger interface {
        Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool
        GetActions() []ActionConfig
        GetStage() ProcessStage
        GetPriority() int
}

// MockStatusTrigger Mock状态码触发器
type MockStatusTrigger struct {
        Codes        []int
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配状态码
func (t *MockStatusTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        if resp == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov8" title="1">for _, code := range t.Codes </span><span class="cov8" title="1">{
                if resp.StatusCode == code </span><span class="cov8" title="1">{
                        return true
                }</span>
        }

        <span class="cov8" title="1">return false</span>
}

// GetActions 获取动作
func (t *MockStatusTrigger) GetActions() []ActionConfig <span class="cov8" title="1">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockStatusTrigger) GetStage() ProcessStage <span class="cov8" title="1">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockStatusTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockBodyTrigger Mock响应体触发器
type MockBodyTrigger struct {
        Pattern      string
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配响应体
func (t *MockBodyTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        if resp == nil || resp.Body == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        // 这里简化处理，实际应该读取Body内容
        // 为了测试，我们假设Body包含特定模式
        <span class="cov8" title="1">return strings.Contains(t.Pattern, "error")</span> // 简化的匹配逻辑
}

// GetActions 获取动作
func (t *MockBodyTrigger) GetActions() []ActionConfig <span class="cov8" title="1">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockBodyTrigger) GetStage() ProcessStage <span class="cov0" title="0">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockBodyTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockMaxRequestTimeTrigger Mock最大请求时间触发器
type MockMaxRequestTimeTrigger struct {
        MaxTime      int64 // 毫秒
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配请求时间
func (t *MockMaxRequestTimeTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        return requestTime.Milliseconds() &gt; t.MaxTime
}</span>

// GetActions 获取动作
func (t *MockMaxRequestTimeTrigger) GetActions() []ActionConfig <span class="cov8" title="1">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockMaxRequestTimeTrigger) GetStage() ProcessStage <span class="cov8" title="1">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockMaxRequestTimeTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockURLTrigger Mock URL触发器
type MockURLTrigger struct {
        Pattern      string
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配URL
func (t *MockURLTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        if req == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov8" title="1">return strings.Contains(req.URL.Path, t.Pattern)</span>
}

// GetActions 获取动作
func (t *MockURLTrigger) GetActions() []ActionConfig <span class="cov8" title="1">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockURLTrigger) GetStage() ProcessStage <span class="cov8" title="1">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockURLTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockDomainTrigger Mock域名触发器
type MockDomainTrigger struct {
        Pattern      string
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配域名
func (t *MockDomainTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        if req == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov8" title="1">return strings.Contains(req.URL.Host, t.Pattern)</span>
}

// GetActions 获取动作
func (t *MockDomainTrigger) GetActions() []ActionConfig <span class="cov0" title="0">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockDomainTrigger) GetStage() ProcessStage <span class="cov0" title="0">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockDomainTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockRequestHeaderTrigger Mock请求头触发器
type MockRequestHeaderTrigger struct {
        HeaderName   string
        Pattern      string
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配请求头
func (t *MockRequestHeaderTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov8" title="1">{
        if req == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov8" title="1">headerValue := req.Header.Get(t.HeaderName)
        if headerValue == "" </span><span class="cov8" title="1">{
                return false
        }</span>

        <span class="cov8" title="1">matched, _ := regexp.MatchString(t.Pattern, headerValue)
        return matched</span>
}

// GetActions 获取动作
func (t *MockRequestHeaderTrigger) GetActions() []ActionConfig <span class="cov8" title="1">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockRequestHeaderTrigger) GetStage() ProcessStage <span class="cov8" title="1">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockRequestHeaderTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockResponseHeaderTrigger Mock响应头触发器
type MockResponseHeaderTrigger struct {
        HeaderName   string
        Pattern      string
        Priority     int
        ProcessStage ProcessStage
        Actions      []ActionConfig
}

// Match 匹配响应头
func (t *MockResponseHeaderTrigger) Match(req *http.Request, resp *http.Response, requestTime time.Duration) bool <span class="cov0" title="0">{
        if resp == nil </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov0" title="0">headerValue := resp.Header.Get(t.HeaderName)
        if headerValue == "" </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov0" title="0">matched, _ := regexp.MatchString(t.Pattern, headerValue)
        return matched</span>
}

// GetActions 获取动作
func (t *MockResponseHeaderTrigger) GetActions() []ActionConfig <span class="cov0" title="0">{
        return t.Actions
}</span>

// GetStage 获取处理阶段
func (t *MockResponseHeaderTrigger) GetStage() ProcessStage <span class="cov0" title="0">{
        return t.ProcessStage
}</span>

// GetPriority 获取优先级
func (t *MockResponseHeaderTrigger) GetPriority() int <span class="cov0" title="0">{
        return t.Priority
}</span>

// MockLogService Mock日志服务
type MockLogService struct {
        logs  []LogEntry
        mutex sync.RWMutex
}

// LogEntry 日志条目
type LogEntry struct {
        Level     string    `json:"level"`
        Message   string    `json:"message"`
        Timestamp time.Time `json:"timestamp"`
}

// NewMockLogService 创建Mock日志服务
func NewMockLogService() *MockLogService <span class="cov8" title="1">{
        return &amp;MockLogService{
                logs: make([]LogEntry, 0),
        }
}</span>

// Log 记录日志
func (m *MockLogService) Log(level, message string) <span class="cov8" title="1">{
        m.mutex.Lock()
        m.logs = append(m.logs, LogEntry{
                Level:     level,
                Message:   message,
                Timestamp: time.Now(),
        })
        m.mutex.Unlock()
}</span>

// GetLogs 获取日志
func (m *MockLogService) GetLogs() []LogEntry <span class="cov8" title="1">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()

        result := make([]LogEntry, len(m.logs))
        copy(result, m.logs)
        return result
}</span>

// ClearLogs 清空日志
func (m *MockLogService) ClearLogs() <span class="cov8" title="1">{
        m.mutex.Lock()
        m.logs = m.logs[:0]
        m.mutex.Unlock()
}</span>

// GetLogsByLevel 按级别获取日志
func (m *MockLogService) GetLogsByLevel(level string) []LogEntry <span class="cov8" title="1">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()

        var result []LogEntry
        for _, log := range m.logs </span><span class="cov8" title="1">{
                if log.Level == level </span><span class="cov8" title="1">{
                        result = append(result, log)
                }</span>
        }
        <span class="cov8" title="1">return result</span>
}

// GetLogCount 获取日志数量
func (m *MockLogService) GetLogCount() int <span class="cov8" title="1">{
        m.mutex.RLock()
        defer m.mutex.RUnlock()
        return len(m.logs)
}</span>
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
