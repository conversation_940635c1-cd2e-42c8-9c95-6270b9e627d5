package modules

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/mubeng/mubeng/tests/integration/framework"
)

// ActionsEventsIntegrationTestSuite Actions & Events 集成测试套件
type ActionsEventsIntegrationTestSuite struct {
	framework.BaseIntegrationTestSuite
	actionManager  *MockActionManager
	triggerManager *MockTriggerManager
	mockServer     *httptest.Server
	testData       *ActionsEventsTestData
	logService     *MockLogService
}

// ActionsEventsTestData 测试数据结构
type ActionsEventsTestData struct {
	TestRequests  map[string]*http.Request
	TestResponses map[string]*http.Response
	TestConfigs   map[string]map[string]interface{}
	TestPayloads  map[string]interface{}
}

// SetupSuite 设置测试套件
func (s *ActionsEventsIntegrationTestSuite) SetupSuite() {
	s.BaseIntegrationTestSuite.SetupSuite()
	s.SuiteName = "ActionsEventsIntegration"
	
	s.AddLog("SetupSuite", "开始设置Actions & Events集成测试套件")
	
	// 初始化测试数据
	s.initializeTestData()
	
	// 创建Mock服务器
	s.setupMockServer()
	
	// 初始化日志服务
	s.setupLogService()

	// 初始化Action Manager
	s.setupActionManager()

	// 初始化Trigger Manager
	s.setupTriggerManager()
	
	s.AddLog("SetupSuite", "Actions & Events集成测试套件设置完成")
}

// TearDownSuite 清理测试套件
func (s *ActionsEventsIntegrationTestSuite) TearDownSuite() {
	s.AddLog("TearDownSuite", "开始清理Actions & Events集成测试套件")
	
	if s.mockServer != nil {
		s.mockServer.Close()
	}
	
	s.BaseIntegrationTestSuite.TearDownSuite()
	s.AddLog("TearDownSuite", "Actions & Events集成测试套件清理完成")
}

// initializeTestData 初始化测试数据
func (s *ActionsEventsIntegrationTestSuite) initializeTestData() {
	s.testData = &ActionsEventsTestData{
		TestRequests:  make(map[string]*http.Request),
		TestResponses: make(map[string]*http.Response),
		TestConfigs:   make(map[string]map[string]interface{}),
		TestPayloads:  make(map[string]interface{}),
	}
	
	// 创建测试请求
	s.createTestRequests()
	
	// 创建测试响应
	s.createTestResponses()
	
	// 创建测试配置
	s.createTestConfigs()
	
	// 创建测试负载
	s.createTestPayloads()
}

// createTestRequests 创建测试请求
func (s *ActionsEventsIntegrationTestSuite) createTestRequests() {
	// GET请求
	req1, _ := http.NewRequest("GET", "http://example.com/api/users", nil)
	req1.Header.Set("User-Agent", "FlexProxy-Test/1.0")
	req1.Header.Set("Authorization", "Bearer test-token")
	s.testData.TestRequests["get_users"] = req1
	
	// POST请求
	postBody := `{"name":"test","email":"<EMAIL>"}`
	req2, _ := http.NewRequest("POST", "http://example.com/api/users", strings.NewReader(postBody))
	req2.Header.Set("Content-Type", "application/json")
	req2.Header.Set("User-Agent", "FlexProxy-Test/1.0")
	s.testData.TestRequests["post_user"] = req2
	
	// PUT请求
	putBody := `{"id":1,"name":"updated","email":"<EMAIL>"}`
	req3, _ := http.NewRequest("PUT", "http://example.com/api/users/1", strings.NewReader(putBody))
	req3.Header.Set("Content-Type", "application/json")
	s.testData.TestRequests["put_user"] = req3
	
	// DELETE请求
	req4, _ := http.NewRequest("DELETE", "http://example.com/api/users/1", nil)
	req4.Header.Set("Authorization", "Bearer admin-token")
	s.testData.TestRequests["delete_user"] = req4
	
	// 错误请求（用于测试错误处理）
	req5, _ := http.NewRequest("GET", "http://malicious-site.com/attack", nil)
	req5.Header.Set("User-Agent", "AttackBot/1.0")
	s.testData.TestRequests["malicious_request"] = req5
}

// createTestResponses 创建测试响应
func (s *ActionsEventsIntegrationTestSuite) createTestResponses() {
	// 成功响应
	resp1 := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
		Body:       io.NopCloser(strings.NewReader(`{"users":[{"id":1,"name":"test"}]}`)),
	}
	resp1.Header.Set("Content-Type", "application/json")
	s.testData.TestResponses["success_response"] = resp1
	
	// 错误响应
	resp2 := &http.Response{
		StatusCode: 500,
		Header:     make(http.Header),
		Body:       io.NopCloser(strings.NewReader(`{"error":"Internal Server Error"}`)),
	}
	resp2.Header.Set("Content-Type", "application/json")
	s.testData.TestResponses["error_response"] = resp2
	
	// 超时响应
	resp3 := &http.Response{
		StatusCode: 408,
		Header:     make(http.Header),
		Body:       io.NopCloser(strings.NewReader(`{"error":"Request Timeout"}`)),
	}
	s.testData.TestResponses["timeout_response"] = resp3
	
	// 重定向响应
	resp4 := &http.Response{
		StatusCode: 302,
		Header:     make(http.Header),
		Body:       io.NopCloser(strings.NewReader("")),
	}
	resp4.Header.Set("Location", "http://example.com/new-location")
	s.testData.TestResponses["redirect_response"] = resp4
}

// createTestConfigs 创建测试配置
func (s *ActionsEventsIntegrationTestSuite) createTestConfigs() {
	// 基础配置
	baseConfig := map[string]interface{}{
		"global": map[string]interface{}{
			"enable":     true,
			"proxy_file": "./test_proxies.txt",
		},
	}
	s.testData.TestConfigs["base_config"] = baseConfig

	// Actions配置
	actionsConfig := map[string]interface{}{
		"action_sequences": map[string]interface{}{
			"log_sequence": map[string]interface{}{
				"name":        "日志记录序列",
				"description": "记录请求和响应信息",
				"sequence": []map[string]interface{}{
					{
						"type": string(ActionTypeLog),
						"params": map[string]interface{}{
							"level":   "info",
							"message": "请求已处理",
						},
					},
				},
			},
			"ban_sequence": map[string]interface{}{
				"name":        "封禁序列",
				"description": "封禁恶意IP和域名",
				"sequence": []map[string]interface{}{
					{
						"type": string(ActionTypeBanIP),
						"params": map[string]interface{}{
							"ip":       "*************",
							"duration": 3600,
							"reason":   "恶意请求",
						},
					},
				},
			},
		},
	}
	s.testData.TestConfigs["actions_config"] = actionsConfig
}

// createTestPayloads 创建测试负载
func (s *ActionsEventsIntegrationTestSuite) createTestPayloads() {
	// JSON负载
	s.testData.TestPayloads["json_payload"] = map[string]interface{}{
		"user": map[string]interface{}{
			"id":    1,
			"name":  "test user",
			"email": "<EMAIL>",
		},
		"timestamp": time.Now().Unix(),
	}
	
	// XML负载
	s.testData.TestPayloads["xml_payload"] = `<?xml version="1.0" encoding="UTF-8"?>
<user>
	<id>1</id>
	<name>test user</name>
	<email><EMAIL></email>
</user>`
	
	// HTML负载
	s.testData.TestPayloads["html_payload"] = `<!DOCTYPE html>
<html>
<head><title>Test Page</title></head>
<body><h1>Hello World</h1></body>
</html>`
	
	// 表单负载
	s.testData.TestPayloads["form_payload"] = "name=test&email=<EMAIL>&age=25"
}

// setupMockServer 设置Mock服务器
func (s *ActionsEventsIntegrationTestSuite) setupMockServer() {
	mux := http.NewServeMux()
	
	// 正常API端点
	mux.HandleFunc("/api/users", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case "GET":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(200)
			w.Write([]byte(`{"users":[{"id":1,"name":"test"}]}`))
		case "POST":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(201)
			w.Write([]byte(`{"id":2,"name":"created"}`))
		default:
			w.WriteHeader(405)
		}
	})
	
	// 错误端点
	mux.HandleFunc("/api/error", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(500)
		w.Write([]byte(`{"error":"Internal Server Error"}`))
	})
	
	// 超时端点
	mux.HandleFunc("/api/timeout", func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second)
		w.WriteHeader(408)
		w.Write([]byte(`{"error":"Request Timeout"}`))
	})
	
	// 大响应端点
	mux.HandleFunc("/api/large", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(200)
		// 生成大响应（1MB）
		largeData := make([]byte, 1024*1024)
		for i := range largeData {
			largeData[i] = 'A'
		}
		w.Write(largeData)
	})
	
	s.mockServer = httptest.NewServer(mux)
	s.AddLog("setupMockServer", fmt.Sprintf("Mock服务器已启动: %s", s.mockServer.URL))
}

// setupLogService 设置日志服务
func (s *ActionsEventsIntegrationTestSuite) setupLogService() {
	s.logService = NewMockLogService()
	s.AddLog("setupLogService", "Mock日志服务已初始化")
}

// setupActionManager 设置Action Manager
func (s *ActionsEventsIntegrationTestSuite) setupActionManager() {
	// 创建Action Manager
	s.actionManager = NewMockActionManager(s.logService)

	s.AddLog("setupActionManager", "Mock Action Manager已初始化")
}

// setupTriggerManager 设置Trigger Manager
func (s *ActionsEventsIntegrationTestSuite) setupTriggerManager() {
	// 创建Trigger Manager
	s.triggerManager = NewMockTriggerManager()

	// 添加一些默认触发器
	s.addDefaultTriggers()

	s.AddLog("setupTriggerManager", "Mock Trigger Manager已初始化")
}

// addDefaultTriggers 添加默认触发器
func (s *ActionsEventsIntegrationTestSuite) addDefaultTriggers() {
	// 状态码错误触发器
	statusTrigger := &MockStatusTrigger{
		Codes:        []int{500, 502, 503},
		Priority:     1,
		ProcessStage: ProcessStagePostBody,
		Actions: []ActionConfig{
			{
				Type: ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "error",
					"message": "服务器错误状态码",
				},
			},
		},
	}
	s.triggerManager.AddTrigger(statusTrigger)
}

// TestBasicActions 测试基础动作类型
func (s *ActionsEventsIntegrationTestSuite) TestBasicActions() {
	testName := "TestBasicActions"
	s.AddLog(testName, "开始测试基础动作类型")
	
	// 测试所有8种基础动作类型
	basicActions := []struct {
		actionType  string
		description string
		params      map[string]interface{}
	}{
		{
			actionType:  string(ActionTypeLog),
			description: "日志记录动作",
			params: map[string]interface{}{
				"level":   "info",
				"message": "测试日志消息",
			},
		},
		{
			actionType:  string(ActionTypeBanIP),
			description: "IP封禁动作",
			params: map[string]interface{}{
				"ip":       "*************",
				"duration": 3600,
				"reason":   "测试封禁",
			},
		},
		{
			actionType:  string(ActionTypeBanDomain),
			description: "域名封禁动作",
			params: map[string]interface{}{
				"domain":   "malicious-site.com",
				"duration": 7200,
				"reason":   "恶意域名",
			},
		},
		{
			actionType:  string(ActionTypeBlockRequest),
			description: "请求阻止动作",
			params: map[string]interface{}{
				"reason":      "请求被阻止",
				"status_code": 403,
			},
		},
		{
			actionType:  string(ActionTypeModifyRequest),
			description: "请求修改动作",
			params: map[string]interface{}{
				"headers": map[string]interface{}{
					"add": map[string]string{
						"X-Modified": "true",
					},
				},
			},
		},
		{
			actionType:  string(ActionTypeModifyResponse),
			description: "响应修改动作",
			params: map[string]interface{}{
				"headers": map[string]interface{}{
					"add": map[string]string{
						"X-Response-Modified": "true",
					},
				},
			},
		},
		{
			actionType:  string(ActionTypeCacheResponse),
			description: "响应缓存动作",
			params: map[string]interface{}{
				"ttl":     3600,
				"key":     "test-cache-key",
				"enabled": true,
			},
		},
		{
			actionType:  string(ActionTypeScript),
			description: "脚本执行动作",
			params: map[string]interface{}{
				"language": "javascript",
				"code":     "console.log('Hello from script');",
			},
		},
	}
	
	for _, actionTest := range basicActions {
		s.Run(fmt.Sprintf("Test_%s", actionTest.actionType), func() {
			s.AddLog(testName, fmt.Sprintf("测试动作: %s - %s", actionTest.actionType, actionTest.description))
			
			// 执行动作
			ctx := context.Background()
			err := s.actionManager.ExecuteAction(ctx, actionTest.actionType, actionTest.params)
			
			if err != nil {
				s.AddLog(testName, fmt.Sprintf("动作执行失败: %v", err))
				// 某些动作可能因为缺少依赖而失败，这是正常的
				s.T().Logf("动作 %s 执行失败（可能是预期的）: %v", actionTest.actionType, err)
			} else {
				s.AddLog(testName, fmt.Sprintf("动作 %s 执行成功", actionTest.actionType))
			}
		})
	}
	
	s.AddLog(testName, "基础动作类型测试完成")
}

// TestExtendedActions 测试扩展动作类型
func (s *ActionsEventsIntegrationTestSuite) TestExtendedActions() {
	testName := "TestExtendedActions"
	s.AddLog(testName, "开始测试扩展动作类型")

	// 测试所有8种扩展动作类型
	extendedActions := []struct {
		actionType  string
		description string
		params      map[string]interface{}
	}{
		{
			actionType:  constants.ActionTypeRetry,
			description: "新代理重试动作",
			params: map[string]interface{}{
				"max_retries": 3,
				"delay":       "1s",
				"reason":      "代理失败，使用新代理重试",
			},
		},
		{
			actionType:  constants.ActionTypeRetrySame,
			description: "相同代理重试动作",
			params: map[string]interface{}{
				"max_retries": 2,
				"delay":       "500ms",
				"reason":      "临时失败，使用相同代理重试",
			},
		},
		{
			actionType:  constants.ActionTypeSaveToPool,
			description: "保存到代理池动作",
			params: map[string]interface{}{
				"proxy_url": "http://proxy.example.com:8080",
				"quality":   "high",
				"tags":      []string{"fast", "reliable"},
			},
		},
		{
			actionType:  constants.ActionTypeCache,
			description: "缓存动作",
			params: map[string]interface{}{
				"key":        "test-cache-key",
				"value":      "test-cache-value",
				"ttl":        3600,
				"namespace":  "test",
			},
		},
		{
			actionType:  constants.ActionTypeRequestURL,
			description: "URL请求动作",
			params: map[string]interface{}{
				"url":     s.mockServer.URL + "/api/users",
				"method":  "GET",
				"timeout": "10s",
			},
		},
		{
			actionType:  constants.ActionTypeBanIPDomain,
			description: "IP域名封禁动作",
			params: map[string]interface{}{
				"target":   "*************",
				"type":     "ip",
				"duration": 3600,
				"reason":   "恶意行为",
			},
		},
		{
			actionType:  constants.ActionTypeNullResponse,
			description: "空响应动作",
			params: map[string]interface{}{
				"status_code": 204,
				"headers": map[string]string{
					"X-Null-Response": "true",
				},
			},
		},
		{
			actionType:  constants.ActionTypeBypassProxy,
			description: "绕过代理动作",
			params: map[string]interface{}{
				"reason":      "直连目标服务器",
				"target_url":  "http://example.com",
				"timeout":     "30s",
			},
		},
	}

	for _, actionTest := range extendedActions {
		s.Run(fmt.Sprintf("Test_%s", actionTest.actionType), func() {
			s.AddLog(testName, fmt.Sprintf("测试动作: %s - %s", actionTest.actionType, actionTest.description))

			// 执行动作
			ctx := context.Background()
			err := s.actionManager.ExecuteAction(ctx, actionTest.actionType, actionTest.params)

			if err != nil {
				s.AddLog(testName, fmt.Sprintf("动作执行失败: %v", err))
				// 某些动作可能因为缺少依赖而失败，这是正常的
				s.T().Logf("动作 %s 执行失败（可能是预期的）: %v", actionTest.actionType, err)
			} else {
				s.AddLog(testName, fmt.Sprintf("动作 %s 执行成功", actionTest.actionType))
			}
		})
	}

	s.AddLog(testName, "扩展动作类型测试完成")
}

// TestModifyActions 测试修改动作专项功能
func (s *ActionsEventsIntegrationTestSuite) TestModifyActions() {
	testName := "TestModifyActions"
	s.AddLog(testName, "开始测试修改动作专项功能")

	// 测试请求修改动作
	s.Run("TestModifyRequest", func() {
		s.AddLog(testName, "测试请求修改动作")

		// 测试Header修改
		s.testModifyRequestHeaders(testName)

		// 测试Body修改
		s.testModifyRequestBody(testName)
	})

	// 测试响应修改动作
	s.Run("TestModifyResponse", func() {
		s.AddLog(testName, "测试响应修改动作")

		// 测试Header修改
		s.testModifyResponseHeaders(testName)

		// 测试Body修改
		s.testModifyResponseBody(testName)

		// 测试状态码修改
		s.testModifyResponseStatus(testName)
	})

	s.AddLog(testName, "修改动作专项功能测试完成")
}

// testModifyRequestHeaders 测试请求头修改
func (s *ActionsEventsIntegrationTestSuite) testModifyRequestHeaders(testName string) {
	s.AddLog(testName, "测试请求头修改")

	// 测试添加头部
	params := map[string]interface{}{
		"headers": map[string]interface{}{
			"add": map[string]string{
				"X-Custom-Header": "custom-value",
				"X-Test-ID":       "12345",
			},
		},
	}

	ctx := context.Background()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, params)
	s.NoError(err, "请求头添加应该成功")

	// 测试删除头部
	params = map[string]interface{}{
		"headers": map[string]interface{}{
			"remove": []string{"User-Agent", "Authorization"},
		},
	}

	err = s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, params)
	s.NoError(err, "请求头删除应该成功")
}

// testModifyRequestBody 测试请求体修改
func (s *ActionsEventsIntegrationTestSuite) testModifyRequestBody(testName string) {
	s.AddLog(testName, "测试请求体修改")

	// 测试JSON格式修改
	params := map[string]interface{}{
		"body": map[string]interface{}{
			"format": constants.FormatJSON,
			"content": map[string]interface{}{
				"user": map[string]interface{}{
					"name":  "modified user",
					"email": "<EMAIL>",
				},
			},
		},
	}

	ctx := context.Background()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, params)
	s.NoError(err, "JSON请求体修改应该成功")

	// 测试文本格式修改
	params = map[string]interface{}{
		"body": map[string]interface{}{
			"format":  constants.FormatText,
			"content": "Modified text content",
		},
	}

	err = s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, params)
	s.NoError(err, "文本请求体修改应该成功")
}

// testModifyResponseHeaders 测试响应头修改
func (s *ActionsEventsIntegrationTestSuite) testModifyResponseHeaders(testName string) {
	s.AddLog(testName, "测试响应头修改")

	// 测试添加响应头
	params := map[string]interface{}{
		"headers": map[string]interface{}{
			"add": map[string]string{
				"X-Response-Modified": "true",
				"X-Proxy-Version":     "1.0",
			},
		},
	}

	ctx := context.Background()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyResponse, params)
	s.NoError(err, "响应头添加应该成功")
}

// testModifyResponseBody 测试响应体修改
func (s *ActionsEventsIntegrationTestSuite) testModifyResponseBody(testName string) {
	s.AddLog(testName, "测试响应体修改")

	// 测试JSON响应修改
	params := map[string]interface{}{
		"body": map[string]interface{}{
			"format": constants.FormatJSON,
			"content": map[string]interface{}{
				"status":  "modified",
				"message": "Response has been modified",
			},
		},
	}

	ctx := context.Background()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyResponse, params)
	s.NoError(err, "JSON响应体修改应该成功")
}

// testModifyResponseStatus 测试响应状态码修改
func (s *ActionsEventsIntegrationTestSuite) testModifyResponseStatus(testName string) {
	s.AddLog(testName, "测试响应状态码修改")

	params := map[string]interface{}{
		"status_code": 201,
		"reason":      "Modified to Created status",
	}

	ctx := context.Background()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyResponse, params)
	s.NoError(err, "响应状态码修改应该成功")
}

// TestTriggerTypes 测试所有触发器类型
func (s *ActionsEventsIntegrationTestSuite) TestTriggerTypes() {
	testName := "TestTriggerTypes"
	s.AddLog(testName, "开始测试所有触发器类型")

	// 测试状态码触发器
	s.Run("TestStatusTrigger", func() {
		s.testStatusTrigger(testName)
	})

	// 测试响应体触发器
	s.Run("TestBodyTrigger", func() {
		s.testBodyTrigger(testName)
	})

	// 测试请求时间触发器
	s.Run("TestRequestTimeTriggers", func() {
		s.testRequestTimeTriggers(testName)
	})

	// 测试URL触发器
	s.Run("TestURLTrigger", func() {
		s.testURLTrigger(testName)
	})

	// 测试域名触发器
	s.Run("TestDomainTrigger", func() {
		s.testDomainTrigger(testName)
	})

	// 测试请求头触发器
	s.Run("TestRequestHeaderTrigger", func() {
		s.testRequestHeaderTrigger(testName)
	})

	// 测试响应头触发器
	s.Run("TestResponseHeaderTrigger", func() {
		s.testResponseHeaderTrigger(testName)
	})

	s.AddLog(testName, "所有触发器类型测试完成")
}

// testStatusTrigger 测试状态码触发器
func (s *ActionsEventsIntegrationTestSuite) testStatusTrigger(testName string) {
	s.AddLog(testName, "测试状态码触发器")

	// 创建状态码触发器
	statusTrigger := &trigger.StatusTrigger{
		Codes:        []int{500, 502, 503},
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "error",
					"message": "服务器错误状态码触发",
				},
			},
		},
	}

	// 测试匹配
	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["error_response"]

	matched := statusTrigger.Match(req, resp, 100*time.Millisecond)
	s.True(matched, "状态码500应该匹配触发器")

	s.AddLog(testName, "状态码触发器测试完成")
}

// testBodyTrigger 测试响应体触发器
func (s *ActionsEventsIntegrationTestSuite) testBodyTrigger(testName string) {
	s.AddLog(testName, "测试响应体触发器")

	// 创建响应体触发器
	bodyTrigger := &trigger.BodyTrigger{
		Pattern:      "error",
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "warn",
					"message": "响应体包含错误信息",
				},
			},
		},
	}

	// 测试匹配
	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["error_response"]

	matched := bodyTrigger.Match(req, resp, 100*time.Millisecond)
	s.True(matched, "包含'error'的响应体应该匹配触发器")

	s.AddLog(testName, "响应体触发器测试完成")
}

// testRequestTimeTriggers 测试请求时间触发器
func (s *ActionsEventsIntegrationTestSuite) testRequestTimeTriggers(testName string) {
	s.AddLog(testName, "测试请求时间触发器")

	// 测试最大请求时间触发器
	maxTimeTrigger := &trigger.MaxRequestTimeTrigger{
		MaxTime:      1000, // 1秒
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "warn",
					"message": "请求时间过长",
				},
			},
		},
	}

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["success_response"]

	// 测试超时情况
	matched := maxTimeTrigger.Match(req, resp, 2*time.Second)
	s.True(matched, "2秒的请求时间应该触发最大时间触发器")

	// 测试正常情况
	matched = maxTimeTrigger.Match(req, resp, 500*time.Millisecond)
	s.False(matched, "500ms的请求时间不应该触发最大时间触发器")

	s.AddLog(testName, "请求时间触发器测试完成")
}

// testURLTrigger 测试URL触发器
func (s *ActionsEventsIntegrationTestSuite) testURLTrigger(testName string) {
	s.AddLog(testName, "测试URL触发器")

	// 创建URL触发器
	urlTrigger := &trigger.URLTrigger{
		Pattern:      "/api/users",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "info",
					"message": "API用户端点访问",
				},
			},
		},
	}

	req := s.testData.TestRequests["get_users"]

	matched := urlTrigger.Match(req, nil, 0)
	s.True(matched, "URL包含'/api/users'应该匹配触发器")

	s.AddLog(testName, "URL触发器测试完成")
}

// testDomainTrigger 测试域名触发器
func (s *ActionsEventsIntegrationTestSuite) testDomainTrigger(testName string) {
	s.AddLog(testName, "测试域名触发器")

	// 创建域名触发器
	domainTrigger := &trigger.DomainTrigger{
		Pattern:      "example.com",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "info",
					"message": "访问example.com域名",
				},
			},
		},
	}

	req := s.testData.TestRequests["get_users"]

	matched := domainTrigger.Match(req, nil, 0)
	s.True(matched, "域名'example.com'应该匹配触发器")

	s.AddLog(testName, "域名触发器测试完成")
}

// testRequestHeaderTrigger 测试请求头触发器
func (s *ActionsEventsIntegrationTestSuite) testRequestHeaderTrigger(testName string) {
	s.AddLog(testName, "测试请求头触发器")

	// 创建请求头触发器
	headerTrigger := &trigger.RequestHeaderTrigger{
		HeaderName:   "User-Agent",
		Pattern:      "FlexProxy-Test",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "info",
					"message": "检测到FlexProxy测试请求",
				},
			},
		},
	}

	req := s.testData.TestRequests["get_users"]

	matched := headerTrigger.Match(req, nil, 0)
	s.True(matched, "User-Agent包含'FlexProxy-Test'应该匹配触发器")

	s.AddLog(testName, "请求头触发器测试完成")
}

// testResponseHeaderTrigger 测试响应头触发器
func (s *ActionsEventsIntegrationTestSuite) testResponseHeaderTrigger(testName string) {
	s.AddLog(testName, "测试响应头触发器")

	// 创建响应头触发器
	headerTrigger := &trigger.ResponseHeaderTrigger{
		HeaderName:   "Content-Type",
		Pattern:      "application/json",
		Priority:     1,
		ProcessStage: trigger.PostHeader,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "info",
					"message": "检测到JSON响应",
				},
			},
		},
	}

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["success_response"]

	matched := headerTrigger.Match(req, resp, 100*time.Millisecond)
	s.True(matched, "Content-Type为'application/json'应该匹配触发器")

	s.AddLog(testName, "响应头触发器测试完成")
}

// TestActionsEventsIntegration 测试Actions和Events的集成
func (s *ActionsEventsIntegrationTestSuite) TestActionsEventsIntegration() {
	testName := "TestActionsEventsIntegration"
	s.AddLog(testName, "开始测试Actions和Events的集成")

	// 测试触发器和动作的组合
	s.Run("TestTriggerActionCombination", func() {
		s.testTriggerActionCombination(testName)
	})

	// 测试复杂场景
	s.Run("TestComplexScenarios", func() {
		s.testComplexScenarios(testName)
	})

	// 测试错误处理
	s.Run("TestErrorHandling", func() {
		s.testErrorHandling(testName)
	})

	s.AddLog(testName, "Actions和Events集成测试完成")
}

// testTriggerActionCombination 测试触发器和动作的组合
func (s *ActionsEventsIntegrationTestSuite) testTriggerActionCombination(testName string) {
	s.AddLog(testName, "测试触发器和动作的组合")

	// 创建一个复合场景：当状态码为500时，记录日志并封禁IP
	req := s.testData.TestRequests["malicious_request"]
	resp := s.testData.TestResponses["error_response"]

	// 处理触发器
	actions := s.triggerManager.ProcessTriggers(trigger.PostBody, req, resp, 100*time.Millisecond)

	s.Greater(len(actions), 0, "应该有触发的动作")

	// 执行触发的动作
	ctx := context.Background()
	for _, actionConfig := range actions {
		err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
		if err != nil {
			s.T().Logf("动作执行失败（可能是预期的）: %v", err)
		} else {
			s.AddLog(testName, fmt.Sprintf("动作 %s 执行成功", actionConfig.Type))
		}
	}

	s.AddLog(testName, "触发器和动作组合测试完成")
}

// testComplexScenarios 测试复杂场景
func (s *ActionsEventsIntegrationTestSuite) testComplexScenarios(testName string) {
	s.AddLog(testName, "测试复杂场景")

	// 场景1：恶意请求检测和处理
	s.testMaliciousRequestScenario(testName)

	// 场景2：性能监控和优化
	s.testPerformanceMonitoringScenario(testName)

	// 场景3：内容修改和缓存
	s.testContentModificationScenario(testName)

	s.AddLog(testName, "复杂场景测试完成")
}

// testMaliciousRequestScenario 测试恶意请求场景
func (s *ActionsEventsIntegrationTestSuite) testMaliciousRequestScenario(testName string) {
	s.AddLog(testName, "测试恶意请求检测和处理场景")

	// 模拟恶意请求
	req := s.testData.TestRequests["malicious_request"]

	// 检查域名触发器
	domainTrigger := &trigger.DomainTrigger{
		Pattern:      "malicious-site.com",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeBanDomain,
				Params: map[string]interface{}{
					"domain":   "malicious-site.com",
					"duration": 86400, // 24小时
					"reason":   "恶意域名",
				},
			},
			{
				Type: constants.ActionTypeBlockRequest,
				Params: map[string]interface{}{
					"reason":      "访问被禁止的域名",
					"status_code": 403,
				},
			},
		},
	}

	matched := domainTrigger.Match(req, nil, 0)
	s.True(matched, "恶意域名应该被检测到")

	if matched {
		// 执行封禁动作
		ctx := context.Background()
		for _, actionConfig := range domainTrigger.GetActions() {
			err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
			if err != nil {
				s.T().Logf("动作执行失败（可能是预期的）: %v", err)
			}
		}
	}

	s.AddLog(testName, "恶意请求场景测试完成")
}

// testPerformanceMonitoringScenario 测试性能监控场景
func (s *ActionsEventsIntegrationTestSuite) testPerformanceMonitoringScenario(testName string) {
	s.AddLog(testName, "测试性能监控场景")

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["success_response"]

	// 创建性能监控触发器
	perfTrigger := &trigger.MaxRequestTimeTrigger{
		MaxTime:      500, // 500ms
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "warn",
					"message": "请求响应时间过长",
				},
			},
			{
				Type: constants.ActionTypeRetry,
				Params: map[string]interface{}{
					"max_retries": 1,
					"delay":       "100ms",
					"reason":      "性能优化重试",
				},
			},
		},
	}

	// 测试慢请求
	matched := perfTrigger.Match(req, resp, 1*time.Second)
	s.True(matched, "1秒的请求时间应该触发性能监控")

	if matched {
		ctx := context.Background()
		for _, actionConfig := range perfTrigger.GetActions() {
			err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
			if err != nil {
				s.T().Logf("动作执行失败（可能是预期的）: %v", err)
			}
		}
	}

	s.AddLog(testName, "性能监控场景测试完成")
}

// testContentModificationScenario 测试内容修改场景
func (s *ActionsEventsIntegrationTestSuite) testContentModificationScenario(testName string) {
	s.AddLog(testName, "测试内容修改和缓存场景")

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["success_response"]

	// 创建内容修改触发器
	contentTrigger := &trigger.BodyTrigger{
		Pattern:      "users",
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeModifyResponse,
				Params: map[string]interface{}{
					"headers": map[string]interface{}{
						"add": map[string]string{
							"X-Content-Modified": "true",
							"X-Modification-Time": time.Now().Format(time.RFC3339),
						},
					},
				},
			},
			{
				Type: constants.ActionTypeCacheResponse,
				Params: map[string]interface{}{
					"ttl":     3600,
					"key":     "users-response",
					"enabled": true,
				},
			},
		},
	}

	matched := contentTrigger.Match(req, resp, 100*time.Millisecond)
	s.True(matched, "包含'users'的响应应该触发内容修改")

	if matched {
		ctx := context.Background()
		for _, actionConfig := range contentTrigger.GetActions() {
			err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
			if err != nil {
				s.T().Logf("动作执行失败（可能是预期的）: %v", err)
			}
		}
	}

	s.AddLog(testName, "内容修改场景测试完成")
}

// testErrorHandling 测试错误处理
func (s *ActionsEventsIntegrationTestSuite) testErrorHandling(testName string) {
	s.AddLog(testName, "测试错误处理")

	// 测试无效动作类型
	s.Run("TestInvalidActionType", func() {
		ctx := context.Background()
		err := s.actionManager.ExecuteAction(ctx, "invalid_action_type", map[string]interface{}{})
		s.Error(err, "无效的动作类型应该返回错误")
	})

	// 测试无效参数
	s.Run("TestInvalidParameters", func() {
		ctx := context.Background()
		err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeLog, map[string]interface{}{
			"invalid_param": "invalid_value",
		})
		// 某些动作可能容忍无效参数，所以这里不强制要求错误
		if err != nil {
			s.AddLog(testName, fmt.Sprintf("无效参数导致错误（预期）: %v", err))
		}
	})

	// 测试空参数
	s.Run("TestNilParameters", func() {
		ctx := context.Background()
		err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeLog, nil)
		// 某些动作可能容忍空参数
		if err != nil {
			s.AddLog(testName, fmt.Sprintf("空参数导致错误（可能预期）: %v", err))
		}
	})

	s.AddLog(testName, "错误处理测试完成")
}

// TestPerformanceMetrics 测试性能指标
func (s *ActionsEventsIntegrationTestSuite) TestPerformanceMetrics() {
	testName := "TestPerformanceMetrics"
	s.AddLog(testName, "开始测试性能指标")

	// 测试动作执行时间
	s.Run("TestActionExecutionTime", func() {
		s.testActionExecutionTime(testName)
	})

	// 测试触发器检测时间
	s.Run("TestTriggerDetectionTime", func() {
		s.testTriggerDetectionTime(testName)
	})

	// 测试并发处理
	s.Run("TestConcurrentProcessing", func() {
		s.testConcurrentProcessing(testName)
	})

	s.AddLog(testName, "性能指标测试完成")
}

// testActionExecutionTime 测试动作执行时间
func (s *ActionsEventsIntegrationTestSuite) testActionExecutionTime(testName string) {
	s.AddLog(testName, "测试动作执行时间")

	ctx := context.Background()

	// 测试日志动作执行时间
	start := time.Now()
	err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeLog, map[string]interface{}{
		"level":   "info",
		"message": "性能测试日志",
	})
	duration := time.Since(start)

	if err == nil {
		s.Less(duration, 100*time.Millisecond, "日志动作执行时间应该小于100ms")
		s.AddLog(testName, fmt.Sprintf("日志动作执行时间: %v", duration))
	}

	// 测试修改动作执行时间
	start = time.Now()
	err = s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, map[string]interface{}{
		"headers": map[string]interface{}{
			"add": map[string]string{
				"X-Performance-Test": "true",
			},
		},
	})
	duration = time.Since(start)

	if err == nil {
		s.Less(duration, 50*time.Millisecond, "修改动作执行时间应该小于50ms")
		s.AddLog(testName, fmt.Sprintf("修改动作执行时间: %v", duration))
	}

	s.AddLog(testName, "动作执行时间测试完成")
}

// testTriggerDetectionTime 测试触发器检测时间
func (s *ActionsEventsIntegrationTestSuite) testTriggerDetectionTime(testName string) {
	s.AddLog(testName, "测试触发器检测时间")

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["success_response"]

	// 测试状态码触发器检测时间
	statusTrigger := &trigger.StatusTrigger{
		Codes:        []int{200},
		Priority:     1,
		ProcessStage: trigger.PostBody,
	}

	start := time.Now()
	matched := statusTrigger.Match(req, resp, 100*time.Millisecond)
	duration := time.Since(start)

	s.True(matched, "状态码200应该匹配")
	s.Less(duration, 10*time.Millisecond, "状态码触发器检测时间应该小于10ms")
	s.AddLog(testName, fmt.Sprintf("状态码触发器检测时间: %v", duration))

	// 测试URL触发器检测时间
	urlTrigger := &trigger.URLTrigger{
		Pattern:      "/api/users",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
	}

	start = time.Now()
	matched = urlTrigger.Match(req, nil, 0)
	duration = time.Since(start)

	s.True(matched, "URL应该匹配")
	s.Less(duration, 5*time.Millisecond, "URL触发器检测时间应该小于5ms")
	s.AddLog(testName, fmt.Sprintf("URL触发器检测时间: %v", duration))

	s.AddLog(testName, "触发器检测时间测试完成")
}

// testConcurrentProcessing 测试并发处理
func (s *ActionsEventsIntegrationTestSuite) testConcurrentProcessing(testName string) {
	s.AddLog(testName, "测试并发处理")

	const numGoroutines = 100
	const numOperations = 10

	// 测试并发动作执行
	s.Run("ConcurrentActionExecution", func() {
		var results = make(chan error, numGoroutines*numOperations)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				ctx := context.Background()
				for j := 0; j < numOperations; j++ {
					err := s.actionManager.ExecuteAction(ctx, constants.ActionTypeLog, map[string]interface{}{
						"level":   "info",
						"message": fmt.Sprintf("并发测试 goroutine-%d operation-%d", id, j),
					})
					results <- err
				}
			}(i)
		}

		// 收集结果
		successCount := 0
		for i := 0; i < numGoroutines*numOperations; i++ {
			err := <-results
			if err == nil {
				successCount++
			}
		}

		s.AddLog(testName, fmt.Sprintf("并发动作执行: %d/%d 成功", successCount, numGoroutines*numOperations))
	})

	// 测试并发触发器检测
	s.Run("ConcurrentTriggerDetection", func() {
		req := s.testData.TestRequests["get_users"]
		resp := s.testData.TestResponses["success_response"]

		statusTrigger := &trigger.StatusTrigger{
			Codes:        []int{200},
			Priority:     1,
			ProcessStage: trigger.PostBody,
		}

		var results = make(chan bool, numGoroutines*numOperations)

		for i := 0; i < numGoroutines; i++ {
			go func() {
				for j := 0; j < numOperations; j++ {
					matched := statusTrigger.Match(req, resp, 100*time.Millisecond)
					results <- matched
				}
			}()
		}

		// 收集结果
		matchCount := 0
		for i := 0; i < numGoroutines*numOperations; i++ {
			if <-results {
				matchCount++
			}
		}

		s.Equal(numGoroutines*numOperations, matchCount, "所有并发触发器检测都应该匹配")
		s.AddLog(testName, fmt.Sprintf("并发触发器检测: %d/%d 匹配", matchCount, numGoroutines*numOperations))
	})

	s.AddLog(testName, "并发处理测试完成")
}

// TestRealWorldScenarios 测试真实世界场景
func (s *ActionsEventsIntegrationTestSuite) TestRealWorldScenarios() {
	testName := "TestRealWorldScenarios"
	s.AddLog(testName, "开始测试真实世界场景")

	// 场景1：API网关场景
	s.Run("APIGatewayScenario", func() {
		s.testAPIGatewayScenario(testName)
	})

	// 场景2：安全防护场景
	s.Run("SecurityProtectionScenario", func() {
		s.testSecurityProtectionScenario(testName)
	})

	// 场景3：负载均衡场景
	s.Run("LoadBalancingScenario", func() {
		s.testLoadBalancingScenario(testName)
	})

	s.AddLog(testName, "真实世界场景测试完成")
}

// testAPIGatewayScenario 测试API网关场景
func (s *ActionsEventsIntegrationTestSuite) testAPIGatewayScenario(testName string) {
	s.AddLog(testName, "测试API网关场景")

	// 模拟API网关的典型工作流程
	req := s.testData.TestRequests["post_user"]

	// 1. 请求验证
	authTrigger := &trigger.RequestHeaderTrigger{
		HeaderName:   "Authorization",
		Pattern:      "Bearer",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "info",
					"message": "API请求认证检查",
				},
			},
		},
	}

	matched := authTrigger.Match(req, nil, 0)
	if !matched {
		// 如果没有认证头，阻止请求
		ctx := context.Background()
		s.actionManager.ExecuteAction(ctx, constants.ActionTypeBlockRequest, map[string]interface{}{
			"reason":      "缺少认证信息",
			"status_code": 401,
		})
	}

	// 2. 请求修改（添加API网关标识）
	ctx := context.Background()
	s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyRequest, map[string]interface{}{
		"headers": map[string]interface{}{
			"add": map[string]string{
				"X-API-Gateway": "FlexProxy",
				"X-Request-ID":  fmt.Sprintf("req-%d", time.Now().Unix()),
			},
		},
	})

	// 3. 响应处理
	resp := s.testData.TestResponses["success_response"]
	s.actionManager.ExecuteAction(ctx, constants.ActionTypeModifyResponse, map[string]interface{}{
		"headers": map[string]interface{}{
			"add": map[string]string{
				"X-API-Gateway-Response": "true",
				"X-Response-Time":        time.Now().Format(time.RFC3339),
			},
		},
	})

	s.AddLog(testName, "API网关场景测试完成")
}

// testSecurityProtectionScenario 测试安全防护场景
func (s *ActionsEventsIntegrationTestSuite) testSecurityProtectionScenario(testName string) {
	s.AddLog(testName, "测试安全防护场景")

	// 模拟安全防护的典型场景
	maliciousReq := s.testData.TestRequests["malicious_request"]

	// 1. 恶意User-Agent检测
	uaTrigger := &trigger.RequestHeaderTrigger{
		HeaderName:   "User-Agent",
		Pattern:      "AttackBot",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeBanIP,
				Params: map[string]interface{}{
					"ip":       "*************", // 从请求中提取
					"duration": 3600,
					"reason":   "恶意User-Agent",
				},
			},
			{
				Type: constants.ActionTypeBlockRequest,
				Params: map[string]interface{}{
					"reason":      "检测到恶意请求",
					"status_code": 403,
				},
			},
		},
	}

	matched := uaTrigger.Match(maliciousReq, nil, 0)
	s.True(matched, "恶意User-Agent应该被检测到")

	if matched {
		ctx := context.Background()
		for _, actionConfig := range uaTrigger.GetActions() {
			err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
			if err != nil {
				s.T().Logf("安全动作执行失败（可能是预期的）: %v", err)
			}
		}
	}

	// 2. 域名黑名单检测
	domainTrigger := &trigger.DomainTrigger{
		Pattern:      "malicious-site.com",
		Priority:     1,
		ProcessStage: trigger.PreRequest,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeBanDomain,
				Params: map[string]interface{}{
					"domain":   "malicious-site.com",
					"duration": 86400,
					"reason":   "域名黑名单",
				},
			},
		},
	}

	matched = domainTrigger.Match(maliciousReq, nil, 0)
	s.True(matched, "恶意域名应该被检测到")

	s.AddLog(testName, "安全防护场景测试完成")
}

// testLoadBalancingScenario 测试负载均衡场景
func (s *ActionsEventsIntegrationTestSuite) testLoadBalancingScenario(testName string) {
	s.AddLog(testName, "测试负载均衡场景")

	req := s.testData.TestRequests["get_users"]
	resp := s.testData.TestResponses["error_response"]

	// 模拟服务器错误时的负载均衡处理
	errorTrigger := &trigger.StatusTrigger{
		Codes:        []int{500, 502, 503},
		Priority:     1,
		ProcessStage: trigger.PostBody,
		Actions: []common.ActionConfig{
			{
				Type: constants.ActionTypeRetry,
				Params: map[string]interface{}{
					"max_retries": 3,
					"delay":       "1s",
					"reason":      "服务器错误，切换到其他节点",
				},
			},
			{
				Type: constants.ActionTypeLog,
				Params: map[string]interface{}{
					"level":   "warn",
					"message": "检测到服务器错误，启动重试机制",
				},
			},
		},
	}

	matched := errorTrigger.Match(req, resp, 100*time.Millisecond)
	s.True(matched, "服务器错误应该触发负载均衡")

	if matched {
		ctx := context.Background()
		for _, actionConfig := range errorTrigger.GetActions() {
			err := s.actionManager.ExecuteAction(ctx, actionConfig.Type, actionConfig.Params)
			if err != nil {
				s.T().Logf("负载均衡动作执行失败（可能是预期的）: %v", err)
			}
		}
	}

	s.AddLog(testName, "负载均衡场景测试完成")
}

// TestActionsEventsIntegrationSuite 运行Actions & Events集成测试套件
func TestActionsEventsIntegrationSuite(t *testing.T) {
	suite.Run(t, new(ActionsEventsIntegrationTestSuite))
}
