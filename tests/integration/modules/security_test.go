// Package modules 包含各个模块的集成测试
package modules

import (
	"fmt"
	"testing"
	"time"

	"github.com/mubeng/mubeng/tests/integration/framework"
)

// SecurityIntegrationTestSuite Security 模块集成测试套件
type SecurityIntegrationTestSuite struct {
	framework.IntegrationTestSuite
	httpMockServer *framework.HTTPMockServer
	testUtils      *framework.TestUtils
	securityPort   int
	tokens         map[string]string
	certificates   map[string]string
}

// SetupSuite 测试套件初始化
func (s *SecurityIntegrationTestSuite) SetupSuite() {
	s.IntegrationTestSuite.SetupSuite()
	s.testUtils = framework.NewTestUtils()
	s.tokens = make(map[string]string)
	s.certificates = make(map[string]string)
	
	// 分配安全服务端口
	port, err := s.testUtils.FindFreePort()
	s.Require().NoError(err, "分配安全服务端口失败")
	s.securityPort = port
	
	// 创建 HTTP Mock 服务器用于认证测试
	s.httpMockServer, err = s.GetMockManager().CreateHTTPMockServer("auth_server", 0)
	s.Require().NoError(err, "创建认证服务器失败")
	
	// 启动 Mock 服务器
	err = s.GetMockManager().StartAll()
	s.Require().NoError(err, "启动 Mock 服务器失败")
	
	// 等待服务器就绪
	err = s.GetMockManager().WaitForReady(10 * time.Second)
	s.Require().NoError(err, "等待 Mock 服务器就绪失败")
	
	s.T().Logf("Security 集成测试环境初始化完成，安全端口: %d", s.securityPort)
}

// TestAuthentication 测试认证功能
func (s *SecurityIntegrationTestSuite) TestAuthentication() {
	testName := "TestAuthentication"
	s.AddLog(testName, "开始测试认证功能")
	
	// 创建认证配置
	configContent := s.createAuthenticationConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_auth.yaml", configContent)
	s.Require().NoError(err, "创建认证配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟认证测试
	s.simulateAuthenticationTest(testName)
	
	s.AddLog(testName, "认证功能测试完成")
}

// TestAuthorization 测试授权功能
func (s *SecurityIntegrationTestSuite) TestAuthorization() {
	testName := "TestAuthorization"
	s.AddLog(testName, "开始测试授权功能")
	
	// 创建授权配置
	configContent := s.createAuthorizationConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_authz.yaml", configContent)
	s.Require().NoError(err, "创建授权配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟授权测试
	s.simulateAuthorizationTest(testName)
	
	s.AddLog(testName, "授权功能测试完成")
}

// TestTLSConfiguration 测试 TLS 配置
func (s *SecurityIntegrationTestSuite) TestTLSConfiguration() {
	testName := "TestTLSConfiguration"
	s.AddLog(testName, "开始测试 TLS 配置")
	
	// 创建 TLS 配置
	configContent := s.createTLSConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_tls.yaml", configContent)
	s.Require().NoError(err, "创建 TLS 配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟 TLS 测试
	s.simulateTLSTest(testName)
	
	s.AddLog(testName, "TLS 配置测试完成")
}

// TestTokenManagement 测试令牌管理
func (s *SecurityIntegrationTestSuite) TestTokenManagement() {
	testName := "TestTokenManagement"
	s.AddLog(testName, "开始测试令牌管理")
	
	// 创建令牌管理配置
	configContent := s.createTokenManagementConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_token.yaml", configContent)
	s.Require().NoError(err, "创建令牌管理配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟令牌管理测试
	s.simulateTokenManagementTest(testName)
	
	s.AddLog(testName, "令牌管理测试完成")
}

// TestRequestFiltering 测试请求过滤
func (s *SecurityIntegrationTestSuite) TestRequestFiltering() {
	testName := "TestRequestFiltering"
	s.AddLog(testName, "开始测试请求过滤")
	
	// 创建请求过滤配置
	configContent := s.createRequestFilteringConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_filter.yaml", configContent)
	s.Require().NoError(err, "创建请求过滤配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟请求过滤测试
	s.simulateRequestFilteringTest(testName)
	
	s.AddLog(testName, "请求过滤测试完成")
}

// TestEncryptionDecryption 测试加密解密
func (s *SecurityIntegrationTestSuite) TestEncryptionDecryption() {
	testName := "TestEncryptionDecryption"
	s.AddLog(testName, "开始测试加密解密")
	
	// 创建加密配置
	configContent := s.createEncryptionConfig()
	configFile, err := s.GetEnvironment().CreateConfigFile("security_encryption.yaml", configContent)
	s.Require().NoError(err, "创建加密配置失败")
	
	s.AddLog(testName, fmt.Sprintf("配置文件已创建: %s", configFile))
	
	// 模拟加密解密测试
	s.simulateEncryptionTest(testName)
	
	s.AddLog(testName, "加密解密测试完成")
}

// 模拟测试方法

// simulateAuthenticationTest 模拟认证测试
func (s *SecurityIntegrationTestSuite) simulateAuthenticationTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟用户认证")
	
	// 模拟用户认证场景
	users := []struct {
		username string
		password string
		valid    bool
	}{
		{"admin", "admin123", true},
		{"user1", "password1", true},
		{"user2", "wrongpass", false},
		{"guest", "", false},
		{"", "password", false},
	}
	
	successfulAuth := 0
	failedAuth := 0
	
	for _, user := range users {
		s.AddLog(testName, fmt.Sprintf("认证用户: %s", user.username))
		
		if user.valid && user.username != "" && user.password != "" {
			// 模拟认证成功
			token := fmt.Sprintf("token_%s_%d", user.username, time.Now().Unix())
			s.tokens[user.username] = token
			successfulAuth++
			s.AddLog(testName, fmt.Sprintf("用户 %s 认证成功，令牌: %s", user.username, token))
		} else {
			// 模拟认证失败
			failedAuth++
			s.AddLog(testName, fmt.Sprintf("用户 %s 认证失败", user.username))
		}
		
		time.Sleep(10 * time.Millisecond)
	}
	
	// 模拟令牌验证
	s.AddLog(testName, "验证已颁发的令牌")
	validTokens := 0
	for username, token := range s.tokens {
		s.AddLog(testName, fmt.Sprintf("验证令牌: %s -> %s", username, token))
		validTokens++
		time.Sleep(5 * time.Millisecond)
	}
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(users)))
	s.RecordMetric(testName, "success_count", int64(successfulAuth))
	s.RecordMetric(testName, "failure_count", int64(failedAuth))
	s.RecordMetric(testName, "avg_response_time", 10*time.Millisecond)
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("认证测试完成，耗时: %v，成功: %d，失败: %d，有效令牌: %d", 
		duration, successfulAuth, failedAuth, validTokens))
	
	// 验证认证结果
	s.Assert().Greater(successfulAuth, 0, "应该有成功的认证")
	s.Assert().Equal(successfulAuth, len(s.tokens), "令牌数量应该等于成功认证数量")
}

// simulateAuthorizationTest 模拟授权测试
func (s *SecurityIntegrationTestSuite) simulateAuthorizationTest(testName string) {
	start := time.Now()
	
	s.AddLog(testName, "模拟用户授权")
	
	// 模拟权限检查场景
	permissions := []struct {
		user     string
		resource string
		action   string
		allowed  bool
	}{
		{"admin", "/api/admin", "GET", true},
		{"admin", "/api/users", "POST", true},
		{"user1", "/api/profile", "GET", true},
		{"user1", "/api/admin", "GET", false},
		{"guest", "/api/public", "GET", true},
		{"guest", "/api/users", "POST", false},
	}
	
	allowedCount := 0
	deniedCount := 0
	
	for _, perm := range permissions {
		s.AddLog(testName, fmt.Sprintf("检查权限: 用户=%s, 资源=%s, 动作=%s", 
			perm.user, perm.resource, perm.action))
		
		if perm.allowed {
			allowedCount++
			s.AddLog(testName, fmt.Sprintf("✅ 授权通过: %s 可以 %s %s", 
				perm.user, perm.action, perm.resource))
		} else {
			deniedCount++
			s.AddLog(testName, fmt.Sprintf("❌ 授权拒绝: %s 不能 %s %s", 
				perm.user, perm.action, perm.resource))
		}
		
		time.Sleep(8 * time.Millisecond)
	}
	
	// 模拟角色权限检查
	s.AddLog(testName, "检查角色权限")
	roles := map[string][]string{
		"admin": {"read", "write", "delete", "admin"},
		"user":  {"read", "write"},
		"guest": {"read"},
	}
	
	for role, perms := range roles {
		s.AddLog(testName, fmt.Sprintf("角色 %s 权限: %v", role, perms))
	}
	
	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(permissions)))
	s.RecordMetric(testName, "success_count", int64(allowedCount))
	s.RecordMetric(testName, "failure_count", int64(deniedCount))
	
	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("授权测试完成，耗时: %v，允许: %d，拒绝: %d", 
		duration, allowedCount, deniedCount))
	
	// 验证授权结果
	s.Assert().Greater(allowedCount, 0, "应该有被允许的操作")
	s.Assert().Greater(deniedCount, 0, "应该有被拒绝的操作")
}

// simulateTLSTest 模拟 TLS 测试
func (s *SecurityIntegrationTestSuite) simulateTLSTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟 TLS 配置和握手")

	// 模拟创建自签名证书
	s.AddLog(testName, "生成自签名证书")
	certFile := fmt.Sprintf("%s/test_cert.pem", s.GetEnvironment().GetDataDir())
	keyFile := fmt.Sprintf("%s/test_key.pem", s.GetEnvironment().GetDataDir())

	// 模拟证书内容
	certContent := `-----BEGIN CERTIFICATE-----
MIICljCCAX4CCQDTest123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ
-----END CERTIFICATE-----`

	keyContent := `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTest123456789
-----END PRIVATE KEY-----`

	err := s.testUtils.WriteFile(certFile, []byte(certContent))
	s.Require().NoError(err, "写入证书文件失败")

	err = s.testUtils.WriteFile(keyFile, []byte(keyContent))
	s.Require().NoError(err, "写入密钥文件失败")

	s.certificates["cert"] = certFile
	s.certificates["key"] = keyFile

	s.AddLog(testName, fmt.Sprintf("证书文件已创建: %s", certFile))
	s.AddLog(testName, fmt.Sprintf("密钥文件已创建: %s", keyFile))

	// 模拟 TLS 握手
	tlsVersions := []string{"1.2", "1.3"}
	cipherSuites := []string{
		"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
		"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}

	successfulHandshakes := 0
	for _, version := range tlsVersions {
		for _, cipher := range cipherSuites {
			s.AddLog(testName, fmt.Sprintf("测试 TLS 握手: 版本=%s, 密码套件=%s", version, cipher))

			// 模拟握手成功
			successfulHandshakes++
			s.AddLog(testName, fmt.Sprintf("✅ TLS 握手成功: %s + %s", version, cipher))

			time.Sleep(5 * time.Millisecond)
		}
	}

	// 模拟证书验证
	s.AddLog(testName, "验证证书链")
	certValidation := true
	s.Assert().True(certValidation, "证书验证应该通过")

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(tlsVersions)*len(cipherSuites)))
	s.RecordMetric(testName, "success_count", int64(successfulHandshakes))
	s.RecordMetric(testName, "avg_response_time", 5*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("TLS 测试完成，耗时: %v，成功握手: %d",
		duration, successfulHandshakes))

	// 验证 TLS 配置
	s.Assert().True(s.testUtils.FileExists(certFile), "证书文件应该存在")
	s.Assert().True(s.testUtils.FileExists(keyFile), "密钥文件应该存在")
	s.Assert().Equal(len(tlsVersions)*len(cipherSuites), successfulHandshakes, "所有握手都应该成功")
}

// simulateTokenManagementTest 模拟令牌管理测试
func (s *SecurityIntegrationTestSuite) simulateTokenManagementTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟令牌管理")

	// 模拟生成不同类型的令牌
	tokenTypes := []struct {
		tokenType string
		userID    string
		scopes    []string
		duration  time.Duration
	}{
		{"access", "user1", []string{"read", "write"}, 1 * time.Hour},
		{"refresh", "user1", []string{"refresh"}, 24 * time.Hour},
		{"api", "service1", []string{"api:read", "api:write"}, 30 * 24 * time.Hour},
		{"temp", "guest", []string{"read"}, 15 * time.Minute},
	}

	generatedTokens := 0
	for _, tokenInfo := range tokenTypes {
		s.AddLog(testName, fmt.Sprintf("生成令牌: 类型=%s, 用户=%s, 权限=%v, 有效期=%v",
			tokenInfo.tokenType, tokenInfo.userID, tokenInfo.scopes, tokenInfo.duration))

		// 模拟令牌生成
		token := fmt.Sprintf("%s_%s_%d", tokenInfo.tokenType, tokenInfo.userID, time.Now().Unix())
		s.tokens[fmt.Sprintf("%s_%s", tokenInfo.tokenType, tokenInfo.userID)] = token
		generatedTokens++

		s.AddLog(testName, fmt.Sprintf("令牌已生成: %s", token))
		time.Sleep(8 * time.Millisecond)
	}

	// 模拟令牌验证
	s.AddLog(testName, "验证令牌有效性")
	validTokens := 0
	expiredTokens := 0

	for tokenKey, token := range s.tokens {
		s.AddLog(testName, fmt.Sprintf("验证令牌: %s", tokenKey))

		// 模拟令牌验证（大部分有效）
		if len(token) > 10 {
			validTokens++
			s.AddLog(testName, fmt.Sprintf("✅ 令牌有效: %s", tokenKey))
		} else {
			expiredTokens++
			s.AddLog(testName, fmt.Sprintf("❌ 令牌无效: %s", tokenKey))
		}

		time.Sleep(3 * time.Millisecond)
	}

	// 模拟令牌撤销
	s.AddLog(testName, "撤销部分令牌")
	revokedTokens := 0
	for tokenKey := range s.tokens {
		if tokenKey == "temp_guest" {
			delete(s.tokens, tokenKey)
			revokedTokens++
			s.AddLog(testName, fmt.Sprintf("令牌已撤销: %s", tokenKey))
			break
		}
	}

	// 模拟令牌清理
	s.AddLog(testName, "清理过期令牌")
	cleanedTokens := 1 // 模拟清理1个过期令牌

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(generatedTokens))
	s.RecordMetric(testName, "success_count", int64(validTokens))
	s.RecordMetric(testName, "failure_count", int64(expiredTokens))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("令牌管理测试完成，耗时: %v，生成: %d，有效: %d，撤销: %d，清理: %d",
		duration, generatedTokens, validTokens, revokedTokens, cleanedTokens))

	// 验证令牌管理
	s.Assert().Equal(generatedTokens, len(tokenTypes), "生成的令牌数量应该正确")
	s.Assert().Greater(validTokens, 0, "应该有有效的令牌")
}

// simulateRequestFilteringTest 模拟请求过滤测试
func (s *SecurityIntegrationTestSuite) simulateRequestFilteringTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟请求过滤")

	// 模拟各种请求过滤场景
	requests := []struct {
		method    string
		path      string
		clientIP  string
		userAgent string
		allowed   bool
		reason    string
	}{
		{"GET", "/api/public", "*************", "Mozilla/5.0", true, "正常请求"},
		{"POST", "/api/admin", "*************", "Mozilla/5.0", true, "管理员请求"},
		{"GET", "/api/public", "***********", "Mozilla/5.0", false, "IP被封禁"},
		{"POST", "/api/upload", "*************", "curl/7.68.0", false, "可疑用户代理"},
		{"DELETE", "/api/users", "*************", "Mozilla/5.0", false, "危险操作"},
		{"GET", "/health", "127.0.0.1", "HealthCheck/1.0", true, "健康检查"},
	}

	allowedRequests := 0
	blockedRequests := 0

	for i, req := range requests {
		s.AddLog(testName, fmt.Sprintf("过滤请求 %d: %s %s from %s",
			i+1, req.method, req.path, req.clientIP))

		if req.allowed {
			allowedRequests++
			s.AddLog(testName, fmt.Sprintf("✅ 请求通过: %s", req.reason))
		} else {
			blockedRequests++
			s.AddLog(testName, fmt.Sprintf("🚫 请求被阻止: %s", req.reason))
		}

		time.Sleep(6 * time.Millisecond)
	}

	// 模拟安全规则检查
	s.AddLog(testName, "应用安全规则")
	securityRules := []string{
		"阻止来自恶意IP的请求",
		"限制API调用频率",
		"检查SQL注入攻击",
		"验证请求头完整性",
		"检查文件上传类型",
	}

	appliedRules := 0
	for _, rule := range securityRules {
		s.AddLog(testName, fmt.Sprintf("应用规则: %s", rule))
		appliedRules++
		time.Sleep(3 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(len(requests)))
	s.RecordMetric(testName, "success_count", int64(allowedRequests))
	s.RecordMetric(testName, "failure_count", int64(blockedRequests))

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("请求过滤测试完成，耗时: %v，通过: %d，阻止: %d，规则: %d",
		duration, allowedRequests, blockedRequests, appliedRules))

	// 验证过滤效果
	s.Assert().Greater(allowedRequests, 0, "应该有通过的请求")
	s.Assert().Greater(blockedRequests, 0, "应该有被阻止的请求")
	s.Assert().Equal(len(securityRules), appliedRules, "所有安全规则都应该被应用")
}

// simulateEncryptionTest 模拟加密解密测试
func (s *SecurityIntegrationTestSuite) simulateEncryptionTest(testName string) {
	start := time.Now()

	s.AddLog(testName, "模拟加密解密操作")

	// 模拟加密不同类型的数据
	testData := []struct {
		dataType string
		content  string
		algorithm string
	}{
		{"password", "user_password_123", "AES256"},
		{"token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", "AES256"},
		{"config", `{"database":"localhost","port":5432}`, "AES256"},
		{"session", "session_data_abc123", "AES128"},
		{"api_key", "sk_test_123456789abcdef", "AES256"},
	}

	encryptedData := make(map[string]string)
	encryptionCount := 0

	for _, data := range testData {
		s.AddLog(testName, fmt.Sprintf("加密数据: 类型=%s, 算法=%s", data.dataType, data.algorithm))

		// 模拟加密过程
		encrypted := fmt.Sprintf("encrypted_%s_%d", data.dataType, time.Now().UnixNano())
		encryptedData[data.dataType] = encrypted
		encryptionCount++

		s.AddLog(testName, fmt.Sprintf("加密完成: %s -> %s", data.dataType, encrypted))
		time.Sleep(5 * time.Millisecond)
	}

	// 模拟解密过程
	s.AddLog(testName, "开始解密数据")
	decryptionCount := 0
	successfulDecryptions := 0

	for dataType, encrypted := range encryptedData {
		s.AddLog(testName, fmt.Sprintf("解密数据: %s", dataType))

		// 模拟解密成功
		if len(encrypted) > 10 {
			successfulDecryptions++
			s.AddLog(testName, fmt.Sprintf("✅ 解密成功: %s", dataType))
		} else {
			s.AddLog(testName, fmt.Sprintf("❌ 解密失败: %s", dataType))
		}

		decryptionCount++
		time.Sleep(5 * time.Millisecond)
	}

	// 模拟密钥管理
	s.AddLog(testName, "管理加密密钥")
	keyOperations := []string{
		"生成主密钥",
		"派生子密钥",
		"轮换密钥",
		"备份密钥",
		"验证密钥完整性",
	}

	keyOpsCount := 0
	for _, operation := range keyOperations {
		s.AddLog(testName, fmt.Sprintf("执行密钥操作: %s", operation))
		keyOpsCount++
		time.Sleep(3 * time.Millisecond)
	}

	// 记录测试指标
	s.RecordMetric(testName, "request_count", int64(encryptionCount+decryptionCount))
	s.RecordMetric(testName, "success_count", int64(encryptionCount+successfulDecryptions))
	s.RecordMetric(testName, "avg_response_time", 5*time.Millisecond)

	duration := time.Since(start)
	s.AddLog(testName, fmt.Sprintf("加密解密测试完成，耗时: %v，加密: %d，解密成功: %d，密钥操作: %d",
		duration, encryptionCount, successfulDecryptions, keyOpsCount))

	// 验证加密解密
	s.Assert().Equal(len(testData), encryptionCount, "加密数量应该正确")
	s.Assert().Equal(encryptionCount, successfulDecryptions, "解密成功数量应该等于加密数量")
	s.Assert().Equal(len(keyOperations), keyOpsCount, "所有密钥操作都应该执行")
}

// 配置生成方法

// createAuthenticationConfig 创建认证配置
func (s *SecurityIntegrationTestSuite) createAuthenticationConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

security:
  enabled: true

  auth:
    type: "basic"
    token_expiry: "1h"
    users:
      - username: "admin"
        password: "admin123"
        roles: ["admin"]
      - username: "user1"
        password: "password1"
        roles: ["user"]

    jwt:
      secret: "test_jwt_secret_key_123456"
      issuer: "flexproxy-test"
      audience: "flexproxy-users"

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_auth_test.log"
`, s.securityPort, s.GetLogDir())
}

// createAuthorizationConfig 创建授权配置
func (s *SecurityIntegrationTestSuite) createAuthorizationConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

security:
  enabled: true

  auth:
    type: "rbac"
    token_expiry: "2h"

  authorization:
    enabled: true
    default_policy: "deny"

    roles:
      admin:
        permissions: ["read", "write", "delete", "admin"]
        resources: ["*"]
      user:
        permissions: ["read", "write"]
        resources: ["/api/profile", "/api/data"]
      guest:
        permissions: ["read"]
        resources: ["/api/public"]

    policies:
      - name: "admin_access"
        effect: "allow"
        principals: ["role:admin"]
        actions: ["*"]
        resources: ["*"]
      - name: "user_access"
        effect: "allow"
        principals: ["role:user"]
        actions: ["read", "write"]
        resources: ["/api/profile", "/api/data"]

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_authz_test.log"
`, s.securityPort, s.GetLogDir())
}

// createTLSConfig 创建 TLS 配置
func (s *SecurityIntegrationTestSuite) createTLSConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

security:
  enabled: true

  tls:
    enabled: true
    cert_file: "%s/test_cert.pem"
    key_file: "%s/test_key.pem"
    min_version: "1.2"
    max_version: "1.3"
    cipher_suites:
      - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
      - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
      - "TLS_RSA_WITH_AES_256_GCM_SHA384"

    client_auth: "none"
    verify_client_cert: false

server:
  host: "127.0.0.1"
  port: %d
  https_port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_tls_test.log"
`, s.GetEnvironment().GetDataDir(), s.GetEnvironment().GetDataDir(),
   s.securityPort, s.securityPort+1000, s.GetLogDir())
}

// createTokenManagementConfig 创建令牌管理配置
func (s *SecurityIntegrationTestSuite) createTokenManagementConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

security:
  enabled: true

  auth:
    type: "jwt"
    token_expiry: "1h"
    refresh_token_expiry: "24h"

  token_management:
    enabled: true
    issuer: "flexproxy-test"
    audience: "flexproxy-api"
    secret: "test_token_secret_key_987654321"

    token_types:
      access:
        expiry: "1h"
        scopes: ["read", "write"]
      refresh:
        expiry: "24h"
        scopes: ["refresh"]
      api:
        expiry: "720h"  # 30 days
        scopes: ["api:read", "api:write"]

    cleanup:
      enabled: true
      interval: "1h"
      max_age: "168h"  # 7 days

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_token_test.log"
`, s.securityPort, s.GetLogDir())
}

// createRequestFilteringConfig 创建请求过滤配置
func (s *SecurityIntegrationTestSuite) createRequestFilteringConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

  blocked_ips:
    - "***********/24"
    - "************/24"

  trusted_ips:
    - "127.0.0.1"
    - "***********/24"

security:
  enabled: true

  request_filtering:
    enabled: true

    ip_filtering:
      enabled: true
      whitelist_mode: false
      blocked_ips:
        - "***********"
        - "***********"
      trusted_ips:
        - "127.0.0.1"
        - "***********/24"

    user_agent_filtering:
      enabled: true
      blocked_patterns:
        - "curl/*"
        - "wget/*"
        - "*bot*"
        - "*crawler*"
      allowed_patterns:
        - "Mozilla/*"
        - "HealthCheck/*"

    path_filtering:
      enabled: true
      blocked_paths:
        - "/admin/*"
        - "/api/internal/*"
        - "*.php"
        - "*.asp"
      protected_methods:
        - "DELETE"
        - "PUT"

    rate_limiting:
      enabled: true
      global_rate: 1000
      per_ip_rate: 100
      burst_size: 50
      window_size: "1m"

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_filter_test.log"
`, s.securityPort, s.GetLogDir())
}

// createEncryptionConfig 创建加密配置
func (s *SecurityIntegrationTestSuite) createEncryptionConfig() string {
	return fmt.Sprintf(`
global:
  enable: true
  dns_lookup_mode: "system"

security:
  enabled: true

  encryption:
    algorithm: "aes256"
    key_length: 32
    mode: "GCM"

    key_management:
      enabled: true
      master_key: "test_master_key_123456789abcdef0123456789abcdef"
      key_rotation_interval: "720h"  # 30 days
      key_derivation: "PBKDF2"
      iterations: 100000

    data_encryption:
      enabled: true
      encrypt_passwords: true
      encrypt_tokens: true
      encrypt_config: false
      encrypt_logs: false

    at_rest_encryption:
      enabled: true
      database: true
      files: true
      cache: false

server:
  host: "127.0.0.1"
  port: %d

logging:
  enabled: true
  level: "debug"
  file: "%s/security_encryption_test.log"
`, s.securityPort, s.GetLogDir())
}

// TestSecurityIntegration 运行 Security 集成测试
func TestSecurityIntegration(t *testing.T) {
	framework.RunIntegrationTest(t, new(SecurityIntegrationTestSuite))
}
