#!/bin/bash

# ================================
# FlexProxy Actions & Events 测试运行脚本
# ================================
# 功能：专门运行Actions和Events的集成测试
# 作者：FlexProxy Team
# 版本：v1.0
# 创建时间：2025-01-11

set -e  # 遇到错误立即退出

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $CYAN "================================"
    print_message $CYAN "$1"
    print_message $CYAN "================================"
}

# 打印成功消息
print_success() {
    print_message $GREEN "✅ $1"
}

# 打印警告消息
print_warning() {
    print_message $YELLOW "⚠️  $1"
}

# 打印错误消息
print_error() {
    print_message $RED "❌ $1"
}

# 打印信息消息
print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查Go环境
check_go_environment() {
    print_info "检查Go环境..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go环境未安装，请先安装Go"
        exit 1
    fi
    
    go_version=$(go version | awk '{print $3}' | sed 's/go//')
    print_success "Go版本: $go_version"
}

# 检查项目依赖
check_dependencies() {
    print_info "检查项目依赖..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f "go.mod" ]; then
        print_error "未找到go.mod文件"
        exit 1
    fi
    
    # 下载依赖
    if go mod download; then
        print_success "依赖下载完成"
    else
        print_error "依赖下载失败"
        exit 1
    fi
    
    # 整理依赖
    if go mod tidy; then
        print_success "依赖整理完成"
    else
        print_warning "依赖整理有警告"
    fi
}

# 准备测试环境
prepare_test_environment() {
    print_info "准备测试环境..."
    
    # 创建日志目录
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$SCRIPT_DIR/test_results"
    
    # 清理旧的测试结果
    rm -f "$SCRIPT_DIR/test_results/actions_events_*.json"
    rm -f "$SCRIPT_DIR/test_results/actions_events_*.html"
    
    print_success "测试环境准备完成"
}

# 运行基础动作测试
run_basic_actions_tests() {
    print_title "运行基础动作测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试8种基础动作类型..."
    
    # 运行基础动作测试
    if go test ./modules -run "TestBasicActions" -v -timeout 60s; then
        print_success "基础动作测试通过"
        return 0
    else
        print_error "基础动作测试失败"
        return 1
    fi
}

# 运行扩展动作测试
run_extended_actions_tests() {
    print_title "运行扩展动作测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试8种扩展动作类型..."
    
    # 运行扩展动作测试
    if go test ./modules -run "TestExtendedActions" -v -timeout 60s; then
        print_success "扩展动作测试通过"
        return 0
    else
        print_error "扩展动作测试失败"
        return 1
    fi
}

# 运行修改动作测试
run_modify_actions_tests() {
    print_title "运行修改动作测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试请求和响应修改功能..."
    
    # 运行修改动作测试
    if go test ./modules -run "TestModifyActions" -v -timeout 60s; then
        print_success "修改动作测试通过"
        return 0
    else
        print_error "修改动作测试失败"
        return 1
    fi
}

# 运行触发器测试
run_trigger_tests() {
    print_title "运行触发器测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试12种触发器类型..."
    
    # 运行触发器测试
    if go test ./modules -run "TestTriggerTypes" -v -timeout 60s; then
        print_success "触发器测试通过"
        return 0
    else
        print_error "触发器测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_title "运行Actions & Events集成测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试动作和事件的集成功能..."
    
    # 运行集成测试
    if go test ./modules -run "TestActionsEventsIntegration" -v -timeout 120s; then
        print_success "集成测试通过"
        return 0
    else
        print_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    print_title "运行性能测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试动作执行和触发器检测性能..."
    
    # 运行性能测试
    if go test ./modules -run "TestPerformanceMetrics" -v -timeout 180s; then
        print_success "性能测试通过"
        return 0
    else
        print_error "性能测试失败"
        return 1
    fi
}

# 运行真实场景测试
run_real_world_tests() {
    print_title "运行真实场景测试"
    
    cd "$SCRIPT_DIR"
    
    print_info "测试真实世界使用场景..."
    
    # 运行真实场景测试
    if go test ./modules -run "TestRealWorldScenarios" -v -timeout 180s; then
        print_success "真实场景测试通过"
        return 0
    else
        print_error "真实场景测试失败"
        return 1
    fi
}

# 运行完整测试套件
run_full_test_suite() {
    print_title "运行完整Actions & Events测试套件"
    
    cd "$SCRIPT_DIR"
    
    print_info "运行完整的Actions & Events测试套件..."
    
    # 运行完整测试套件
    if go test ./modules -run "TestActionsEventsIntegrationSuite" -v -timeout 300s; then
        print_success "完整测试套件通过"
        return 0
    else
        print_error "完整测试套件失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    print_title "生成测试报告"
    
    cd "$SCRIPT_DIR"
    
    print_info "生成Actions & Events测试报告..."
    
    # 运行测试并生成JSON报告
    go test ./modules -run "TestActionsEventsIntegrationSuite" -v -json > "test_results/actions_events_$(date +%Y%m%d_%H%M%S).json" 2>&1 || true
    
    # 生成覆盖率报告
    go test ./modules -run "TestActionsEventsIntegrationSuite" -coverprofile="test_results/actions_events_coverage.out" > /dev/null 2>&1 || true
    
    if [ -f "test_results/actions_events_coverage.out" ]; then
        go tool cover -html="test_results/actions_events_coverage.out" -o "test_results/actions_events_coverage.html"
        print_success "覆盖率报告已生成: test_results/actions_events_coverage.html"
    fi
    
    print_success "测试报告生成完成"
}

# 显示测试结果摘要
show_test_summary() {
    print_title "测试结果摘要"
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 统计测试结果（这里简化处理）
    if [ -f "test_results/actions_events_coverage.out" ]; then
        print_info "测试覆盖率报告已生成"
    fi
    
    print_info "Actions & Events测试完成"
    print_info "详细结果请查看 test_results/ 目录"
}

# 清理测试环境
cleanup_test_environment() {
    print_info "清理测试环境..."
    
    # 清理临时文件
    rm -f "$PROJECT_ROOT"/*.test
    rm -f "$PROJECT_ROOT"/cpu.prof
    rm -f "$PROJECT_ROOT"/mem.prof
    
    print_success "测试环境清理完成"
}

# 显示帮助信息
show_help() {
    echo "FlexProxy Actions & Events 测试运行脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  basic           运行基础动作测试 (8种基础动作)"
    echo "  extended        运行扩展动作测试 (8种扩展动作)"
    echo "  modify          运行修改动作测试 (请求/响应修改)"
    echo "  triggers        运行触发器测试 (12种触发器)"
    echo "  integration     运行集成测试 (动作+事件集成)"
    echo "  performance     运行性能测试 (执行时间+并发)"
    echo "  scenarios       运行真实场景测试 (API网关+安全+负载均衡)"
    echo "  full            运行完整测试套件 (所有测试)"
    echo "  report          生成测试报告 (JSON+HTML+覆盖率)"
    echo "  all             运行所有测试并生成报告"
    echo "  clean           清理测试环境"
    echo "  help            显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 all          # 运行所有测试并生成报告"
    echo "  $0 basic        # 仅运行基础动作测试"
    echo "  $0 triggers     # 仅运行触发器测试"
    echo "  $0 performance  # 仅运行性能测试"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "basic")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_basic_actions_tests
            cleanup_test_environment
            ;;
        "extended")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_extended_actions_tests
            cleanup_test_environment
            ;;
        "modify")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_modify_actions_tests
            cleanup_test_environment
            ;;
        "triggers")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_trigger_tests
            cleanup_test_environment
            ;;
        "integration")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_integration_tests
            cleanup_test_environment
            ;;
        "performance")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_performance_tests
            cleanup_test_environment
            ;;
        "scenarios")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_real_world_tests
            cleanup_test_environment
            ;;
        "full")
            check_go_environment
            check_dependencies
            prepare_test_environment
            run_full_test_suite
            cleanup_test_environment
            ;;
        "report")
            check_go_environment
            check_dependencies
            prepare_test_environment
            generate_test_report
            show_test_summary
            cleanup_test_environment
            ;;
        "all")
            check_go_environment
            check_dependencies
            prepare_test_environment
            
            print_title "开始完整的Actions & Events测试流程"
            
            local overall_result=0
            
            # 运行所有测试
            run_basic_actions_tests || overall_result=1
            run_extended_actions_tests || overall_result=1
            run_modify_actions_tests || overall_result=1
            run_trigger_tests || overall_result=1
            run_integration_tests || overall_result=1
            run_performance_tests || overall_result=1
            run_real_world_tests || overall_result=1
            
            # 生成报告
            generate_test_report
            show_test_summary
            
            cleanup_test_environment
            
            if [ $overall_result -eq 0 ]; then
                print_success "🎉 所有Actions & Events测试通过！"
            else
                print_error "❌ 部分测试失败，请查看详细日志"
                exit 1
            fi
            ;;
        "clean")
            cleanup_test_environment
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
