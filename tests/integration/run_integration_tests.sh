#!/bin/bash

# FlexProxy 集成测试自动化脚本
# 用于启动模拟服务器并运行完整的集成测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TEST_DATA_DIR="$SCRIPT_DIR/test_data"
MOCK_SERVERS_DIR="$SCRIPT_DIR/mock_servers"
SCENARIOS_DIR="$SCRIPT_DIR/scenarios"
PERFORMANCE_DIR="$SCRIPT_DIR/performance"

# 服务器配置
DNS_PORT=15353  # 使用不同的端口避免冲突
PROXY_PORTS=(18080 18081 18082 18083 18084)  # 使用不同的端口范围
FLEXPROXY_PORT=19080

# PID文件目录
PID_DIR="$SCRIPT_DIR/pids"
LOG_DIR="$SCRIPT_DIR/logs"
REPORTS_DIR="$SCRIPT_DIR/reports"

# 创建必要的目录
mkdir -p "$PID_DIR" "$LOG_DIR" "$REPORTS_DIR"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "================================"
    print_message $BLUE "$1"
    print_message $BLUE "================================"
    echo
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 等待端口可用
wait_for_port() {
    local port=$1
    local timeout=${2:-30}
    local count=0
    
    print_message $YELLOW "等待端口 $port 可用..."
    
    while [ $count -lt $timeout ]; do
        if check_port $port; then
            print_message $GREEN "端口 $port 已可用"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    print_message $RED "等待端口 $port 超时"
    return 1
}

# 启动DNS服务器
start_dns_server() {
    print_title "启动模拟DNS服务器"
    
    if check_port $DNS_PORT; then
        print_message $YELLOW "端口 $DNS_PORT 已被占用，尝试停止现有服务..."
        pkill -f "dns_server.go" || true
        sleep 2
    fi
    
    cd "$MOCK_SERVERS_DIR"
    nohup go run dns_server.go $DNS_PORT > "$LOG_DIR/dns_server.log" 2>&1 &
    echo $! > "$PID_DIR/dns_server.pid"
    
    if wait_for_port $DNS_PORT; then
        print_message $GREEN "DNS服务器已启动在端口 $DNS_PORT"
    else
        print_message $RED "DNS服务器启动失败"
        return 1
    fi
}

# 启动代理服务器
start_proxy_servers() {
    print_title "启动模拟代理服务器"
    
    cd "$MOCK_SERVERS_DIR"
    
    for i in "${!PROXY_PORTS[@]}"; do
        local port=${PROXY_PORTS[$i]}
        local server_name="proxy-$((i+1))"
        
        if check_port $port; then
            print_message $YELLOW "端口 $port 已被占用，跳过启动 $server_name"
            continue
        fi
        
        print_message $CYAN "启动代理服务器 $server_name 在端口 $port..."
        
        nohup go run proxy_server.go $port $server_name > "$LOG_DIR/proxy_server_$port.log" 2>&1 &
        echo $! > "$PID_DIR/proxy_server_$port.pid"
        
        # 等待服务器启动
        if wait_for_port $port 10; then
            print_message $GREEN "代理服务器 $server_name 已启动"
        else
            print_message $RED "代理服务器 $server_name 启动失败"
        fi
    done
}

# 配置代理服务器
configure_proxy_servers() {
    print_title "配置代理服务器"
    
    # 配置不同的代理服务器特性
    local fast_ports=(8080 8081)
    local medium_ports=(8082 8083)
    local slow_ports=(8084)
    
    # 配置快速代理
    for port in "${fast_ports[@]}"; do
        if check_port $port; then
            print_message $CYAN "配置快速代理服务器 $port..."
            curl -s -X POST "http://127.0.0.1:$port/control/delay?delay=50" > /dev/null || true
            curl -s -X POST "http://127.0.0.1:$port/control/failure?rate=0.01" > /dev/null || true
        fi
    done
    
    # 配置中等代理
    for port in "${medium_ports[@]}"; do
        if check_port $port; then
            print_message $CYAN "配置中等代理服务器 $port..."
            curl -s -X POST "http://127.0.0.1:$port/control/delay?delay=200" > /dev/null || true
            curl -s -X POST "http://127.0.0.1:$port/control/failure?rate=0.05" > /dev/null || true
        fi
    done
    
    # 配置慢速代理
    for port in "${slow_ports[@]}"; do
        if check_port $port; then
            print_message $CYAN "配置慢速代理服务器 $port..."
            curl -s -X POST "http://127.0.0.1:$port/control/delay?delay=1000" > /dev/null || true
            curl -s -X POST "http://127.0.0.1:$port/control/failure?rate=0.1" > /dev/null || true
        fi
    done
    
    print_message $GREEN "代理服务器配置完成"
}

# 启动FlexProxy (模拟)
start_flexproxy() {
    print_title "启动FlexProxy (模拟)"
    
    if check_port $FLEXPROXY_PORT; then
        print_message $YELLOW "端口 $FLEXPROXY_PORT 已被占用，尝试停止现有服务..."
        pkill -f ":$FLEXPROXY_PORT" || true
        sleep 2
    fi
    
    # 这里应该启动实际的FlexProxy，但由于我们没有二进制文件，我们创建一个简单的模拟服务器
    cat > "$SCRIPT_DIR/mock_flexproxy.go" << 'EOF'
package main

import (
    "fmt"
    "log"
    "net/http"
    "os"
    "strconv"
)

func main() {
    port := 18080
    if len(os.Args) > 1 {
        if p, err := strconv.Atoi(os.Args[1]); err == nil {
            port = p
        }
    }
    
    http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"status":"healthy","service":"flexproxy-mock"}`)
    })
    
    http.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"proxy_count":5,"active_proxies":4,"requests_processed":100}`)
    })
    
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprint(w, `{"message":"Request processed by FlexProxy mock"}`)
    })
    
    log.Printf("FlexProxy模拟服务器启动在端口 %d", port)
    log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}
EOF
    
    nohup go run "$SCRIPT_DIR/mock_flexproxy.go" $FLEXPROXY_PORT > "$LOG_DIR/flexproxy.log" 2>&1 &
    echo $! > "$PID_DIR/flexproxy.pid"
    
    if wait_for_port $FLEXPROXY_PORT; then
        print_message $GREEN "FlexProxy已启动在端口 $FLEXPROXY_PORT"
    else
        print_message $RED "FlexProxy启动失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_title "运行集成测试"
    
    cd "$SCENARIOS_DIR"
    
    # 创建go.mod文件
    if [ ! -f "go.mod" ]; then
        go mod init integration_scenarios
        go get github.com/stretchr/testify
    fi
    
    print_message $CYAN "运行全局配置集成测试..."
    if go test -v -timeout=30m .; then
        print_message $GREEN "集成测试通过"
    else
        print_message $RED "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    print_title "运行性能测试"
    
    cd "$PERFORMANCE_DIR"
    
    # 创建go.mod文件
    if [ ! -f "go.mod" ]; then
        go mod init performance_tests
        go get github.com/stretchr/testify
    fi
    
    print_message $CYAN "运行性能基准测试..."
    if go test -v -bench=. -benchmem -timeout=30m .; then
        print_message $GREEN "性能测试完成"
    else
        print_message $RED "性能测试失败"
        return 1
    fi
}

# 停止所有服务
stop_all_services() {
    print_title "停止所有服务"
    
    # 停止FlexProxy
    if [ -f "$PID_DIR/flexproxy.pid" ]; then
        local pid=$(cat "$PID_DIR/flexproxy.pid")
        if kill -0 $pid 2>/dev/null; then
            print_message $YELLOW "停止FlexProxy (PID: $pid)..."
            kill $pid
            rm -f "$PID_DIR/flexproxy.pid"
        fi
    fi
    
    # 停止代理服务器
    for port in "${PROXY_PORTS[@]}"; do
        if [ -f "$PID_DIR/proxy_server_$port.pid" ]; then
            local pid=$(cat "$PID_DIR/proxy_server_$port.pid")
            if kill -0 $pid 2>/dev/null; then
                print_message $YELLOW "停止代理服务器 $port (PID: $pid)..."
                kill $pid
                rm -f "$PID_DIR/proxy_server_$port.pid"
            fi
        fi
    done
    
    # 停止DNS服务器
    if [ -f "$PID_DIR/dns_server.pid" ]; then
        local pid=$(cat "$PID_DIR/dns_server.pid")
        if kill -0 $pid 2>/dev/null; then
            print_message $YELLOW "停止DNS服务器 (PID: $pid)..."
            kill $pid
            rm -f "$PID_DIR/dns_server.pid"
        fi
    fi
    
    # 清理临时文件
    rm -f "$SCRIPT_DIR/mock_flexproxy.go"
    
    print_message $GREEN "所有服务已停止"
}

# 显示服务状态
show_status() {
    print_title "服务状态"
    
    # 检查DNS服务器
    if check_port $DNS_PORT; then
        print_message $GREEN "✓ DNS服务器运行在端口 $DNS_PORT"
    else
        print_message $RED "✗ DNS服务器未运行"
    fi
    
    # 检查代理服务器
    for port in "${PROXY_PORTS[@]}"; do
        if check_port $port; then
            print_message $GREEN "✓ 代理服务器运行在端口 $port"
        else
            print_message $RED "✗ 代理服务器未运行在端口 $port"
        fi
    done
    
    # 检查FlexProxy
    if check_port $FLEXPROXY_PORT; then
        print_message $GREEN "✓ FlexProxy运行在端口 $FLEXPROXY_PORT"
    else
        print_message $RED "✗ FlexProxy未运行"
    fi
}

# 显示帮助信息
show_help() {
    echo "FlexProxy 集成测试自动化脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  start           启动所有模拟服务器"
    echo "  stop            停止所有服务器"
    echo "  restart         重启所有服务器"
    echo "  status          显示服务状态"
    echo "  test            运行集成测试"
    echo "  modules         运行所有模块测试 (Server, Proxy, Cache, Logging, Monitoring, Security, DNS, Actions & Events)"
    echo "  scenarios       运行所有场景测试 (端到端, 并发, 配置热重载)"
    echo "  reports         生成测试报告 (HTML + JSON)"
    echo "  performance     运行性能测试"
    echo "  full            完整测试流程 (启动服务器 + 运行测试 + 停止服务器)"
    echo "  clean           清理日志和PID文件"
    echo "  help            显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 full         # 运行完整测试流程"
    echo "  $0 start        # 仅启动服务器"
    echo "  $0 test         # 仅运行测试"
}

# 运行模块测试
run_module_tests() {
    print_title "运行模块集成测试"

    cd "$SCRIPT_DIR"

    print_message $BLUE "🧩 运行所有模块测试..."

    # 运行各个模块测试
    modules=("server" "proxy" "cache" "logging" "monitoring" "security" "dns_service" "actions_events")

    for module in "${modules[@]}"; do
        print_message $CYAN "📦 运行 ${module} 模块测试..."
        if [[ "$module" == "actions_events" ]]; then
            # Actions & Events 测试使用特殊的测试套件名称
            if go test ./modules -run "TestActionsEventsIntegrationSuite" -v; then
                print_message $GREEN "✅ ${module} 模块测试通过"
            else
                print_message $RED "❌ ${module} 模块测试失败"
            fi
        else
            if go test ./modules -run "Test${module^}Integration" -v; then
                print_message $GREEN "✅ ${module} 模块测试通过"
            else
                print_message $RED "❌ ${module} 模块测试失败"
            fi
        fi
    done

    print_message $GREEN "🎉 模块测试完成"
}

# 运行场景测试
run_scenario_tests() {
    print_title "运行场景集成测试"

    cd "$SCRIPT_DIR"

    print_message $BLUE "🎬 运行所有场景测试..."

    # 运行各个场景测试
    scenarios=("EndToEnd" "Concurrent" "ConfigReload")

    for scenario in "${scenarios[@]}"; do
        print_message $CYAN "🎭 运行 ${scenario} 场景测试..."
        if go test ./scenarios -run "Test${scenario}Integration" -v; then
            print_message $GREEN "✅ ${scenario} 场景测试通过"
        else
            print_message $RED "❌ ${scenario} 场景测试失败"
        fi
    done

    print_message $GREEN "🎉 场景测试完成"
}

# 生成测试报告
generate_test_reports() {
    print_title "生成测试报告"

    cd "$SCRIPT_DIR"

    print_message $BLUE "📊 生成测试报告..."

    # 创建报告目录
    mkdir -p "$REPORTS_DIR"

    # 运行测试并生成报告
    if go test . -output="$REPORTS_DIR" -format=both -v; then
        print_message $GREEN "✅ 测试报告生成成功"
        print_message $CYAN "📄 JSON 报告: $REPORTS_DIR/integration_test_report_*.json"
        print_message $CYAN "🌐 HTML 报告: $REPORTS_DIR/integration_test_report_*.html"
        print_message $CYAN "📋 摘要报告: $REPORTS_DIR/test_summary_*.txt"
    else
        print_message $RED "❌ 测试报告生成失败"
    fi
}

# 清理文件
clean_files() {
    print_title "清理文件"

    rm -rf "$PID_DIR" "$LOG_DIR" "$REPORTS_DIR"
    rm -f "$SCRIPT_DIR/mock_flexproxy.go"

    print_message $GREEN "清理完成"
}

# 主函数
main() {
    case "${1:-full}" in
        start)
            start_dns_server
            start_proxy_servers
            configure_proxy_servers
            start_flexproxy
            show_status
            ;;
        stop)
            stop_all_services
            ;;
        restart)
            stop_all_services
            sleep 2
            start_dns_server
            start_proxy_servers
            configure_proxy_servers
            start_flexproxy
            show_status
            ;;
        status)
            show_status
            ;;
        test)
            run_integration_tests
            ;;
        modules)
            run_module_tests
            ;;
        scenarios)
            run_scenario_tests
            ;;
        reports)
            generate_test_reports
            ;;
        performance)
            run_performance_tests
            ;;
        full)
            print_title "FlexProxy 完整集成测试流程"
            
            # 启动服务器
            start_dns_server
            start_proxy_servers
            configure_proxy_servers
            start_flexproxy
            
            # 等待服务器稳定
            sleep 5
            show_status
            
            # 运行测试
            if run_integration_tests && run_performance_tests; then
                print_message $GREEN "✅ 所有测试通过！"
            else
                print_message $RED "❌ 部分测试失败"
            fi
            
            # 停止服务器
            stop_all_services
            ;;
        clean)
            stop_all_services
            clean_files
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 设置信号处理
trap 'stop_all_services; exit 130' INT TERM

# 运行主函数
main "$@"
