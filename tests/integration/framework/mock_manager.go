// Package framework 提供集成测试的基础框架
package framework

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/miekg/dns"
)

// MockManager Mock 服务管理器
type MockManager struct {
	ctx         context.Context
	cancel      context.CancelFunc
	logDir      string
	servers     map[string]*MockServer
	mu          sync.RWMutex
	logger      *log.Logger
}

// MockServer Mock 服务器接口
type MockServer interface {
	Start() error
	Stop() error
	GetAddress() string
	GetPort() int
	IsRunning() bool
}

// HTTPMockServer HTTP Mock 服务器
type HTTPMockServer struct {
	name     string
	address  string
	port     int
	server   *http.Server
	mux      *http.ServeMux
	running  bool
	mu       sync.RWMutex
	logger   *log.Logger
	logFile  *os.File
}

// DNSMockServer DNS Mock 服务器
type DNSMockServer struct {
	name     string
	address  string
	port     int
	server   *dns.Server
	running  bool
	mu       sync.RWMutex
	logger   *log.Logger
	logFile  *os.File
	records  map[string][]string // 域名到IP的映射
}

// ProxyMockServer 代理 Mock 服务器
type ProxyMockServer struct {
	name     string
	address  string
	port     int
	server   *http.Server
	running  bool
	mu       sync.RWMutex
	logger   *log.Logger
	logFile  *os.File
	delay    time.Duration // 模拟延迟
	failRate float64       // 失败率
}

// NewMockManager 创建 Mock 管理器
func NewMockManager(ctx context.Context, logDir string) *MockManager {
	ctx, cancel := context.WithCancel(ctx)
	
	logFile := filepath.Join(logDir, "mock_manager.log")
	logger := log.New(os.Stdout, "[MockManager] ", log.LstdFlags|log.Lshortfile)
	
	return &MockManager{
		ctx:     ctx,
		cancel:  cancel,
		logDir:  logDir,
		servers: make(map[string]*MockServer),
		logger:  logger,
	}
}

// CreateHTTPMockServer 创建 HTTP Mock 服务器
func (mm *MockManager) CreateHTTPMockServer(name string, port int) (*HTTPMockServer, error) {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	
	if port == 0 {
		// 自动分配端口
		listener, err := net.Listen("tcp", ":0")
		if err != nil {
			return nil, fmt.Errorf("分配端口失败: %v", err)
		}
		port = listener.Addr().(*net.TCPAddr).Port
		listener.Close()
	}
	
	address := fmt.Sprintf("127.0.0.1:%d", port)
	logFile := filepath.Join(mm.logDir, fmt.Sprintf("http_mock_%s.log", name))
	
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("创建日志文件失败: %v", err)
	}
	
	logger := log.New(file, fmt.Sprintf("[HTTPMock-%s] ", name), log.LstdFlags)
	mux := http.NewServeMux()
	
	server := &HTTPMockServer{
		name:    name,
		address: address,
		port:    port,
		mux:     mux,
		logger:  logger,
		logFile: file,
	}
	
	server.server = &http.Server{
		Addr:    address,
		Handler: mux,
	}
	
	// 添加默认路由
	server.setupDefaultRoutes()
	
	mockServer := MockServer(server)
	mm.servers[name] = &mockServer
	
	mm.logger.Printf("创建 HTTP Mock 服务器: %s, 地址: %s", name, address)
	return server, nil
}

// CreateDNSMockServer 创建 DNS Mock 服务器
func (mm *MockManager) CreateDNSMockServer(name string, port int) (*DNSMockServer, error) {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	
	if port == 0 {
		port = 5353 // 默认 DNS 端口
	}
	
	address := fmt.Sprintf("127.0.0.1:%d", port)
	logFile := filepath.Join(mm.logDir, fmt.Sprintf("dns_mock_%s.log", name))
	
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("创建日志文件失败: %v", err)
	}
	
	logger := log.New(file, fmt.Sprintf("[DNSMock-%s] ", name), log.LstdFlags)
	
	server := &DNSMockServer{
		name:    name,
		address: address,
		port:    port,
		logger:  logger,
		logFile: file,
		records: make(map[string][]string),
	}
	
	// 添加默认记录
	server.setupDefaultRecords()
	
	mockServer := MockServer(server)
	mm.servers[name] = &mockServer
	
	mm.logger.Printf("创建 DNS Mock 服务器: %s, 地址: %s", name, address)
	return server, nil
}

// CreateProxyMockServer 创建代理 Mock 服务器
func (mm *MockManager) CreateProxyMockServer(name string, port int, delay time.Duration, failRate float64) (*ProxyMockServer, error) {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	
	if port == 0 {
		// 自动分配端口
		listener, err := net.Listen("tcp", ":0")
		if err != nil {
			return nil, fmt.Errorf("分配端口失败: %v", err)
		}
		port = listener.Addr().(*net.TCPAddr).Port
		listener.Close()
	}
	
	address := fmt.Sprintf("127.0.0.1:%d", port)
	logFile := filepath.Join(mm.logDir, fmt.Sprintf("proxy_mock_%s.log", name))
	
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("创建日志文件失败: %v", err)
	}
	
	logger := log.New(file, fmt.Sprintf("[ProxyMock-%s] ", name), log.LstdFlags)
	
	server := &ProxyMockServer{
		name:     name,
		address:  address,
		port:     port,
		logger:   logger,
		logFile:  file,
		delay:    delay,
		failRate: failRate,
	}
	
	// 设置代理处理器
	server.setupProxyHandler()
	
	mockServer := MockServer(server)
	mm.servers[name] = &mockServer
	
	mm.logger.Printf("创建代理 Mock 服务器: %s, 地址: %s, 延迟: %v, 失败率: %.2f", 
		name, address, delay, failRate)
	return server, nil
}

// GetServer 获取 Mock 服务器
func (mm *MockManager) GetServer(name string) MockServer {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	if server, exists := mm.servers[name]; exists {
		return *server
	}
	return nil
}

// StartAll 启动所有 Mock 服务器
func (mm *MockManager) StartAll() error {
	mm.mu.RLock()
	servers := make([]MockServer, 0, len(mm.servers))
	for _, server := range mm.servers {
		servers = append(servers, *server)
	}
	mm.mu.RUnlock()
	
	for _, server := range servers {
		if err := server.Start(); err != nil {
			mm.logger.Printf("启动 Mock 服务器失败: %v", err)
			return err
		}
	}
	
	mm.logger.Printf("所有 Mock 服务器启动完成")
	return nil
}

// StopAll 停止所有 Mock 服务器
func (mm *MockManager) StopAll() error {
	mm.mu.RLock()
	servers := make([]MockServer, 0, len(mm.servers))
	for _, server := range mm.servers {
		servers = append(servers, *server)
	}
	mm.mu.RUnlock()
	
	for _, server := range servers {
		if err := server.Stop(); err != nil {
			mm.logger.Printf("停止 Mock 服务器失败: %v", err)
		}
	}
	
	mm.logger.Printf("所有 Mock 服务器停止完成")
	return nil
}

// Stop 停止 Mock 管理器
func (mm *MockManager) Stop() error {
	defer mm.cancel()
	return mm.StopAll()
}

// WaitForReady 等待所有服务器就绪
func (mm *MockManager) WaitForReady(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(mm.ctx, timeout)
	defer cancel()
	
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("等待 Mock 服务器就绪超时")
		case <-ticker.C:
			if mm.allServersReady() {
				mm.logger.Printf("所有 Mock 服务器就绪")
				return nil
			}
		}
	}
}

// allServersReady 检查所有服务器是否就绪
func (mm *MockManager) allServersReady() bool {
	mm.mu.RLock()
	defer mm.mu.RUnlock()

	for _, server := range mm.servers {
		if !(*server).IsRunning() {
			return false
		}
	}
	return true
}

// HTTPMockServer 实现

// Start 启动 HTTP Mock 服务器
func (hms *HTTPMockServer) Start() error {
	hms.mu.Lock()
	defer hms.mu.Unlock()

	if hms.running {
		return fmt.Errorf("HTTP Mock 服务器 %s 已在运行", hms.name)
	}

	go func() {
		hms.logger.Printf("启动 HTTP Mock 服务器: %s", hms.address)
		if err := hms.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			hms.logger.Printf("HTTP Mock 服务器错误: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	hms.running = true
	return nil
}

// Stop 停止 HTTP Mock 服务器
func (hms *HTTPMockServer) Stop() error {
	hms.mu.Lock()
	defer hms.mu.Unlock()

	if !hms.running {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := hms.server.Shutdown(ctx)
	if err != nil {
		hms.logger.Printf("停止 HTTP Mock 服务器失败: %v", err)
	}

	if hms.logFile != nil {
		hms.logFile.Close()
	}

	hms.running = false
	hms.logger.Printf("HTTP Mock 服务器已停止: %s", hms.name)
	return err
}

// GetAddress 获取服务器地址
func (hms *HTTPMockServer) GetAddress() string {
	return hms.address
}

// GetPort 获取服务器端口
func (hms *HTTPMockServer) GetPort() int {
	return hms.port
}

// IsRunning 检查服务器是否运行
func (hms *HTTPMockServer) IsRunning() bool {
	hms.mu.RLock()
	defer hms.mu.RUnlock()
	return hms.running
}

// AddRoute 添加路由
func (hms *HTTPMockServer) AddRoute(pattern string, handler http.HandlerFunc) {
	hms.mux.HandleFunc(pattern, func(w http.ResponseWriter, r *http.Request) {
		hms.logger.Printf("请求: %s %s", r.Method, r.URL.Path)
		handler(w, r)
	})
}

// setupDefaultRoutes 设置默认路由
func (hms *HTTPMockServer) setupDefaultRoutes() {
	// 健康检查
	hms.AddRoute("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok","server":"` + hms.name + `"}`))
	})

	// 状态检查
	hms.AddRoute("/status", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"running","name":"` + hms.name + `","address":"` + hms.address + `"}`))
	})

	// 延迟测试
	hms.AddRoute("/delay/", func(w http.ResponseWriter, r *http.Request) {
		delay := 1 * time.Second // 默认延迟
		time.Sleep(delay)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message":"delayed response","delay":"` + delay.String() + `"}`))
	})

	// 错误测试
	hms.AddRoute("/error/", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte(`{"error":"simulated error","server":"` + hms.name + `"}`))
	})

	// 默认处理器
	hms.AddRoute("/", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message":"mock response","path":"` + r.URL.Path + `","server":"` + hms.name + `"}`))
	})
}

// DNSMockServer 实现

// Start 启动 DNS Mock 服务器
func (dms *DNSMockServer) Start() error {
	dms.mu.Lock()
	defer dms.mu.Unlock()

	if dms.running {
		return fmt.Errorf("DNS Mock 服务器 %s 已在运行", dms.name)
	}

	dms.server = &dns.Server{
		Addr: dms.address,
		Net:  "udp",
	}

	dns.HandleFunc(".", dms.handleDNSRequest)

	go func() {
		dms.logger.Printf("启动 DNS Mock 服务器: %s", dms.address)
		if err := dms.server.ListenAndServe(); err != nil {
			dms.logger.Printf("DNS Mock 服务器错误: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	dms.running = true
	return nil
}

// Stop 停止 DNS Mock 服务器
func (dms *DNSMockServer) Stop() error {
	dms.mu.Lock()
	defer dms.mu.Unlock()

	if !dms.running {
		return nil
	}

	if dms.server != nil {
		err := dms.server.Shutdown()
		if err != nil {
			dms.logger.Printf("停止 DNS Mock 服务器失败: %v", err)
		}
	}

	if dms.logFile != nil {
		dms.logFile.Close()
	}

	dms.running = false
	dms.logger.Printf("DNS Mock 服务器已停止: %s", dms.name)
	return nil
}

// GetAddress 获取服务器地址
func (dms *DNSMockServer) GetAddress() string {
	return dms.address
}

// GetPort 获取服务器端口
func (dms *DNSMockServer) GetPort() int {
	return dms.port
}

// IsRunning 检查服务器是否运行
func (dms *DNSMockServer) IsRunning() bool {
	dms.mu.RLock()
	defer dms.mu.RUnlock()
	return dms.running
}

// AddRecord 添加 DNS 记录
func (dms *DNSMockServer) AddRecord(domain string, ips []string) {
	dms.mu.Lock()
	defer dms.mu.Unlock()
	dms.records[domain] = ips
	dms.logger.Printf("添加 DNS 记录: %s -> %v", domain, ips)
}

// setupDefaultRecords 设置默认 DNS 记录
func (dms *DNSMockServer) setupDefaultRecords() {
	dms.records["test.local"] = []string{"127.0.0.1"}
	dms.records["example.test"] = []string{"*************"}
	dms.records["mock.server"] = []string{"********", "********"}
	dms.records["integration.test"] = []string{"**********"}
}

// handleDNSRequest 处理 DNS 请求
func (dms *DNSMockServer) handleDNSRequest(w dns.ResponseWriter, r *dns.Msg) {
	m := new(dns.Msg)
	m.SetReply(r)
	m.Authoritative = true

	for _, q := range r.Question {
		dms.logger.Printf("DNS 查询: %s %s", q.Name, dns.TypeToString[q.Qtype])

		if q.Qtype == dns.TypeA {
			domain := q.Name
			if domain[len(domain)-1] == '.' {
				domain = domain[:len(domain)-1]
			}

			dms.mu.RLock()
			ips, exists := dms.records[domain]
			dms.mu.RUnlock()

			if exists {
				for _, ip := range ips {
					rr := &dns.A{
						Hdr: dns.RR_Header{
							Name:   q.Name,
							Rrtype: dns.TypeA,
							Class:  dns.ClassINET,
							Ttl:    300,
						},
						A: net.ParseIP(ip),
					}
					m.Answer = append(m.Answer, rr)
				}
				dms.logger.Printf("DNS 响应: %s -> %v", domain, ips)
			} else {
				dms.logger.Printf("DNS 记录未找到: %s", domain)
			}
		}
	}

	w.WriteMsg(m)
}

// ProxyMockServer 实现

// Start 启动代理 Mock 服务器
func (pms *ProxyMockServer) Start() error {
	pms.mu.Lock()
	defer pms.mu.Unlock()

	if pms.running {
		return fmt.Errorf("代理 Mock 服务器 %s 已在运行", pms.name)
	}

	go func() {
		pms.logger.Printf("启动代理 Mock 服务器: %s", pms.address)
		if err := pms.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			pms.logger.Printf("代理 Mock 服务器错误: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
	pms.running = true
	return nil
}

// Stop 停止代理 Mock 服务器
func (pms *ProxyMockServer) Stop() error {
	pms.mu.Lock()
	defer pms.mu.Unlock()

	if !pms.running {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := pms.server.Shutdown(ctx)
	if err != nil {
		pms.logger.Printf("停止代理 Mock 服务器失败: %v", err)
	}

	if pms.logFile != nil {
		pms.logFile.Close()
	}

	pms.running = false
	pms.logger.Printf("代理 Mock 服务器已停止: %s", pms.name)
	return err
}

// GetAddress 获取服务器地址
func (pms *ProxyMockServer) GetAddress() string {
	return pms.address
}

// GetPort 获取服务器端口
func (pms *ProxyMockServer) GetPort() int {
	return pms.port
}

// IsRunning 检查服务器是否运行
func (pms *ProxyMockServer) IsRunning() bool {
	pms.mu.RLock()
	defer pms.mu.RUnlock()
	return pms.running
}

// setupProxyHandler 设置代理处理器
func (pms *ProxyMockServer) setupProxyHandler() {
	mux := http.NewServeMux()

	// 代理处理器
	mux.HandleFunc("/", pms.handleProxyRequest)

	pms.server = &http.Server{
		Addr:    pms.address,
		Handler: mux,
	}
}

// handleProxyRequest 处理代理请求
func (pms *ProxyMockServer) handleProxyRequest(w http.ResponseWriter, r *http.Request) {
	start := time.Now()

	// 模拟延迟
	if pms.delay > 0 {
		time.Sleep(pms.delay)
	}

	// 模拟失败
	if pms.failRate > 0 && pms.shouldFail() {
		pms.logger.Printf("模拟代理失败: %s %s", r.Method, r.URL.String())
		w.WriteHeader(http.StatusBadGateway)
		w.Write([]byte(`{"error":"proxy failure simulation"}`))
		return
	}

	// 记录请求
	pms.logger.Printf("代理请求: %s %s -> 成功, 延迟: %v",
		r.Method, r.URL.String(), time.Since(start))

	// 返回成功响应
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"success","proxy":"` + pms.name + `","delay":"` + pms.delay.String() + `"}`))
}

// shouldFail 判断是否应该失败
func (pms *ProxyMockServer) shouldFail() bool {
	// 简单的随机失败逻辑
	return time.Now().UnixNano()%100 < int64(pms.failRate*100)
}
