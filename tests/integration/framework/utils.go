// Package framework 提供集成测试的基础框架
package framework

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// TestUtils 测试工具函数集合
type TestUtils struct{}

// NewTestUtils 创建测试工具实例
func NewTestUtils() *TestUtils {
	return &TestUtils{}
}

// FindFreePort 查找可用端口
func (tu *TestUtils) FindFreePort() (int, error) {
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		return 0, err
	}
	defer listener.Close()
	
	addr := listener.Addr().(*net.TCPAddr)
	return addr.Port, nil
}

// FindFreePorts 查找多个可用端口
func (tu *TestUtils) FindFreePorts(count int) ([]int, error) {
	ports := make([]int, 0, count)
	
	for i := 0; i < count; i++ {
		port, err := tu.FindFreePort()
		if err != nil {
			return nil, err
		}
		ports = append(ports, port)
	}
	
	return ports, nil
}

// IsPortOpen 检查端口是否开放
func (tu *TestUtils) IsPortOpen(host string, port int) bool {
	timeout := 1 * time.Second
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(host, strconv.Itoa(port)), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// WaitForPort 等待端口开放
func (tu *TestUtils) WaitForPort(host string, port int, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("等待端口 %s:%d 开放超时", host, port)
		case <-ticker.C:
			if tu.IsPortOpen(host, port) {
				return nil
			}
		}
	}
}

// HTTPGet 发送 HTTP GET 请求
func (tu *TestUtils) HTTPGet(url string, timeout time.Duration) (*http.Response, error) {
	client := &http.Client{
		Timeout: timeout,
	}
	
	return client.Get(url)
}

// HTTPPost 发送 HTTP POST 请求
func (tu *TestUtils) HTTPPost(url string, contentType string, body io.Reader, timeout time.Duration) (*http.Response, error) {
	client := &http.Client{
		Timeout: timeout,
	}
	
	return client.Post(url, contentType, body)
}

// HTTPGetJSON 发送 HTTP GET 请求并解析 JSON 响应
func (tu *TestUtils) HTTPGetJSON(url string, timeout time.Duration, result interface{}) error {
	resp, err := tu.HTTPGet(url, timeout)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP 请求失败: %d", resp.StatusCode)
	}
	
	return json.NewDecoder(resp.Body).Decode(result)
}

// HTTPPostJSON 发送 HTTP POST 请求并解析 JSON 响应
func (tu *TestUtils) HTTPPostJSON(url string, payload interface{}, timeout time.Duration, result interface{}) error {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	
	resp, err := tu.HTTPPost(url, "application/json", bytes.NewBuffer(jsonData), timeout)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP 请求失败: %d", resp.StatusCode)
	}
	
	if result != nil {
		return json.NewDecoder(resp.Body).Decode(result)
	}
	
	return nil
}

// ReadResponseBody 读取响应体内容
func (tu *TestUtils) ReadResponseBody(resp *http.Response) (string, error) {
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	return string(body), nil
}

// GenerateTestData 生成测试数据
func (tu *TestUtils) GenerateTestData(dataType string, count int) interface{} {
	switch dataType {
	case "proxies":
		return tu.generateProxyList(count)
	case "domains":
		return tu.generateDomainList(count)
	case "ips":
		return tu.generateIPList(count)
	default:
		return nil
	}
}

// generateProxyList 生成代理列表
func (tu *TestUtils) generateProxyList(count int) []string {
	proxies := make([]string, count)
	basePort := 18080
	
	for i := 0; i < count; i++ {
		proxies[i] = fmt.Sprintf("127.0.0.1:%d", basePort+i)
	}
	
	return proxies
}

// generateDomainList 生成域名列表
func (tu *TestUtils) generateDomainList(count int) []string {
	domains := make([]string, count)
	
	for i := 0; i < count; i++ {
		domains[i] = fmt.Sprintf("test%d.example.com", i+1)
	}
	
	return domains
}

// generateIPList 生成 IP 列表
func (tu *TestUtils) generateIPList(count int) []string {
	ips := make([]string, count)
	
	for i := 0; i < count; i++ {
		ips[i] = fmt.Sprintf("192.168.1.%d", i+1)
	}
	
	return ips
}

// GetSystemInfo 获取系统信息
func (tu *TestUtils) GetSystemInfo() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return map[string]interface{}{
		"os":           runtime.GOOS,
		"arch":         runtime.GOARCH,
		"go_version":   runtime.Version(),
		"num_cpu":      runtime.NumCPU(),
		"num_goroutine": runtime.NumGoroutine(),
		"memory": map[string]interface{}{
			"alloc_mb":      bToMb(m.Alloc),
			"total_alloc_mb": bToMb(m.TotalAlloc),
			"sys_mb":        bToMb(m.Sys),
			"num_gc":        m.NumGC,
		},
	}
}

// bToMb 字节转换为 MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// CreateTestConfig 创建测试配置
func (tu *TestUtils) CreateTestConfig(configType string, params map[string]interface{}) string {
	switch configType {
	case "basic":
		return tu.createBasicConfig(params)
	case "proxy":
		return tu.createProxyConfig(params)
	case "dns":
		return tu.createDNSConfig(params)
	default:
		return ""
	}
}

// createBasicConfig 创建基础配置
func (tu *TestUtils) createBasicConfig(params map[string]interface{}) string {
	config := `
global:
  enable: true
  proxy_file: "%s"
  ip_rotation_mode: "%s"
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: %d
  read_timeout: "10s"
  write_timeout: "10s"

logging:
  enabled: true
  level: "debug"
  file: "%s"
`
	
	proxyFile := getStringParam(params, "proxy_file", "./test_data/proxies.txt")
	rotationMode := getStringParam(params, "ip_rotation_mode", "sequential")
	port := getIntParam(params, "port", 18080)
	logFile := getStringParam(params, "log_file", "./logs/test.log")
	
	return fmt.Sprintf(config, proxyFile, rotationMode, port, logFile)
}

// createProxyConfig 创建代理配置
func (tu *TestUtils) createProxyConfig(params map[string]interface{}) string {
	// 实现代理配置生成逻辑
	return ""
}

// createDNSConfig 创建 DNS 配置
func (tu *TestUtils) createDNSConfig(params map[string]interface{}) string {
	// 实现 DNS 配置生成逻辑
	return ""
}

// getStringParam 获取字符串参数
func getStringParam(params map[string]interface{}, key string, defaultValue string) string {
	if value, exists := params[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// getIntParam 获取整数参数
func getIntParam(params map[string]interface{}, key string, defaultValue int) int {
	if value, exists := params[key]; exists {
		if num, ok := value.(int); ok {
			return num
		}
	}
	return defaultValue
}

// FileExists 检查文件是否存在
func (tu *TestUtils) FileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// CreateDirectory 创建目录
func (tu *TestUtils) CreateDirectory(dirPath string) error {
	return os.MkdirAll(dirPath, 0755)
}

// WriteFile 写入文件
func (tu *TestUtils) WriteFile(filePath string, content []byte) error {
	return os.WriteFile(filePath, content, 0644)
}

// ReadFile 读取文件
func (tu *TestUtils) ReadFile(filePath string) ([]byte, error) {
	return os.ReadFile(filePath)
}

// ParseURL 解析 URL
func (tu *TestUtils) ParseURL(rawURL string) (host string, port int, err error) {
	parts := strings.Split(rawURL, ":")
	if len(parts) != 2 {
		return "", 0, fmt.Errorf("无效的 URL 格式: %s", rawURL)
	}
	
	host = parts[0]
	port, err = strconv.Atoi(parts[1])
	if err != nil {
		return "", 0, fmt.Errorf("无效的端口号: %s", parts[1])
	}
	
	return host, port, nil
}
