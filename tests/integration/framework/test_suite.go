// Package framework 提供集成测试的基础框架
package framework

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
)

// IntegrationTestSuite 集成测试套件基类
type IntegrationTestSuite struct {
	suite.Suite
	ctx           context.Context
	cancel        context.CancelFunc
	testID        string
	startTime     time.Time
	mockManager   *MockManager
	environment   *TestEnvironment
	cleanup       []func() error
	mu            sync.RWMutex
	testResults   map[string]*TestResult
	logDir        string
	reportDir     string
}

// TestResult 测试结果
type TestResult struct {
	TestName    string        `json:"test_name"`
	Status      string        `json:"status"`      // PASS, FAIL, SKIP
	Duration    time.Duration `json:"duration"`
	StartTime   time.Time     `json:"start_time"`
	EndTime     time.Time     `json:"end_time"`
	ErrorMsg    string        `json:"error_msg,omitempty"`
	Metrics     TestMetrics   `json:"metrics"`
	Logs        []string      `json:"logs,omitempty"`
}

// TestMetrics 测试指标
type TestMetrics struct {
	RequestCount    int64         `json:"request_count"`
	SuccessCount    int64         `json:"success_count"`
	FailureCount    int64         `json:"failure_count"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	MaxResponseTime time.Duration `json:"max_response_time"`
	MinResponseTime time.Duration `json:"min_response_time"`
	ThroughputRPS   float64       `json:"throughput_rps"`
	ErrorRate       float64       `json:"error_rate"`
	MemoryUsageMB   float64       `json:"memory_usage_mb"`
	CPUUsagePercent float64       `json:"cpu_usage_percent"`
}

// SetupSuite 测试套件初始化
func (s *IntegrationTestSuite) SetupSuite() {
	s.startTime = time.Now()
	s.testID = fmt.Sprintf("test_%d", s.startTime.Unix())
	s.ctx, s.cancel = context.WithCancel(context.Background())
	s.testResults = make(map[string]*TestResult)
	
	// 创建测试目录
	s.logDir = filepath.Join("logs", s.testID)
	s.reportDir = filepath.Join("reports", s.testID)
	
	err := os.MkdirAll(s.logDir, 0755)
	s.Require().NoError(err, "创建日志目录失败")
	
	err = os.MkdirAll(s.reportDir, 0755)
	s.Require().NoError(err, "创建报告目录失败")
	
	// 初始化 Mock 管理器
	s.mockManager = NewMockManager(s.ctx, s.logDir)
	s.AddCleanup(s.mockManager.Stop)
	
	// 初始化测试环境
	s.environment = NewTestEnvironment(s.ctx, s.testID, s.logDir)
	s.AddCleanup(s.environment.Stop)
	
	s.T().Logf("测试套件初始化完成: %s", s.testID)
}

// TearDownSuite 测试套件清理
func (s *IntegrationTestSuite) TearDownSuite() {
	defer s.cancel()
	
	// 执行清理函数
	s.mu.RLock()
	cleanupFuncs := make([]func() error, len(s.cleanup))
	copy(cleanupFuncs, s.cleanup)
	s.mu.RUnlock()
	
	for i := len(cleanupFuncs) - 1; i >= 0; i-- {
		if err := cleanupFuncs[i](); err != nil {
			s.T().Logf("清理函数执行失败: %v", err)
		}
	}
	
	// 生成测试报告
	s.generateTestReport()
	
	duration := time.Since(s.startTime)
	s.T().Logf("测试套件完成: %s, 耗时: %v", s.testID, duration)
}

// SetupTest 单个测试初始化
func (s *IntegrationTestSuite) SetupTest() {
	testName := s.T().Name()
	s.mu.Lock()
	s.testResults[testName] = &TestResult{
		TestName:  testName,
		Status:    "RUNNING",
		StartTime: time.Now(),
		Metrics:   TestMetrics{},
		Logs:      make([]string, 0),
	}
	s.mu.Unlock()
	
	s.T().Logf("开始测试: %s", testName)
}

// TearDownTest 单个测试清理
func (s *IntegrationTestSuite) TearDownTest() {
	testName := s.T().Name()
	
	s.mu.Lock()
	if result, exists := s.testResults[testName]; exists {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		
		if s.T().Failed() {
			result.Status = "FAIL"
			result.ErrorMsg = "测试失败"
		} else if s.T().Skipped() {
			result.Status = "SKIP"
		} else {
			result.Status = "PASS"
		}
	}
	s.mu.Unlock()
	
	s.T().Logf("测试完成: %s", testName)
}

// AddCleanup 添加清理函数
func (s *IntegrationTestSuite) AddCleanup(cleanup func() error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.cleanup = append(s.cleanup, cleanup)
}

// GetMockManager 获取 Mock 管理器
func (s *IntegrationTestSuite) GetMockManager() *MockManager {
	return s.mockManager
}

// GetEnvironment 获取测试环境
func (s *IntegrationTestSuite) GetEnvironment() *TestEnvironment {
	return s.environment
}

// GetContext 获取测试上下文
func (s *IntegrationTestSuite) GetContext() context.Context {
	return s.ctx
}

// GetTestID 获取测试 ID
func (s *IntegrationTestSuite) GetTestID() string {
	return s.testID
}

// GetLogDir 获取日志目录
func (s *IntegrationTestSuite) GetLogDir() string {
	return s.logDir
}

// RecordMetric 记录测试指标
func (s *IntegrationTestSuite) RecordMetric(testName string, metric string, value interface{}) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if result, exists := s.testResults[testName]; exists {
		switch metric {
		case "request_count":
			if v, ok := value.(int64); ok {
				result.Metrics.RequestCount = v
			}
		case "success_count":
			if v, ok := value.(int64); ok {
				result.Metrics.SuccessCount = v
			}
		case "failure_count":
			if v, ok := value.(int64); ok {
				result.Metrics.FailureCount = v
			}
		case "avg_response_time":
			if v, ok := value.(time.Duration); ok {
				result.Metrics.AvgResponseTime = v
			}
		case "max_response_time":
			if v, ok := value.(time.Duration); ok {
				result.Metrics.MaxResponseTime = v
			}
		case "min_response_time":
			if v, ok := value.(time.Duration); ok {
				result.Metrics.MinResponseTime = v
			}
		case "throughput_rps":
			if v, ok := value.(float64); ok {
				result.Metrics.ThroughputRPS = v
			}
		case "error_rate":
			if v, ok := value.(float64); ok {
				result.Metrics.ErrorRate = v
			}
		case "memory_usage_mb":
			if v, ok := value.(float64); ok {
				result.Metrics.MemoryUsageMB = v
			}
		case "cpu_usage_percent":
			if v, ok := value.(float64); ok {
				result.Metrics.CPUUsagePercent = v
			}
		}
	}
}

// AddLog 添加测试日志
func (s *IntegrationTestSuite) AddLog(testName string, logMsg string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if result, exists := s.testResults[testName]; exists {
		result.Logs = append(result.Logs, fmt.Sprintf("[%s] %s", 
			time.Now().Format("15:04:05.000"), logMsg))
	}
}

// WaitForCondition 等待条件满足
func (s *IntegrationTestSuite) WaitForCondition(condition func() bool, timeout time.Duration, message string) bool {
	ctx, cancel := context.WithTimeout(s.ctx, timeout)
	defer cancel()
	
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			s.T().Logf("等待条件超时: %s", message)
			return false
		case <-ticker.C:
			if condition() {
				return true
			}
		}
	}
}

// AssertEventually 断言条件最终满足
func (s *IntegrationTestSuite) AssertEventually(condition func() bool, timeout time.Duration, message string) {
	if !s.WaitForCondition(condition, timeout, message) {
		s.Fail(fmt.Sprintf("条件未在超时时间内满足: %s", message))
	}
}

// generateTestReport 生成测试报告
func (s *IntegrationTestSuite) generateTestReport() {
	// 这里会在后续实现详细的报告生成逻辑
	s.T().Logf("生成测试报告到: %s", s.reportDir)
}

// RunIntegrationTest 运行集成测试的辅助函数
func RunIntegrationTest(t *testing.T, testSuite suite.TestingSuite) {
	suite.Run(t, testSuite)
}
