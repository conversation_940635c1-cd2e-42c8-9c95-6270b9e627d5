// Package framework 提供集成测试的基础框架
package framework

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TestEnvironment 测试环境管理器
type TestEnvironment struct {
	ctx       context.Context
	cancel    context.CancelFunc
	testID    string
	logDir    string
	dataDir   string
	configDir string
	tempFiles []string
	processes map[string]*TestProcess
	mu        sync.RWMutex
	logger    *log.Logger
}

// TestProcess 测试进程信息
type TestProcess struct {
	Name    string
	PID     int
	Command string
	Args    []string
	LogFile string
	Started time.Time
	Status  string // running, stopped, failed
}

// EnvironmentConfig 环境配置
type EnvironmentConfig struct {
	TestID      string
	LogLevel    string
	DataDir     string
	ConfigDir   string
	CleanupMode string // auto, manual, never
}

// NewTestEnvironment 创建测试环境
func NewTestEnvironment(ctx context.Context, testID string, logDir string) *TestEnvironment {
	ctx, cancel := context.WithCancel(ctx)
	
	dataDir := filepath.Join("test_data", testID)
	configDir := filepath.Join(dataDir, "configs")
	
	// 创建目录
	os.MkdirAll(dataDir, 0755)
	os.MkdirAll(configDir, 0755)
	
	logger := log.New(os.Stdout, "[TestEnv] ", log.LstdFlags|log.Lshortfile)
	
	return &TestEnvironment{
		ctx:       ctx,
		cancel:    cancel,
		testID:    testID,
		logDir:    logDir,
		dataDir:   dataDir,
		configDir: configDir,
		tempFiles: make([]string, 0),
		processes: make(map[string]*TestProcess),
		logger:    logger,
	}
}

// CreateTempFile 创建临时文件
func (te *TestEnvironment) CreateTempFile(name string, content []byte) (string, error) {
	te.mu.Lock()
	defer te.mu.Unlock()
	
	filePath := filepath.Join(te.dataDir, name)
	
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}
	
	if err := ioutil.WriteFile(filePath, content, 0644); err != nil {
		return "", fmt.Errorf("创建临时文件失败: %v", err)
	}
	
	te.tempFiles = append(te.tempFiles, filePath)
	te.logger.Printf("创建临时文件: %s", filePath)
	
	return filePath, nil
}

// CreateConfigFile 创建配置文件
func (te *TestEnvironment) CreateConfigFile(name string, config interface{}) (string, error) {
	// 这里可以根据需要实现不同格式的配置文件生成
	// 暂时使用简单的字符串内容
	var content []byte
	
	switch v := config.(type) {
	case string:
		content = []byte(v)
	case []byte:
		content = v
	default:
		return "", fmt.Errorf("不支持的配置类型: %T", config)
	}
	
	return te.CreateTempFile(filepath.Join("configs", name), content)
}

// CreateProxyListFile 创建代理列表文件
func (te *TestEnvironment) CreateProxyListFile(proxies []string) (string, error) {
	content := ""
	for _, proxy := range proxies {
		content += proxy + "\n"
	}
	
	return te.CreateTempFile("proxies.txt", []byte(content))
}

// CreateHostsFile 创建 hosts 文件
func (te *TestEnvironment) CreateHostsFile(hosts map[string]string) (string, error) {
	content := ""
	for ip, hostname := range hosts {
		content += fmt.Sprintf("%s %s\n", ip, hostname)
	}
	
	return te.CreateTempFile("hosts.txt", []byte(content))
}

// GetDataDir 获取数据目录
func (te *TestEnvironment) GetDataDir() string {
	return te.dataDir
}

// GetConfigDir 获取配置目录
func (te *TestEnvironment) GetConfigDir() string {
	return te.configDir
}

// GetLogDir 获取日志目录
func (te *TestEnvironment) GetLogDir() string {
	return te.logDir
}

// RegisterProcess 注册测试进程
func (te *TestEnvironment) RegisterProcess(name string, process *TestProcess) {
	te.mu.Lock()
	defer te.mu.Unlock()
	
	te.processes[name] = process
	te.logger.Printf("注册测试进程: %s (PID: %d)", name, process.PID)
}

// GetProcess 获取测试进程
func (te *TestEnvironment) GetProcess(name string) (*TestProcess, bool) {
	te.mu.RLock()
	defer te.mu.RUnlock()
	
	process, exists := te.processes[name]
	return process, exists
}

// ListProcesses 列出所有进程
func (te *TestEnvironment) ListProcesses() map[string]*TestProcess {
	te.mu.RLock()
	defer te.mu.RUnlock()
	
	result := make(map[string]*TestProcess)
	for name, process := range te.processes {
		result[name] = process
	}
	return result
}

// WaitForFile 等待文件创建
func (te *TestEnvironment) WaitForFile(filePath string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(te.ctx, timeout)
	defer cancel()
	
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("等待文件创建超时: %s", filePath)
		case <-ticker.C:
			if _, err := os.Stat(filePath); err == nil {
				te.logger.Printf("文件已创建: %s", filePath)
				return nil
			}
		}
	}
}

// WaitForPort 等待端口可用
func (te *TestEnvironment) WaitForPort(port int, timeout time.Duration) error {
	// 这里可以实现端口检查逻辑
	// 暂时使用简单的延迟
	time.Sleep(100 * time.Millisecond)
	te.logger.Printf("端口检查: %d", port)
	return nil
}

// Cleanup 清理测试环境
func (te *TestEnvironment) Cleanup() error {
	te.mu.Lock()
	defer te.mu.Unlock()
	
	// 清理临时文件
	for _, filePath := range te.tempFiles {
		if err := os.Remove(filePath); err != nil {
			te.logger.Printf("删除临时文件失败: %s, 错误: %v", filePath, err)
		} else {
			te.logger.Printf("删除临时文件: %s", filePath)
		}
	}
	
	// 清理目录
	if err := os.RemoveAll(te.dataDir); err != nil {
		te.logger.Printf("删除数据目录失败: %s, 错误: %v", te.dataDir, err)
	} else {
		te.logger.Printf("删除数据目录: %s", te.dataDir)
	}
	
	te.logger.Printf("测试环境清理完成: %s", te.testID)
	return nil
}

// Stop 停止测试环境
func (te *TestEnvironment) Stop() error {
	defer te.cancel()
	return te.Cleanup()
}

// GetTestID 获取测试 ID
func (te *TestEnvironment) GetTestID() string {
	return te.testID
}

// IsHealthy 检查环境健康状态
func (te *TestEnvironment) IsHealthy() bool {
	// 检查关键目录是否存在
	if _, err := os.Stat(te.dataDir); os.IsNotExist(err) {
		return false
	}
	
	if _, err := os.Stat(te.configDir); os.IsNotExist(err) {
		return false
	}
	
	return true
}

// GetStats 获取环境统计信息
func (te *TestEnvironment) GetStats() map[string]interface{} {
	te.mu.RLock()
	defer te.mu.RUnlock()
	
	return map[string]interface{}{
		"test_id":     te.testID,
		"data_dir":    te.dataDir,
		"config_dir":  te.configDir,
		"log_dir":     te.logDir,
		"temp_files":  len(te.tempFiles),
		"processes":   len(te.processes),
		"healthy":     te.IsHealthy(),
	}
}
