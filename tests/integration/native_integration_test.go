// Package integration 原生集成测试 (不依赖外部包)
package integration

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// TestNativeIntegration 原生集成测试
func TestNativeIntegration(t *testing.T) {
	fmt.Println("🚀 FlexProxy 原生集成测试开始")
	
	// 测试基本功能
	t.Run("BasicFunctionality", func(t *testing.T) {
		testBasicFunctionality(t)
	})
	
	// 测试HTTP服务器功能
	t.Run("HTTPServerFunctionality", func(t *testing.T) {
		testHTTPServerFunctionality(t)
	})
	
	// 测试文件系统操作
	t.Run("FileSystemOperations", func(t *testing.T) {
		testFileSystemOperations(t)
	})
	
	// 测试配置处理
	t.Run("ConfigurationHandling", func(t *testing.T) {
		testConfigurationHandling(t)
	})
	
	// 测试并发处理
	t.Run("ConcurrentProcessing", func(t *testing.T) {
		testConcurrentProcessing(t)
	})
	
	// 测试错误处理
	t.Run("ErrorHandling", func(t *testing.T) {
		testErrorHandling(t)
	})
	
	fmt.Println("🎉 FlexProxy 原生集成测试完成")
}

// testBasicFunctionality 测试基本功能
func testBasicFunctionality(t *testing.T) {
	fmt.Println("📦 测试基本功能...")
	
	// 测试字符串操作
	testString := "FlexProxy Integration Test"
	if len(testString) == 0 {
		t.Error("字符串长度不应为0")
	}
	
	// 测试时间操作
	start := time.Now()
	time.Sleep(1 * time.Millisecond)
	duration := time.Since(start)
	
	if duration < 1*time.Millisecond {
		t.Errorf("时间测试失败: 期望至少 1ms, 实际 %v", duration)
	}
	
	fmt.Printf("✅ 基本功能测试通过: 字符串长度=%d, 时间测试=%v\n", len(testString), duration)
}

// testHTTPServerFunctionality 测试HTTP服务器功能
func testHTTPServerFunctionality(t *testing.T) {
	fmt.Println("🌐 测试HTTP服务器功能...")
	
	// 创建测试服务器
	mux := http.NewServeMux()
	
	// 健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"healthy"}`)
	})
	
	// 代理端点
	mux.HandleFunc("/proxy", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprint(w, `{"proxy":"active","mode":"test"}`)
	})
	
	// 错误端点
	mux.HandleFunc("/error", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, `{"error":"test error"}`)
	})
	
	server := httptest.NewServer(mux)
	defer server.Close()
	
	fmt.Printf("📡 测试服务器启动: %s\n", server.URL)
	
	// 测试健康检查
	resp, err := http.Get(server.URL + "/health")
	if err != nil {
		t.Fatalf("健康检查请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("健康检查状态码错误: 期望 %d, 实际 %d", http.StatusOK, resp.StatusCode)
	}
	
	// 测试代理端点
	resp, err = http.Get(server.URL + "/proxy")
	if err != nil {
		t.Fatalf("代理请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("代理请求状态码错误: 期望 %d, 实际 %d", http.StatusOK, resp.StatusCode)
	}
	
	// 测试错误处理
	resp, err = http.Get(server.URL + "/error")
	if err != nil {
		t.Fatalf("错误请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusInternalServerError {
		t.Errorf("错误请求状态码错误: 期望 %d, 实际 %d", http.StatusInternalServerError, resp.StatusCode)
	}
	
	fmt.Printf("✅ HTTP服务器功能测试通过\n")
}

// testFileSystemOperations 测试文件系统操作
func testFileSystemOperations(t *testing.T) {
	fmt.Println("📁 测试文件系统操作...")
	
	// 创建临时目录
	tempDir := filepath.Join(os.TempDir(), "flexproxy_native_test")
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	// 测试配置文件写入
	configFile := filepath.Join(tempDir, "test_config.yaml")
	configContent := `
global:
  enable: true
  ip_rotation_mode: "random"
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
  file: "./logs/test.log"
`
	
	err = os.WriteFile(configFile, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("写入配置文件失败: %v", err)
	}
	
	// 测试配置文件读取
	content, err := os.ReadFile(configFile)
	if err != nil {
		t.Fatalf("读取配置文件失败: %v", err)
	}
	
	if len(content) == 0 {
		t.Error("配置文件内容为空")
	}
	
	// 验证配置内容
	contentStr := string(content)
	if !strings.Contains(contentStr, "enable: true") {
		t.Error("配置文件内容不正确")
	}
	
	fmt.Printf("✅ 文件系统操作测试通过: 配置文件大小=%d字节\n", len(content))
}

// testConfigurationHandling 测试配置处理
func testConfigurationHandling(t *testing.T) {
	fmt.Println("⚙️ 测试配置处理...")
	
	// 模拟配置结构
	type Config struct {
		Global struct {
			Enable           bool   `yaml:"enable"`
			IPRotationMode   string `yaml:"ip_rotation_mode"`
			DNSLookupMode    string `yaml:"dns_lookup_mode"`
		} `yaml:"global"`
		Server struct {
			Host string `yaml:"host"`
			Port int    `yaml:"port"`
		} `yaml:"server"`
	}
	
	// 测试配置验证
	validModes := []string{"random", "sequential", "quality", "smart"}
	testMode := "random"
	
	isValid := false
	for _, mode := range validModes {
		if mode == testMode {
			isValid = true
			break
		}
	}
	
	if !isValid {
		t.Errorf("配置验证失败: 无效的IP轮换模式 %s", testMode)
	}
	
	// 测试端口验证
	testPort := 18080
	if testPort < 1 || testPort > 65535 {
		t.Errorf("配置验证失败: 无效的端口号 %d", testPort)
	}
	
	fmt.Printf("✅ 配置处理测试通过: 模式=%s, 端口=%d\n", testMode, testPort)
}

// testConcurrentProcessing 测试并发处理
func testConcurrentProcessing(t *testing.T) {
	fmt.Println("⚡ 测试并发处理...")
	
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟一些处理时间
		time.Sleep(10 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"ok"}`)
	}))
	defer server.Close()
	
	concurrency := 5
	requests := 3
	results := make(chan bool, concurrency*requests)
	
	start := time.Now()
	
	// 启动并发请求
	for i := 0; i < concurrency; i++ {
		go func(workerID int) {
			client := &http.Client{Timeout: 5 * time.Second}
			
			for j := 0; j < requests; j++ {
				resp, err := client.Get(server.URL)
				if err != nil {
					results <- false
					continue
				}
				resp.Body.Close()
				
				results <- (resp.StatusCode == http.StatusOK)
			}
		}(i)
	}
	
	// 收集结果
	successCount := 0
	totalCount := 0
	
	for i := 0; i < concurrency*requests; i++ {
		success := <-results
		totalCount++
		if success {
			successCount++
		}
	}
	
	duration := time.Since(start)
	successRate := float64(successCount) / float64(totalCount) * 100
	
	if successRate < 90.0 {
		t.Errorf("并发测试成功率过低: %.2f%%", successRate)
	}
	
	fmt.Printf("✅ 并发处理测试通过: %d/%d 成功 (%.2f%%), 耗时: %v\n", 
		successCount, totalCount, successRate, duration)
}

// testErrorHandling 测试错误处理
func testErrorHandling(t *testing.T) {
	fmt.Println("🚨 测试错误处理...")
	
	// 测试网络错误处理
	client := &http.Client{Timeout: 1 * time.Millisecond}
	
	// 尝试连接不存在的服务器
	_, err := client.Get("http://localhost:99999/test")
	if err == nil {
		t.Error("应该产生连接错误")
	}
	
	// 测试超时错误
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond) // 超过客户端超时时间
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()
	
	_, err = client.Get(server.URL)
	if err == nil {
		t.Error("应该产生超时错误")
	}
	
	// 测试文件操作错误
	_, err = os.ReadFile("/nonexistent/path/file.txt")
	if err == nil {
		t.Error("应该产生文件不存在错误")
	}
	
	fmt.Printf("✅ 错误处理测试通过\n")
}

// BenchmarkHTTPRequests HTTP请求基准测试
func BenchmarkHTTPRequests(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"ok"}`)
	}))
	defer server.Close()
	
	client := &http.Client{Timeout: 5 * time.Second}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		resp, err := client.Get(server.URL)
		if err != nil {
			b.Fatalf("基准测试请求失败: %v", err)
		}
		resp.Body.Close()
	}
}

// BenchmarkStringOperations 字符串操作基准测试
func BenchmarkStringOperations(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = fmt.Sprintf("FlexProxy-Test-%d", i)
	}
}

// TestArchitectureValidation 测试架构验证
func TestArchitectureValidation(t *testing.T) {
	fmt.Println("🏗️ 测试架构验证...")
	
	// 验证目录结构
	directories := []string{
		"framework",
		"modules", 
		"scenarios",
		"reports",
	}
	
	for _, dir := range directories {
		if _, err := os.Stat(dir); err != nil {
			t.Logf("⚠️  目录不存在或无法访问: %s", dir)
		} else {
			fmt.Printf("✅ 目录验证通过: %s\n", dir)
		}
	}
	
	// 验证关键文件
	files := []string{
		"go.mod",
		"README.md",
	}
	
	for _, file := range files {
		if _, err := os.Stat(file); err != nil {
			t.Logf("⚠️  文件不存在或无法访问: %s", file)
		} else {
			fmt.Printf("✅ 文件验证通过: %s\n", file)
		}
	}
	
	fmt.Printf("✅ 架构验证测试完成\n")
}
