// Package reports 提供集成测试报告生成功能
package reports

import (
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"sort"
	"time"
)

// TestReport 测试报告
type TestReport struct {
	GeneratedAt     time.Time           `json:"generated_at"`
	TestSuite       string              `json:"test_suite"`
	TotalTests      int                 `json:"total_tests"`
	PassedTests     int                 `json:"passed_tests"`
	FailedTests     int                 `json:"failed_tests"`
	SkippedTests    int                 `json:"skipped_tests"`
	Duration        time.Duration       `json:"duration"`
	SuccessRate     float64             `json:"success_rate"`
	TestResults     []TestResult        `json:"test_results"`
	ModuleResults   []ModuleResult      `json:"module_results"`
	PerformanceData PerformanceData     `json:"performance_data"`
	SystemInfo      map[string]interface{} `json:"system_info"`
	Configuration   ConfigurationInfo   `json:"configuration"`
}

// TestResult 单个测试结果
type TestResult struct {
	Name        string            `json:"name"`
	Module      string            `json:"module"`
	Status      string            `json:"status"`
	Duration    time.Duration     `json:"duration"`
	ErrorMsg    string            `json:"error_msg,omitempty"`
	Metrics     map[string]interface{} `json:"metrics"`
	Logs        []string          `json:"logs"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     time.Time         `json:"end_time"`
}

// ModuleResult 模块测试结果
type ModuleResult struct {
	Name         string        `json:"name"`
	TotalTests   int           `json:"total_tests"`
	PassedTests  int           `json:"passed_tests"`
	FailedTests  int           `json:"failed_tests"`
	Duration     time.Duration `json:"duration"`
	SuccessRate  float64       `json:"success_rate"`
	Coverage     float64       `json:"coverage"`
}

// PerformanceData 性能数据
type PerformanceData struct {
	TotalRequests     int64         `json:"total_requests"`
	SuccessfulRequests int64        `json:"successful_requests"`
	FailedRequests    int64         `json:"failed_requests"`
	AvgResponseTime   time.Duration `json:"avg_response_time"`
	MaxResponseTime   time.Duration `json:"max_response_time"`
	MinResponseTime   time.Duration `json:"min_response_time"`
	ThroughputRPS     float64       `json:"throughput_rps"`
	ErrorRate         float64       `json:"error_rate"`
	MemoryUsageMB     float64       `json:"memory_usage_mb"`
	CPUUsagePercent   float64       `json:"cpu_usage_percent"`
}

// ConfigurationInfo 配置信息
type ConfigurationInfo struct {
	TestEnvironment string            `json:"test_environment"`
	MockServers     []MockServerInfo  `json:"mock_servers"`
	TestData        map[string]interface{} `json:"test_data"`
	Settings        map[string]interface{} `json:"settings"`
}

// MockServerInfo Mock 服务器信息
type MockServerInfo struct {
	Name    string `json:"name"`
	Type    string `json:"type"`
	Address string `json:"address"`
	Status  string `json:"status"`
}

// ReportGenerator 报告生成器
type ReportGenerator struct {
	outputDir    string
	templateDir  string
	testResults  []TestResult
	moduleResults []ModuleResult
	systemInfo   map[string]interface{}
	config       ConfigurationInfo
}

// NewReportGenerator 创建报告生成器
func NewReportGenerator(outputDir, templateDir string) *ReportGenerator {
	return &ReportGenerator{
		outputDir:     outputDir,
		templateDir:   templateDir,
		testResults:   make([]TestResult, 0),
		moduleResults: make([]ModuleResult, 0),
		systemInfo:    make(map[string]interface{}),
		config:        ConfigurationInfo{},
	}
}

// AddTestResult 添加测试结果
func (rg *ReportGenerator) AddTestResult(result TestResult) {
	rg.testResults = append(rg.testResults, result)
}

// AddModuleResult 添加模块结果
func (rg *ReportGenerator) AddModuleResult(result ModuleResult) {
	rg.moduleResults = append(rg.moduleResults, result)
}

// SetSystemInfo 设置系统信息
func (rg *ReportGenerator) SetSystemInfo(info map[string]interface{}) {
	rg.systemInfo = info
}

// SetConfiguration 设置配置信息
func (rg *ReportGenerator) SetConfiguration(config ConfigurationInfo) {
	rg.config = config
}

// GenerateReport 生成完整报告
func (rg *ReportGenerator) GenerateReport(suiteName string) (*TestReport, error) {
	report := &TestReport{
		GeneratedAt:     time.Now(),
		TestSuite:       suiteName,
		TestResults:     rg.testResults,
		ModuleResults:   rg.moduleResults,
		SystemInfo:      rg.systemInfo,
		Configuration:   rg.config,
	}
	
	// 计算统计数据
	rg.calculateStatistics(report)
	
	// 计算性能数据
	rg.calculatePerformanceData(report)
	
	return report, nil
}

// calculateStatistics 计算统计数据
func (rg *ReportGenerator) calculateStatistics(report *TestReport) {
	report.TotalTests = len(report.TestResults)
	
	var totalDuration time.Duration
	
	for _, result := range report.TestResults {
		switch result.Status {
		case "PASSED":
			report.PassedTests++
		case "FAILED":
			report.FailedTests++
		case "SKIPPED":
			report.SkippedTests++
		}
		totalDuration += result.Duration
	}
	
	report.Duration = totalDuration
	
	if report.TotalTests > 0 {
		report.SuccessRate = float64(report.PassedTests) / float64(report.TotalTests) * 100
	}
}

// calculatePerformanceData 计算性能数据
func (rg *ReportGenerator) calculatePerformanceData(report *TestReport) {
	var totalRequests, successfulRequests, failedRequests int64
	var totalResponseTime, maxResponseTime time.Duration
	var minResponseTime = time.Hour // 初始化为很大的值
	var totalMemory, totalCPU float64
	var sampleCount int
	
	for _, result := range report.TestResults {
		if metrics := result.Metrics; metrics != nil {
			if reqCount, ok := metrics["request_count"].(int64); ok {
				totalRequests += reqCount
			}
			if successCount, ok := metrics["success_count"].(int64); ok {
				successfulRequests += successCount
			}
			if failCount, ok := metrics["failure_count"].(int64); ok {
				failedRequests += failCount
			}
			if respTime, ok := metrics["avg_response_time"].(time.Duration); ok {
				totalResponseTime += respTime
				if respTime > maxResponseTime {
					maxResponseTime = respTime
				}
				if respTime < minResponseTime {
					minResponseTime = respTime
				}
			}
			if memory, ok := metrics["memory_usage_mb"].(float64); ok {
				totalMemory += memory
				sampleCount++
			}
			if cpu, ok := metrics["cpu_usage_percent"].(float64); ok {
				totalCPU += cpu
			}
		}
	}
	
	report.PerformanceData = PerformanceData{
		TotalRequests:      totalRequests,
		SuccessfulRequests: successfulRequests,
		FailedRequests:     failedRequests,
		MaxResponseTime:    maxResponseTime,
		MinResponseTime:    minResponseTime,
	}
	
	if len(report.TestResults) > 0 {
		report.PerformanceData.AvgResponseTime = totalResponseTime / time.Duration(len(report.TestResults))
	}
	
	if totalRequests > 0 {
		report.PerformanceData.ErrorRate = float64(failedRequests) / float64(totalRequests) * 100
		report.PerformanceData.ThroughputRPS = float64(totalRequests) / report.Duration.Seconds()
	}
	
	if sampleCount > 0 {
		report.PerformanceData.MemoryUsageMB = totalMemory / float64(sampleCount)
		report.PerformanceData.CPUUsagePercent = totalCPU / float64(sampleCount)
	}
}

// SaveJSONReport 保存 JSON 格式报告
func (rg *ReportGenerator) SaveJSONReport(report *TestReport, filename string) error {
	filePath := filepath.Join(rg.outputDir, filename)
	
	// 确保输出目录存在
	if err := os.MkdirAll(rg.outputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}
	
	// 序列化为 JSON
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化报告失败: %v", err)
	}
	
	// 写入文件
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入 JSON 报告失败: %v", err)
	}
	
	return nil
}

// SaveHTMLReport 保存 HTML 格式报告
func (rg *ReportGenerator) SaveHTMLReport(report *TestReport, filename string) error {
	filePath := filepath.Join(rg.outputDir, filename)
	
	// 确保输出目录存在
	if err := os.MkdirAll(rg.outputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}
	
	// 加载 HTML 模板
	tmpl, err := rg.loadHTMLTemplate()
	if err != nil {
		return fmt.Errorf("加载 HTML 模板失败: %v", err)
	}
	
	// 创建输出文件
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建 HTML 报告文件失败: %v", err)
	}
	defer file.Close()
	
	// 执行模板
	if err := tmpl.Execute(file, report); err != nil {
		return fmt.Errorf("执行 HTML 模板失败: %v", err)
	}
	
	return nil
}

// loadHTMLTemplate 加载 HTML 模板
func (rg *ReportGenerator) loadHTMLTemplate() (*template.Template, error) {
	templatePath := filepath.Join(rg.templateDir, "report_template.html")
	
	// 如果模板文件不存在，使用内置模板
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		return template.New("report").Parse(defaultHTMLTemplate)
	}
	
	return template.ParseFiles(templatePath)
}

// GenerateSummaryReport 生成摘要报告
func (rg *ReportGenerator) GenerateSummaryReport(report *TestReport) string {
	summary := fmt.Sprintf(`
FlexProxy 集成测试报告摘要
========================================

测试套件: %s
生成时间: %s
测试持续时间: %v

测试统计:
- 总测试数: %d
- 通过测试: %d
- 失败测试: %d
- 跳过测试: %d
- 成功率: %.2f%%

性能统计:
- 总请求数: %d
- 成功请求: %d
- 失败请求: %d
- 平均响应时间: %v
- 吞吐量: %.2f RPS
- 错误率: %.2f%%
- 内存使用: %.2f MB
- CPU使用: %.2f%%

模块测试结果:
`, report.TestSuite, report.GeneratedAt.Format("2006-01-02 15:04:05"),
		report.Duration, report.TotalTests, report.PassedTests, report.FailedTests,
		report.SkippedTests, report.SuccessRate, report.PerformanceData.TotalRequests,
		report.PerformanceData.SuccessfulRequests, report.PerformanceData.FailedRequests,
		report.PerformanceData.AvgResponseTime, report.PerformanceData.ThroughputRPS,
		report.PerformanceData.ErrorRate, report.PerformanceData.MemoryUsageMB,
		report.PerformanceData.CPUUsagePercent)
	
	// 按成功率排序模块结果
	sortedModules := make([]ModuleResult, len(report.ModuleResults))
	copy(sortedModules, report.ModuleResults)
	sort.Slice(sortedModules, func(i, j int) bool {
		return sortedModules[i].SuccessRate > sortedModules[j].SuccessRate
	})
	
	for _, module := range sortedModules {
		summary += fmt.Sprintf("- %s: %d/%d 通过 (%.2f%%)\n",
			module.Name, module.PassedTests, module.TotalTests, module.SuccessRate)
	}
	
	return summary
}

// defaultHTMLTemplate 默认 HTML 模板
const defaultHTMLTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlexProxy 集成测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .header .meta { color: #666; font-size: 14px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .stat-card h3 { margin: 0 0 10px 0; color: #495057; font-size: 14px; }
        .stat-card .value { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-card.success .value { color: #28a745; }
        .stat-card.danger .value { color: #dc3545; }
        .stat-card.warning .value { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.passed { background-color: #d4edda; color: #155724; }
        .status.failed { background-color: #f8d7da; color: #721c24; }
        .status.skipped { background-color: #fff3cd; color: #856404; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
        .chart { height: 300px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FlexProxy 集成测试报告</h1>
            <div class="meta">
                <p>测试套件: {{.TestSuite}} | 生成时间: {{.GeneratedAt.Format "2006-01-02 15:04:05"}} | 持续时间: {{.Duration}}</p>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <h3>总测试数</h3>
                <div class="value">{{.TotalTests}}</div>
            </div>
            <div class="stat-card success">
                <h3>通过测试</h3>
                <div class="value">{{.PassedTests}}</div>
            </div>
            <div class="stat-card danger">
                <h3>失败测试</h3>
                <div class="value">{{.FailedTests}}</div>
            </div>
            <div class="stat-card warning">
                <h3>跳过测试</h3>
                <div class="value">{{.SkippedTests}}</div>
            </div>
            <div class="stat-card">
                <h3>成功率</h3>
                <div class="value">{{printf "%.2f%%" .SuccessRate}}</div>
            </div>
            <div class="stat-card">
                <h3>平均响应时间</h3>
                <div class="value">{{.PerformanceData.AvgResponseTime}}</div>
            </div>
        </div>

        <div class="section">
            <h2>性能统计</h2>
            <div class="stats">
                <div class="stat-card">
                    <h3>总请求数</h3>
                    <div class="value">{{.PerformanceData.TotalRequests}}</div>
                </div>
                <div class="stat-card">
                    <h3>吞吐量</h3>
                    <div class="value">{{printf "%.2f RPS" .PerformanceData.ThroughputRPS}}</div>
                </div>
                <div class="stat-card">
                    <h3>错误率</h3>
                    <div class="value">{{printf "%.2f%%" .PerformanceData.ErrorRate}}</div>
                </div>
                <div class="stat-card">
                    <h3>内存使用</h3>
                    <div class="value">{{printf "%.2f MB" .PerformanceData.MemoryUsageMB}}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>模块测试结果</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>模块名称</th>
                        <th>测试数量</th>
                        <th>通过/失败</th>
                        <th>成功率</th>
                        <th>持续时间</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    {{range .ModuleResults}}
                    <tr>
                        <td>{{.Name}}</td>
                        <td>{{.TotalTests}}</td>
                        <td>{{.PassedTests}}/{{.FailedTests}}</td>
                        <td>{{printf "%.2f%%" .SuccessRate}}</td>
                        <td>{{.Duration}}</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {{.SuccessRate}}%"></div>
                            </div>
                        </td>
                    </tr>
                    {{end}}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>详细测试结果</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>模块</th>
                        <th>状态</th>
                        <th>持续时间</th>
                        <th>开始时间</th>
                        <th>错误信息</th>
                    </tr>
                </thead>
                <tbody>
                    {{range .TestResults}}
                    <tr>
                        <td>{{.Name}}</td>
                        <td>{{.Module}}</td>
                        <td><span class="status {{.Status | lower}}">{{.Status}}</span></td>
                        <td>{{.Duration}}</td>
                        <td>{{.StartTime.Format "15:04:05"}}</td>
                        <td>{{.ErrorMsg}}</td>
                    </tr>
                    {{end}}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>系统信息</h2>
            <table class="table">
                {{range $key, $value := .SystemInfo}}
                <tr>
                    <td><strong>{{$key}}</strong></td>
                    <td>{{$value}}</td>
                </tr>
                {{end}}
            </table>
        </div>
    </div>

    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为状态添加点击事件
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(function(element) {
                element.addEventListener('click', function() {
                    alert('测试状态: ' + this.textContent);
                });
            });
        });
    </script>
</body>
</html>
`
