// Package main 独立集成测试验证
package main

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// MockServer 模拟服务器
type MockServer struct {
	server *httptest.Server
	port   string
}

// NewMockServer 创建模拟服务器
func NewMockServer() *MockServer {
	mux := http.NewServeMux()
	
	// 健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"healthy","service":"mock-server"}`)
	})
	
	// 测试端点
	mux.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprint(w, `{"message":"Test response","timestamp":"2025-01-11T22:50:00Z"}`)
	})
	
	// 代理端点
	mux.HandleFunc("/proxy", func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		fmt.Fprint(w, `{"proxy":"active","requests_processed":100}`)
	})
	
	server := httptest.NewServer(mux)
	
	return &MockServer{
		server: server,
		port:   strings.Split(server.URL, ":")[2],
	}
}

// Close 关闭模拟服务器
func (ms *MockServer) Close() {
	ms.server.Close()
}

// URL 获取服务器URL
func (ms *MockServer) URL() string {
	return ms.server.URL
}

// TestStandaloneIntegration 独立集成测试
func TestStandaloneIntegration(t *testing.T) {
	fmt.Println("🚀 FlexProxy 独立集成测试开始")
	
	// 启动模拟服务器
	mockServer := NewMockServer()
	defer mockServer.Close()
	
	fmt.Printf("📡 模拟服务器启动: %s\n", mockServer.URL())
	
	// 测试服务器基本功能
	t.Run("ServerBasicFunctionality", func(t *testing.T) {
		testServerBasicFunctionality(t, mockServer)
	})
	
	// 测试代理功能
	t.Run("ProxyFunctionality", func(t *testing.T) {
		testProxyFunctionality(t, mockServer)
	})
	
	// 测试缓存功能
	t.Run("CacheFunctionality", func(t *testing.T) {
		testCacheFunctionality(t, mockServer)
	})
	
	// 测试并发处理
	t.Run("ConcurrentProcessing", func(t *testing.T) {
		testConcurrentProcessing(t, mockServer)
	})
	
	// 测试错误处理
	t.Run("ErrorHandling", func(t *testing.T) {
		testErrorHandling(t, mockServer)
	})
	
	fmt.Println("🎉 FlexProxy 独立集成测试完成")
}

// testServerBasicFunctionality 测试服务器基本功能
func testServerBasicFunctionality(t *testing.T, mockServer *MockServer) {
	fmt.Println("📦 测试服务器基本功能...")
	
	// 测试健康检查
	resp, err := http.Get(mockServer.URL() + "/health")
	if err != nil {
		t.Fatalf("健康检查请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("健康检查状态码错误: 期望 %d, 实际 %d", http.StatusOK, resp.StatusCode)
	}
	
	fmt.Printf("✅ 健康检查通过: 状态码 %d\n", resp.StatusCode)
	
	// 测试基本请求
	resp, err = http.Get(mockServer.URL() + "/test")
	if err != nil {
		t.Fatalf("测试请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("测试请求状态码错误: 期望 %d, 实际 %d", http.StatusOK, resp.StatusCode)
	}
	
	fmt.Printf("✅ 基本请求通过: 状态码 %d\n", resp.StatusCode)
}

// testProxyFunctionality 测试代理功能
func testProxyFunctionality(t *testing.T, mockServer *MockServer) {
	fmt.Println("🔄 测试代理功能...")
	
	// 模拟代理请求
	client := &http.Client{Timeout: 5 * time.Second}
	
	resp, err := client.Get(mockServer.URL() + "/proxy")
	if err != nil {
		t.Fatalf("代理请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		t.Errorf("代理请求状态码错误: 期望 %d, 实际 %d", http.StatusOK, resp.StatusCode)
	}
	
	fmt.Printf("✅ 代理功能通过: 状态码 %d\n", resp.StatusCode)
	
	// 测试代理轮换 (模拟)
	for i := 0; i < 3; i++ {
		resp, err := client.Get(mockServer.URL() + "/proxy")
		if err != nil {
			t.Errorf("代理轮换请求 %d 失败: %v", i+1, err)
			continue
		}
		resp.Body.Close()
		
		fmt.Printf("✅ 代理轮换 %d: 状态码 %d\n", i+1, resp.StatusCode)
	}
}

// testCacheFunctionality 测试缓存功能
func testCacheFunctionality(t *testing.T, mockServer *MockServer) {
	fmt.Println("💾 测试缓存功能...")
	
	// 模拟缓存测试
	cache := make(map[string]string)
	
	// 第一次请求 (缓存未命中)
	start1 := time.Now()
	key := "test-key"
	if _, exists := cache[key]; !exists {
		// 模拟从服务器获取数据
		resp, err := http.Get(mockServer.URL() + "/test")
		if err != nil {
			t.Fatalf("缓存测试请求失败: %v", err)
		}
		resp.Body.Close()
		
		cache[key] = "cached-value"
	}
	duration1 := time.Since(start1)
	
	// 第二次请求 (缓存命中)
	start2 := time.Now()
	if _, exists := cache[key]; exists {
		// 从缓存获取数据
		_ = cache[key]
	}
	duration2 := time.Since(start2)
	
	fmt.Printf("✅ 缓存未命中耗时: %v\n", duration1)
	fmt.Printf("✅ 缓存命中耗时: %v\n", duration2)
	
	if duration2 >= duration1 {
		t.Logf("⚠️  缓存性能提升不明显 (可能是测试环境影响)")
	} else {
		fmt.Printf("✅ 缓存性能提升: %.2fx\n", float64(duration1)/float64(duration2))
	}
}

// testConcurrentProcessing 测试并发处理
func testConcurrentProcessing(t *testing.T, mockServer *MockServer) {
	fmt.Println("⚡ 测试并发处理...")
	
	concurrency := 10
	requests := 5
	
	results := make(chan bool, concurrency*requests)
	
	start := time.Now()
	
	// 启动并发请求
	for i := 0; i < concurrency; i++ {
		go func(workerID int) {
			client := &http.Client{Timeout: 5 * time.Second}
			
			for j := 0; j < requests; j++ {
				resp, err := client.Get(mockServer.URL() + "/test")
				if err != nil {
					results <- false
					continue
				}
				resp.Body.Close()
				
				results <- (resp.StatusCode == http.StatusOK)
			}
		}(i)
	}
	
	// 收集结果
	successCount := 0
	totalCount := 0
	
	for i := 0; i < concurrency*requests; i++ {
		success := <-results
		totalCount++
		if success {
			successCount++
		}
	}
	
	duration := time.Since(start)
	successRate := float64(successCount) / float64(totalCount) * 100
	
	fmt.Printf("✅ 并发测试完成: %d/%d 成功 (%.2f%%)\n", successCount, totalCount, successRate)
	fmt.Printf("✅ 总耗时: %v\n", duration)
	fmt.Printf("✅ 平均RPS: %.2f\n", float64(totalCount)/duration.Seconds())
	
	if successRate < 90.0 {
		t.Errorf("并发测试成功率过低: %.2f%%", successRate)
	}
}

// testErrorHandling 测试错误处理
func testErrorHandling(t *testing.T, mockServer *MockServer) {
	fmt.Println("🚨 测试错误处理...")
	
	// 测试不存在的端点
	resp, err := http.Get(mockServer.URL() + "/nonexistent")
	if err != nil {
		t.Fatalf("错误处理测试请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusNotFound {
		t.Errorf("错误处理状态码错误: 期望 %d, 实际 %d", http.StatusNotFound, resp.StatusCode)
	}
	
	fmt.Printf("✅ 404错误处理通过: 状态码 %d\n", resp.StatusCode)
	
	// 测试超时处理
	client := &http.Client{Timeout: 1 * time.Millisecond} // 极短超时
	
	_, err = client.Get(mockServer.URL() + "/test")
	if err == nil {
		t.Log("⚠️  超时测试未触发 (服务器响应太快)")
	} else {
		fmt.Printf("✅ 超时处理通过: %v\n", err)
	}
}

// TestFileSystemOperations 测试文件系统操作
func TestFileSystemOperations(t *testing.T) {
	fmt.Println("📁 测试文件系统操作...")
	
	// 创建临时目录
	tempDir := filepath.Join(os.TempDir(), "flexproxy_test")
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	fmt.Printf("✅ 临时目录创建: %s\n", tempDir)
	
	// 测试配置文件操作
	configFile := filepath.Join(tempDir, "test_config.yaml")
	configContent := `
global:
  enable: true
  dns_lookup_mode: "system"

server:
  host: "127.0.0.1"
  port: 18080

logging:
  enabled: true
  level: "info"
`
	
	err = os.WriteFile(configFile, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("写入配置文件失败: %v", err)
	}
	
	fmt.Printf("✅ 配置文件创建: %s\n", configFile)
	
	// 读取配置文件
	content, err := os.ReadFile(configFile)
	if err != nil {
		t.Fatalf("读取配置文件失败: %v", err)
	}
	
	if len(content) == 0 {
		t.Error("配置文件内容为空")
	}
	
	fmt.Printf("✅ 配置文件读取: %d 字节\n", len(content))
	
	// 测试日志文件操作
	logFile := filepath.Join(tempDir, "test.log")
	logContent := fmt.Sprintf("[%s] INFO: FlexProxy 集成测试日志\n", time.Now().Format("2006-01-02 15:04:05"))
	
	err = os.WriteFile(logFile, []byte(logContent), 0644)
	if err != nil {
		t.Fatalf("写入日志文件失败: %v", err)
	}
	
	fmt.Printf("✅ 日志文件创建: %s\n", logFile)
}

// BenchmarkHTTPRequests HTTP请求基准测试
func BenchmarkHTTPRequests(b *testing.B) {
	mockServer := NewMockServer()
	defer mockServer.Close()
	
	client := &http.Client{Timeout: 5 * time.Second}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		resp, err := client.Get(mockServer.URL() + "/test")
		if err != nil {
			b.Fatalf("基准测试请求失败: %v", err)
		}
		resp.Body.Close()
	}
}

// BenchmarkCacheOperations 缓存操作基准测试
func BenchmarkCacheOperations(b *testing.B) {
	cache := make(map[string]string)
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("key-%d", i%100) // 100个不同的键
		cache[key] = fmt.Sprintf("value-%d", i)
		_ = cache[key]
	}
}

// main 函数用于直接运行测试
func main() {
	fmt.Println("🚀 FlexProxy 独立集成测试启动")
	fmt.Println("=====================================")
	
	// 检查测试环境
	fmt.Println("🔍 检查测试环境...")
	
	// 检查当前目录
	pwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("❌ 获取当前目录失败: %v\n", err)
		return
	}
	fmt.Printf("📁 当前目录: %s\n", pwd)
	
	// 检查关键目录
	directories := []string{"framework", "modules", "scenarios", "reports"}
	for _, dir := range directories {
		if _, err := os.Stat(dir); err == nil {
			fmt.Printf("✅ 目录存在: %s\n", dir)
		} else {
			fmt.Printf("⚠️  目录不存在: %s\n", dir)
		}
	}
	
	fmt.Println("\n🧪 开始独立测试验证...")
	
	// 启动模拟服务器进行演示
	mockServer := NewMockServer()
	defer mockServer.Close()
	
	fmt.Printf("📡 模拟服务器启动: %s\n", mockServer.URL())
	
	// 演示基本功能
	fmt.Println("\n📦 演示基本功能...")
	resp, err := http.Get(mockServer.URL() + "/health")
	if err != nil {
		fmt.Printf("❌ 健康检查失败: %v\n", err)
	} else {
		fmt.Printf("✅ 健康检查成功: 状态码 %d\n", resp.StatusCode)
		resp.Body.Close()
	}
	
	// 演示并发处理
	fmt.Println("\n⚡ 演示并发处理...")
	start := time.Now()
	
	for i := 0; i < 10; i++ {
		go func(id int) {
			resp, err := http.Get(mockServer.URL() + "/test")
			if err != nil {
				fmt.Printf("❌ 并发请求 %d 失败: %v\n", id, err)
			} else {
				fmt.Printf("✅ 并发请求 %d 成功: 状态码 %d\n", id, resp.StatusCode)
				resp.Body.Close()
			}
		}(i)
	}
	
	time.Sleep(1 * time.Second) // 等待并发请求完成
	duration := time.Since(start)
	
	fmt.Printf("⏱️  并发演示完成: 耗时 %v\n", duration)
	
	fmt.Println("\n🎉 独立集成测试演示完成!")
	fmt.Println("📋 要运行完整测试，请使用: go test -v standalone_test.go")
}
