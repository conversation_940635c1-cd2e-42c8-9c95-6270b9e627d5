# Actions & Events 测试配置文件
# 用于测试所有动作和事件功能

# 全局配置
global:
  enable: true
  proxy_file: "./test_proxies.txt"
  ip_rotation_mode: "smart"
  default_process_stage: "pre"
  retry_proxy_reuse_policy: "cooldown"
  retry_proxy_cooldown_time: 60
  retry_proxy_global_tracking: true

# 统一模块管理
modules:
  enabled:
    - "server"
    - "proxy"
    - "cache"
    - "logging"
    - "monitoring"
    - "security"
    - "dns_service"
  
  disabled:
    - "rate_limiting"
    - "plugins"

# 统一超时配置
timeouts:
  network:
    connect: "10s"
    read: "30s"
    write: "30s"
    idle: "120s"
  dns:
    query: "5s"
    resolve: "5s"
  proxy:
    retry: "1s"
    max_retry: "30s"
    health_check: "10s"
    cooldown: "60s"
  monitoring:
    interval: "5s"
    collection: "3s"
  cache:
    cleanup: "300s"
  action:
    default: "10s"
    request: "30s"
    bypass: "30s"

# 统一端口配置
ports:
  http: 8080
  https: 8443
  socks: 1080
  monitoring: 9090
  debug: 6060
  admin: 8081

# 服务器配置
server:
  host: "0.0.0.0"
  max_idle_conns: 100
  max_idle_conns_per_host: 10
  max_conns_per_host: 50

# 代理配置
proxy:
  strategy: "random"
  load_balancer: "round_robin"
  max_retries: 3
  pool_size: 50

# 日志配置
logging:
  level: "debug"
  format: "json"
  file: "./logs/actions_events_test.log"
  max_size: 100
  max_age: 30
  max_backups: 10

# 监控配置
monitoring:
  path: "/metrics"
  metrics:
    requests_total: "counter"
    request_duration: "histogram"
    active_connections: "gauge"

# 安全配置
security:
  auth:
    type: "basic"
    username: "test"
    password: "test123"

# DNS服务配置
dns_service:
  enabled: true
  lookup_mode: "custom"
  timeout: "5s"
  retries: 3
  servers:
    primary:
      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 1

# 缓存配置
cache:
  global:
    enabled: true
    default_ttl: "3600s"
    default_size: 1000
    cleanup_interval: "300s"
  
  modules:
    dns:
      enabled: true
      ttl: "300s"
      size: 500
    proxy:
      enabled: true
      ttl: "1800s"
      size: 200

# Actions & Events 配置
events:
  # 状态码触发器
  - name: "status_error_trigger"
    enable: true
    trigger_type: "status"
    process_stage: "post_body"
    conditions:
      - type: "status_code"
        operator: "in"
        value: [500, 502, 503, 504]
    actions:
      - type: "log"
        params:
          level: "error"
          message: "服务器错误状态码检测"
      - type: "retry"
        params:
          max_retries: 3
          delay: "1s"
          reason: "服务器错误重试"

  # 响应体触发器
  - name: "error_body_trigger"
    enable: true
    trigger_type: "body"
    process_stage: "post_body"
    conditions:
      - type: "body_contains"
        operator: "contains"
        value: "error"
    actions:
      - type: "log"
        params:
          level: "warn"
          message: "响应体包含错误信息"
      - type: "modify_response"
        params:
          headers:
            add:
              X-Error-Detected: "true"

  # 请求时间触发器
  - name: "slow_request_trigger"
    enable: true
    trigger_type: "max_request_time"
    process_stage: "post_body"
    conditions:
      - type: "request_time"
        operator: "gt"
        value: 5000  # 5秒
    actions:
      - type: "log"
        params:
          level: "warn"
          message: "请求时间过长"
      - type: "cache_response"
        params:
          ttl: 7200
          key: "slow_response_cache"

  # URL触发器
  - name: "api_endpoint_trigger"
    enable: true
    trigger_type: "url"
    process_stage: "pre_request"
    conditions:
      - type: "url_path"
        operator: "contains"
        value: "/api/"
    actions:
      - type: "log"
        params:
          level: "info"
          message: "API端点访问"
      - type: "modify_request"
        params:
          headers:
            add:
              X-API-Request: "true"
              X-Request-ID: "{{uuid}}"

  # 域名触发器
  - name: "malicious_domain_trigger"
    enable: true
    trigger_type: "domain"
    process_stage: "pre_request"
    conditions:
      - type: "domain"
        operator: "in"
        value: ["malicious-site.com", "spam-domain.net", "blocked-site.org"]
    actions:
      - type: "ban_domain"
        params:
          domain: "{{request.host}}"
          duration: 86400
          reason: "恶意域名访问"
      - type: "block_request"
        params:
          status_code: 403
          reason: "访问被禁止的域名"

  # 请求头触发器
  - name: "suspicious_user_agent_trigger"
    enable: true
    trigger_type: "request_header"
    process_stage: "pre_request"
    conditions:
      - type: "header"
        field: "User-Agent"
        operator: "contains"
        value: "bot"
    actions:
      - type: "log"
        params:
          level: "warn"
          message: "检测到可疑User-Agent"
      - type: "banip"
        params:
          ip: "{{request.remote_addr}}"
          duration: 3600
          reason: "可疑User-Agent"

  # 响应头触发器
  - name: "json_response_trigger"
    enable: true
    trigger_type: "response_header"
    process_stage: "post_header"
    conditions:
      - type: "header"
        field: "Content-Type"
        operator: "contains"
        value: "application/json"
    actions:
      - type: "log"
        params:
          level: "info"
          message: "JSON响应检测"
      - type: "modify_response"
        params:
          headers:
            add:
              X-JSON-Response: "true"
              X-Content-Processed: "{{timestamp}}"

# 动作序列配置
action_sequences:
  # 安全防护序列
  security_protection:
    name: "安全防护动作序列"
    description: "检测并处理安全威胁"
    actions:
      - type: "log"
        params:
          level: "warn"
          message: "安全威胁检测"
      - type: "banip"
        params:
          ip: "{{request.remote_addr}}"
          duration: 7200
          reason: "安全威胁"
      - type: "block_request"
        params:
          status_code: 403
          reason: "安全防护阻止"

  # 性能优化序列
  performance_optimization:
    name: "性能优化动作序列"
    description: "优化请求性能"
    actions:
      - type: "log"
        params:
          level: "info"
          message: "性能优化触发"
      - type: "retry"
        params:
          max_retries: 2
          delay: "500ms"
          reason: "性能优化重试"
      - type: "cache_response"
        params:
          ttl: 3600
          key: "performance_cache_{{url_hash}}"

  # 内容修改序列
  content_modification:
    name: "内容修改动作序列"
    description: "修改请求和响应内容"
    actions:
      - type: "modify_request"
        params:
          headers:
            add:
              X-Modified-Request: "true"
              X-Modification-Time: "{{timestamp}}"
      - type: "modify_response"
        params:
          headers:
            add:
              X-Modified-Response: "true"
              X-Processing-Time: "{{processing_time}}"

# 测试场景配置
test_scenarios:
  # 基础功能测试
  basic_functionality:
    description: "测试基础动作和事件功能"
    requests:
      - method: "GET"
        url: "http://example.com/api/users"
        headers:
          User-Agent: "FlexProxy-Test/1.0"
        expected_triggers: ["api_endpoint_trigger"]
        expected_actions: ["log", "modify_request"]

  # 错误处理测试
  error_handling:
    description: "测试错误处理和恢复"
    requests:
      - method: "GET"
        url: "http://error-server.com/500"
        expected_triggers: ["status_error_trigger"]
        expected_actions: ["log", "retry"]

  # 安全防护测试
  security_protection:
    description: "测试安全防护功能"
    requests:
      - method: "GET"
        url: "http://malicious-site.com/attack"
        headers:
          User-Agent: "AttackBot/1.0"
        expected_triggers: ["malicious_domain_trigger", "suspicious_user_agent_trigger"]
        expected_actions: ["ban_domain", "banip", "block_request"]

  # 性能监控测试
  performance_monitoring:
    description: "测试性能监控和优化"
    requests:
      - method: "GET"
        url: "http://slow-server.com/slow-endpoint"
        expected_triggers: ["slow_request_trigger"]
        expected_actions: ["log", "cache_response"]
