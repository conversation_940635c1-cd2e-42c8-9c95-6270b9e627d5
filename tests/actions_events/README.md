# FlexProxy Actions & Events 测试套件

## 📋 概述

这个测试套件专门用于验证 FlexProxy 项目中所有 Actions 和 Events 的功能和特性。

## 🎯 测试覆盖范围

### 📦 Actions 测试 (16种动作类型)

#### 基础动作类型 (8种)
1. **log** - 日志记录动作
2. **banip** - IP封禁动作
3. **ban_domain** - 域名封禁动作
4. **block_request** - 请求阻止动作
5. **modify_request** - 请求修改动作
6. **modify_response** - 响应修改动作
7. **cache_response** - 响应缓存动作
8. **script** - 脚本执行动作

#### 扩展动作类型 (8种)
9. **retry** - 新代理重试动作
10. **retry_same** - 相同代理重试动作
11. **save_to_pool** - 保存到代理池动作
12. **cache** - 缓存动作
13. **request_url** - URL请求动作
14. **banipdomain** - IP域名封禁动作
15. **null_response** - 空响应动作
16. **bypass_proxy** - 绕过代理动作

### 🎬 Events 测试 (11种触发器类型)

1. **status** - HTTP状态码触发器
2. **body** - 响应内容触发器
3. **max_request_time** - 最大请求时间触发器
4. **conn_time_out** - 连接超时触发器
5. **min_request_time** - 最小请求时间触发器
6. **url** - URL匹配触发器
7. **domain** - 域名匹配触发器
8. **combined** - 组合条件触发器
9. **custom** - 自定义触发器
10. **request_body** - 请求内容触发器
11. **request_header** - 请求头触发器
12. **response_header** - 响应头触发器

## 📁 文件结构

```
tests/actions_events/
├── README.md                    # 本文档
├── test_framework.go           # 测试框架和工具
├── mock_services.go            # 模拟服务
├── test_data/                  # 测试数据
│   ├── configs/               # 测试配置文件
│   ├── payloads/              # 测试负载数据
│   └── expected/              # 期望结果
├── actions/                    # Actions测试
│   ├── basic_actions_test.go   # 基础动作测试
│   ├── extended_actions_test.go # 扩展动作测试
│   ├── modify_actions_test.go  # 修改动作专项测试
│   └── script_actions_test.go  # 脚本动作专项测试
├── events/                     # Events测试
│   ├── trigger_tests.go        # 触发器测试
│   ├── condition_tests.go      # 条件测试
│   └── integration_tests.go    # 集成测试
├── scenarios/                  # 场景测试
│   ├── real_world_test.go      # 真实世界场景
│   ├── performance_test.go     # 性能测试
│   └── stress_test.go          # 压力测试
└── run_tests.sh               # 测试运行脚本
```

## 🚀 快速开始

### 运行所有测试
```bash
cd tests/actions_events
./run_tests.sh
```

### 运行特定类型的测试
```bash
# 仅运行Actions测试
./run_tests.sh -a

# 仅运行Events测试
./run_tests.sh -e

# 仅运行场景测试
./run_tests.sh -s

# 运行性能测试
./run_tests.sh -p

# 运行压力测试
./run_tests.sh -stress
```

### 手动运行测试
```bash
# 运行基础动作测试
go test -v ./actions -run "TestBasicActions"

# 运行扩展动作测试
go test -v ./actions -run "TestExtendedActions"

# 运行触发器测试
go test -v ./events -run "TestTriggers"

# 运行真实场景测试
go test -v ./scenarios -run "TestRealWorld"
```

## 🧪 测试特性

### 1. 真实环境模拟
- **Mock HTTP服务器**: 模拟各种HTTP响应
- **Mock代理服务器**: 模拟代理行为
- **Mock DNS服务器**: 模拟DNS解析
- **网络延迟模拟**: 模拟网络延迟和超时

### 2. 全面的测试覆盖
- **功能测试**: 验证每个动作和事件的基本功能
- **边界测试**: 测试极端情况和边界条件
- **错误处理测试**: 验证错误处理机制
- **性能测试**: 测试性能和资源使用

### 3. 真实数据测试
- **真实HTTP请求**: 使用真实的HTTP请求和响应
- **真实配置**: 使用真实的配置文件
- **真实负载**: 模拟真实的网络负载

### 4. 自动化验证
- **结果验证**: 自动验证测试结果
- **日志分析**: 自动分析日志输出
- **性能指标**: 自动收集性能指标
- **报告生成**: 自动生成测试报告

## 📊 测试指标

### 覆盖率目标
- **Actions覆盖率**: 100% (16/16种动作类型)
- **Events覆盖率**: 100% (12/12种触发器类型)
- **代码覆盖率**: ≥90%
- **分支覆盖率**: ≥85%

### 性能目标
- **动作执行时间**: <100ms (平均)
- **触发器检测时间**: <50ms (平均)
- **内存使用**: <100MB (峰值)
- **并发处理**: ≥1000 req/s

## 🔧 测试工具

### 内置工具
- **TestFramework**: 统一的测试框架
- **MockServices**: 模拟服务集合
- **DataGenerator**: 测试数据生成器
- **ResultValidator**: 结果验证器
- **PerformanceProfiler**: 性能分析器

### 外部依赖
- **testify**: 断言和测试套件
- **httptest**: HTTP测试工具
- **gomock**: Mock生成工具
- **pprof**: 性能分析工具

## 📈 测试报告

### 报告类型
- **HTML报告**: 可视化测试结果
- **JSON报告**: 机器可读的测试数据
- **覆盖率报告**: 代码覆盖率分析
- **性能报告**: 性能指标分析

### 报告内容
- **测试执行摘要**: 通过/失败统计
- **详细测试结果**: 每个测试的详细信息
- **性能指标**: 执行时间、内存使用等
- **错误分析**: 失败原因和建议

## 🎯 测试策略

### 1. 分层测试
- **单元测试**: 测试单个动作/事件
- **集成测试**: 测试动作/事件组合
- **系统测试**: 测试完整流程
- **端到端测试**: 测试真实场景

### 2. 数据驱动测试
- **参数化测试**: 使用多组测试数据
- **配置驱动**: 通过配置文件控制测试
- **随机测试**: 使用随机数据测试
- **边界测试**: 测试边界值和极端情况

### 3. 持续测试
- **自动化执行**: CI/CD集成
- **定期回归**: 定期运行完整测试
- **性能监控**: 持续监控性能指标
- **质量门禁**: 质量标准检查

## 🚨 注意事项

### 测试环境要求
- **Go版本**: ≥1.19
- **内存**: ≥2GB可用内存
- **网络**: 需要网络连接进行真实测试
- **端口**: 需要8080-8090端口可用

### 测试数据安全
- **敏感数据**: 不包含真实敏感数据
- **隔离环境**: 测试在隔离环境中运行
- **数据清理**: 测试后自动清理数据
- **权限控制**: 最小权限原则

## 📞 支持

如果您在运行测试时遇到问题，请：

1. 查看测试日志文件
2. 检查测试环境配置
3. 运行诊断脚本
4. 提交Issue到项目仓库

---

**🎉 让我们开始全面测试FlexProxy的Actions和Events功能！**
