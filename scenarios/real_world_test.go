// Package scenarios 真实环境集成测试
package scenarios

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRealWorldProxyScenarios 测试真实世界的代理场景
func TestRealWorldProxyScenarios(t *testing.T) {
	t.Run("测试公共代理服务器", func(t *testing.T) {
		testPublicProxyServers(t)
	})
	
	t.Run("测试HTTPS网站访问", func(t *testing.T) {
		testHTTPSWebsiteAccess(t)
	})
	
	t.Run("测试DNS解析性能", func(t *testing.T) {
		testDNSResolutionPerformance(t)
	})
	
	t.Run("测试网络连接质量", func(t *testing.T) {
		testNetworkConnectionQuality(t)
	})
	
	t.Run("测试地理位置检测", func(t *testing.T) {
		testGeolocationDetection(t)
	})
}

// testPublicProxyServers 测试公共代理服务器连接
func testPublicProxyServers(t *testing.T) {
	// 使用一些公开的免费代理进行测试（注意：这些可能不稳定）
	publicProxies := []string{
		"http://proxy.example.com:8080",  // 示例代理
		"socks5://proxy2.example.com:1080", // 示例SOCKS5代理
	}
	
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	
	testURL := "http://httpbin.org/ip"
	
	// 首先测试直连
	t.Log("🔗 测试直连访问...")
	directResp, err := client.Get(testURL)
	if err != nil {
		t.Skipf("跳过代理测试，直连失败: %v", err)
		return
	}
	defer directResp.Body.Close()
	
	directBody, _ := io.ReadAll(directResp.Body)
	t.Logf("✅ 直连成功，响应: %s", string(directBody))
	
	// 测试代理连接（模拟）
	for i, proxyURL := range publicProxies {
		t.Logf("🔄 测试代理 %d: %s", i+1, proxyURL)
		
		// 解析代理URL
		parsedProxy, err := url.Parse(proxyURL)
		if err != nil {
			t.Logf("❌ 代理URL解析失败: %v", err)
			continue
		}
		
		// 创建带代理的客户端
		transport := &http.Transport{
			Proxy: http.ProxyURL(parsedProxy),
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				// 模拟代理连接（实际环境中这里会真正连接代理）
				t.Logf("📡 模拟通过代理连接: %s", addr)
				return net.Dial(network, addr)
			},
		}
		
		proxyClient := &http.Client{
			Timeout:   10 * time.Second,
			Transport: transport,
		}
		
		// 尝试通过代理访问
		resp, err := proxyClient.Get(testURL)
		if err != nil {
			t.Logf("❌ 代理连接失败: %v", err)
			continue
		}
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("✅ 代理连接成功，响应: %s", string(body))
	}
}

// testHTTPSWebsiteAccess 测试HTTPS网站访问
func testHTTPSWebsiteAccess(t *testing.T) {
	httpsWebsites := []string{
		"https://www.google.com",
		"https://www.github.com",
		"https://httpbin.org/get",
		"https://jsonplaceholder.typicode.com/posts/1",
	}
	
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				MinVersion: tls.VersionTLS12,
			},
		},
	}
	
	successCount := 0
	for _, website := range httpsWebsites {
		t.Logf("🔒 测试HTTPS访问: %s", website)
		
		start := time.Now()
		resp, err := client.Get(website)
		duration := time.Since(start)
		
		if err != nil {
			t.Logf("❌ HTTPS访问失败: %v", err)
			continue
		}
		defer resp.Body.Close()
		
		if resp.StatusCode == 200 {
			successCount++
			t.Logf("✅ HTTPS访问成功，耗时: %v, 状态码: %d", duration, resp.StatusCode)
		} else {
			t.Logf("⚠️ HTTPS访问异常，状态码: %d", resp.StatusCode)
		}
	}
	
	successRate := float64(successCount) / float64(len(httpsWebsites)) * 100
	assert.Greater(t, successRate, 50.0, "HTTPS网站访问成功率应该大于50%")
	t.Logf("📊 HTTPS访问统计: %d/%d 成功 (%.1f%%)", successCount, len(httpsWebsites), successRate)
}

// testDNSResolutionPerformance 测试DNS解析性能
func testDNSResolutionPerformance(t *testing.T) {
	domains := []string{
		"www.google.com",
		"www.github.com",
		"www.stackoverflow.com",
		"httpbin.org",
		"jsonplaceholder.typicode.com",
	}
	
	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: time.Second * 5,
			}
			return d.DialContext(ctx, network, address)
		},
	}
	
	totalDuration := time.Duration(0)
	successCount := 0
	
	for _, domain := range domains {
		t.Logf("🔍 DNS解析: %s", domain)
		
		start := time.Now()
		ips, err := resolver.LookupIPAddr(context.Background(), domain)
		duration := time.Since(start)
		totalDuration += duration
		
		if err != nil {
			t.Logf("❌ DNS解析失败: %v", err)
			continue
		}
		
		successCount++
		ipStrings := make([]string, len(ips))
		for i, ip := range ips {
			ipStrings[i] = ip.IP.String()
		}
		
		t.Logf("✅ DNS解析成功，耗时: %v, IP: %s", duration, strings.Join(ipStrings, ", "))
	}
	
	if successCount > 0 {
		avgDuration := totalDuration / time.Duration(successCount)
		assert.Less(t, avgDuration, 2*time.Second, "平均DNS解析时间应该小于2秒")
		t.Logf("📊 DNS解析统计: %d/%d 成功, 平均耗时: %v", successCount, len(domains), avgDuration)
	}
}

// testNetworkConnectionQuality 测试网络连接质量
func testNetworkConnectionQuality(t *testing.T) {
	testServers := []struct {
		name string
		host string
		port string
	}{
		{"Google DNS", "*******", "53"},
		{"Cloudflare DNS", "*******", "53"},
		{"GitHub", "github.com", "443"},
		{"HTTP测试", "httpbin.org", "80"},
	}
	
	for _, server := range testServers {
		t.Logf("🌐 测试连接质量: %s (%s:%s)", server.name, server.host, server.port)
		
		// 测试连接延迟
		start := time.Now()
		conn, err := net.DialTimeout("tcp", net.JoinHostPort(server.host, server.port), 10*time.Second)
		latency := time.Since(start)
		
		if err != nil {
			t.Logf("❌ 连接失败: %v", err)
			continue
		}
		conn.Close()
		
		t.Logf("✅ 连接成功，延迟: %v", latency)
		
		// 验证延迟合理性
		if latency < 5*time.Second {
			t.Logf("🚀 连接质量良好")
		} else {
			t.Logf("⚠️ 连接延迟较高")
		}
	}
}

// testGeolocationDetection 测试地理位置检测
func testGeolocationDetection(t *testing.T) {
	geoServices := []string{
		"http://httpbin.org/ip",
		"https://api.ipify.org?format=json",
		"https://ipinfo.io/json",
	}
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	for _, service := range geoServices {
		t.Logf("🌍 测试地理位置服务: %s", service)
		
		resp, err := client.Get(service)
		if err != nil {
			t.Logf("❌ 地理位置服务访问失败: %v", err)
			continue
		}
		defer resp.Body.Close()
		
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Logf("❌ 响应读取失败: %v", err)
			continue
		}
		
		t.Logf("✅ 地理位置信息: %s", string(body))
		
		// 验证响应包含IP信息
		bodyStr := string(body)
		if strings.Contains(bodyStr, ".") || strings.Contains(bodyStr, ":") {
			t.Logf("🎯 检测到IP地址信息")
		}
	}
}
