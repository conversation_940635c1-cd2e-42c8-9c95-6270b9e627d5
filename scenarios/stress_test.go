// Package scenarios 真实环境压力测试
package scenarios

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRealWorldStressScenarios 真实世界压力测试场景
func TestRealWorldStressScenarios(t *testing.T) {
	t.Run("高并发代理请求测试", func(t *testing.T) {
		testHighConcurrencyProxyRequests(t)
	})
	
	t.Run("长时间运行稳定性测试", func(t *testing.T) {
		testLongRunningStability(t)
	})
	
	t.Run("内存泄漏检测测试", func(t *testing.T) {
		testMemoryLeakDetection(t)
	})
	
	t.Run("代理池动态管理测试", func(t *testing.T) {
		testDynamicProxyPoolManagement(t)
	})
	
	t.Run("网络异常恢复测试", func(t *testing.T) {
		testNetworkFailureRecovery(t)
	})
}

// testHighConcurrencyProxyRequests 高并发代理请求测试
func testHighConcurrencyProxyRequests(t *testing.T) {
	const (
		numWorkers     = 100
		requestsPerWorker = 50
		totalRequests  = numWorkers * requestsPerWorker
	)
	
	t.Logf("🚀 开始高并发测试: %d个工作协程, 每个发送%d个请求", numWorkers, requestsPerWorker)
	
	var (
		successCount int64
		failureCount int64
		totalLatency int64
		wg          sync.WaitGroup
	)
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	start := time.Now()
	
	// 启动工作协程
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for j := 0; j < requestsPerWorker; j++ {
				requestStart := time.Now()
				
				// 模拟真实的HTTP请求
				testURL := "http://httpbin.org/delay/1"
				resp, err := client.Get(testURL)
				
				latency := time.Since(requestStart)
				atomic.AddInt64(&totalLatency, int64(latency))
				
				if err != nil {
					atomic.AddInt64(&failureCount, 1)
					if j%10 == 0 { // 只记录部分失败日志
						t.Logf("❌ Worker %d 请求失败: %v", workerID, err)
					}
					continue
				}
				
				if resp.StatusCode == 200 {
					atomic.AddInt64(&successCount, 1)
				} else {
					atomic.AddInt64(&failureCount, 1)
				}
				resp.Body.Close()
				
				// 随机延迟，模拟真实使用场景
				time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	// 计算统计数据
	successRate := float64(successCount) / float64(totalRequests) * 100
	avgLatency := time.Duration(totalLatency / totalRequests)
	rps := float64(totalRequests) / duration.Seconds()
	
	t.Logf("📊 高并发测试结果:")
	t.Logf("  总请求数: %d", totalRequests)
	t.Logf("  成功请求: %d (%.2f%%)", successCount, successRate)
	t.Logf("  失败请求: %d", failureCount)
	t.Logf("  平均延迟: %v", avgLatency)
	t.Logf("  请求速率: %.2f RPS", rps)
	t.Logf("  总耗时: %v", duration)
	
	// 验证性能指标
	assert.Greater(t, successRate, 70.0, "成功率应该大于70%")
	assert.Less(t, avgLatency, 5*time.Second, "平均延迟应该小于5秒")
	assert.Greater(t, rps, 10.0, "请求速率应该大于10 RPS")
}

// testLongRunningStability 长时间运行稳定性测试
func testLongRunningStability(t *testing.T) {
	const testDuration = 30 * time.Second // 在真实环境中可以设置更长时间
	
	t.Logf("⏰ 开始长时间稳定性测试，持续时间: %v", testDuration)
	
	var (
		requestCount int64
		errorCount   int64
		isRunning    int64 = 1
	)
	
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()
	
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	// 启动多个持续工作的协程
	numWorkers := 10
	var wg sync.WaitGroup
	
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for atomic.LoadInt64(&isRunning) == 1 {
				select {
				case <-ctx.Done():
					return
				default:
					// 执行请求
					atomic.AddInt64(&requestCount, 1)
					
					// 模拟不同类型的请求
					urls := []string{
						"http://httpbin.org/get",
						"http://httpbin.org/status/200",
						"http://httpbin.org/json",
					}
					
					url := urls[rand.Intn(len(urls))]
					resp, err := client.Get(url)
					
					if err != nil {
						atomic.AddInt64(&errorCount, 1)
					} else {
						resp.Body.Close()
					}
					
					// 随机休息时间
					time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
				}
			}
		}(i)
	}
	
	// 定期报告状态
	ticker := time.NewTicker(5 * time.Second)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				current := atomic.LoadInt64(&requestCount)
				errors := atomic.LoadInt64(&errorCount)
				errorRate := float64(errors) / float64(current) * 100
				t.Logf("📈 运行状态: 请求=%d, 错误=%d (%.2f%%)", current, errors, errorRate)
			}
		}
	}()
	
	wg.Wait()
	atomic.StoreInt64(&isRunning, 0)
	
	finalRequests := atomic.LoadInt64(&requestCount)
	finalErrors := atomic.LoadInt64(&errorCount)
	finalErrorRate := float64(finalErrors) / float64(finalRequests) * 100
	
	t.Logf("✅ 长时间稳定性测试完成")
	t.Logf("📊 最终统计: 总请求=%d, 错误=%d (%.2f%%)", finalRequests, finalErrors, finalErrorRate)
	
	// 验证稳定性
	assert.Greater(t, finalRequests, int64(100), "应该处理足够数量的请求")
	assert.Less(t, finalErrorRate, 20.0, "错误率应该小于20%")
}

// testMemoryLeakDetection 内存泄漏检测测试
func testMemoryLeakDetection(t *testing.T) {
	t.Logf("🔍 开始内存泄漏检测测试")
	
	// 记录初始内存状态
	var initialMem runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&initialMem)
	
	t.Logf("📊 初始内存状态: Alloc=%d KB, Sys=%d KB", 
		initialMem.Alloc/1024, initialMem.Sys/1024)
	
	// 执行大量操作
	const iterations = 1000
	for i := 0; i < iterations; i++ {
		// 模拟代理请求处理
		data := make([]byte, 1024*10) // 10KB 数据
		_ = data
		
		// 模拟配置更新
		config := map[string]interface{}{
			"proxy_list": []string{"127.0.0.1:8080", "127.0.0.1:8081"},
			"rotation":   "random",
			"timeout":    "30s",
		}
		_ = config
		
		if i%100 == 0 {
			runtime.GC() // 定期垃圾回收
			t.Logf("🔄 完成 %d/%d 次操作", i, iterations)
		}
	}
	
	// 强制垃圾回收并检查最终内存状态
	runtime.GC()
	runtime.GC() // 执行两次确保彻底清理
	
	var finalMem runtime.MemStats
	runtime.ReadMemStats(&finalMem)
	
	t.Logf("📊 最终内存状态: Alloc=%d KB, Sys=%d KB", 
		finalMem.Alloc/1024, finalMem.Sys/1024)
	
	// 计算内存增长
	memGrowth := int64(finalMem.Alloc) - int64(initialMem.Alloc)
	memGrowthMB := float64(memGrowth) / 1024 / 1024
	
	t.Logf("📈 内存增长: %+.2f MB", memGrowthMB)
	
	// 验证内存泄漏
	assert.Less(t, memGrowthMB, 50.0, "内存增长应该小于50MB")
	
	if memGrowthMB < 10.0 {
		t.Logf("✅ 内存使用良好，无明显泄漏")
	} else {
		t.Logf("⚠️ 内存增长较大，需要进一步检查")
	}
}

// testDynamicProxyPoolManagement 动态代理池管理测试
func testDynamicProxyPoolManagement(t *testing.T) {
	t.Logf("🔄 开始动态代理池管理测试")
	
	// 模拟代理池
	type ProxyInfo struct {
		Address    string
		IsHealthy  bool
		ResponseTime time.Duration
		ErrorCount int
	}
	
	proxyPool := []*ProxyInfo{
		{"127.0.0.1:8080", true, 50 * time.Millisecond, 0},
		{"127.0.0.1:8081", true, 100 * time.Millisecond, 0},
		{"127.0.0.1:8082", false, 500 * time.Millisecond, 5},
		{"127.0.0.1:8083", true, 75 * time.Millisecond, 1},
	}
	
	t.Logf("📋 初始代理池状态:")
	for i, proxy := range proxyPool {
		status := "健康"
		if !proxy.IsHealthy {
			status = "故障"
		}
		t.Logf("  代理 %d: %s - %s (响应时间: %v, 错误: %d)", 
			i+1, proxy.Address, status, proxy.ResponseTime, proxy.ErrorCount)
	}
	
	// 模拟动态健康检查
	t.Logf("🏥 执行健康检查...")
	for _, proxy := range proxyPool {
		// 模拟健康检查逻辑
		if proxy.ErrorCount > 3 {
			proxy.IsHealthy = false
		} else if proxy.ResponseTime < 200*time.Millisecond {
			proxy.IsHealthy = true
		}
	}
	
	// 统计健康代理
	healthyCount := 0
	for _, proxy := range proxyPool {
		if proxy.IsHealthy {
			healthyCount++
		}
	}
	
	t.Logf("📊 健康检查结果: %d/%d 代理健康", healthyCount, len(proxyPool))
	
	// 模拟负载均衡选择
	t.Logf("⚖️ 测试负载均衡选择...")
	selectionCount := make(map[string]int)
	
	for i := 0; i < 100; i++ {
		// 选择最佳代理（基于响应时间和健康状态）
		var bestProxy *ProxyInfo
		for _, proxy := range proxyPool {
			if !proxy.IsHealthy {
				continue
			}
			if bestProxy == nil || proxy.ResponseTime < bestProxy.ResponseTime {
				bestProxy = proxy
			}
		}
		
		if bestProxy != nil {
			selectionCount[bestProxy.Address]++
		}
	}
	
	t.Logf("📈 代理选择统计:")
	for address, count := range selectionCount {
		t.Logf("  %s: %d次 (%.1f%%)", address, count, float64(count))
	}
	
	// 验证代理池管理
	assert.Greater(t, healthyCount, 0, "应该至少有一个健康的代理")
	assert.Greater(t, len(selectionCount), 0, "应该能够选择到代理")
	
	t.Logf("✅ 动态代理池管理测试完成")
}

// testNetworkFailureRecovery 网络异常恢复测试
func testNetworkFailureRecovery(t *testing.T) {
	t.Logf("🌐 开始网络异常恢复测试")
	
	// 模拟网络状态
	networkStates := []string{"正常", "延迟", "丢包", "断开", "恢复"}
	
	for _, state := range networkStates {
		t.Logf("🔄 模拟网络状态: %s", state)
		
		switch state {
		case "正常":
			// 模拟正常网络请求
			success := simulateNetworkRequest(t, 100*time.Millisecond, 0.0)
			assert.True(t, success, "正常网络状态下请求应该成功")
			
		case "延迟":
			// 模拟高延迟网络
			success := simulateNetworkRequest(t, 2*time.Second, 0.1)
			t.Logf("  高延迟网络请求结果: %v", success)
			
		case "丢包":
			// 模拟丢包网络
			success := simulateNetworkRequest(t, 500*time.Millisecond, 0.3)
			t.Logf("  丢包网络请求结果: %v", success)
			
		case "断开":
			// 模拟网络断开
			success := simulateNetworkRequest(t, 0, 1.0)
			assert.False(t, success, "网络断开时请求应该失败")
			
		case "恢复":
			// 模拟网络恢复
			success := simulateNetworkRequest(t, 150*time.Millisecond, 0.05)
			t.Logf("  网络恢复后请求结果: %v", success)
		}
		
		time.Sleep(500 * time.Millisecond) // 状态切换间隔
	}
	
	t.Logf("✅ 网络异常恢复测试完成")
}

// simulateNetworkRequest 模拟网络请求
func simulateNetworkRequest(t *testing.T, latency time.Duration, failureRate float64) bool {
	// 模拟网络延迟
	if latency > 0 {
		time.Sleep(latency)
	}
	
	// 模拟失败率
	if rand.Float64() < failureRate {
		return false
	}
	
	return true
}
