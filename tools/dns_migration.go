package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// DNSMigrator DNS配置迁移器
type DNSMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewDNSMigrator 创建DNS配置迁移器
func NewDNSMigrator(inputFile, outputFile string) *DNSMigrator {
	backupFile := fmt.Sprintf("%s.dns_backup.%d", inputFile, time.Now().Unix())
	return &DNSMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行DNS配置迁移
func (m *DNSMigrator) Migrate() error {
	fmt.Printf("🔄 开始DNS配置迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migrateDNS(config); err != nil {
		return fmt.Errorf("迁移DNS配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ DNS配置迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *DNSMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migrateDNS 迁移DNS配置
func (m *DNSMigrator) migrateDNS(config map[string]interface{}) error {
	fmt.Println("📋 开始DNS配置迁移...")

	// 创建统一的DNS服务配置
	dnsService := map[string]interface{}{
		"enabled":             true,
		"lookup_mode":         "custom",
		"timeout":             "5s",
		"retries":             3,
		"ip_version_priority": "ipv4",
		"cache": map[string]interface{}{
			"enabled":          true,
			"ttl":              "300s",
			"cleanup_interval": "600s",
			"max_size":         1000,
		},
		"reverse_lookup": map[string]interface{}{
			"enabled": true,
			"mode":    "dns",
			"source":  "",
		},
		"servers": map[string]interface{}{
			"primary": []map[string]interface{}{
				{
					"server":   "*******:53",
					"protocol": "udp",
					"timeout":  5000,
					"priority": 1,
					"tags":     []string{"cloudflare", "primary"},
				},
				{
					"server":   "*******:53",
					"protocol": "udp",
					"timeout":  5000,
					"priority": 2,
					"tags":     []string{"google", "backup"},
				},
			},
			"secondary": []map[string]interface{}{
				{
					"server":   "*******:53",
					"protocol": "udp",
					"timeout":  5000,
					"priority": 1,
					"tags":     []string{"google", "secondary"},
				},
				{
					"server":   "*******:53",
					"protocol": "udp",
					"timeout":  5000,
					"priority": 2,
					"tags":     []string{"cloudflare", "secondary"},
				},
			},
			"fallback": "system",
		},
	}

	// 检查并迁移现有DNS配置
	if err := m.migrateDNSLookupMode(config, dnsService); err != nil {
		return err
	}

	if err := m.migrateDNSServers(config, dnsService); err != nil {
		return err
	}

	if err := m.migrateDNSCache(config, dnsService); err != nil {
		return err
	}

	if err := m.migrateDNSTimeout(config, dnsService); err != nil {
		return err
	}

	// 移除旧的DNS配置
	m.removeOldDNSConfig(config)

	// 设置新的DNS服务配置
	config["dns_service"] = dnsService

	fmt.Println("✅ DNS配置迁移完成")
	return nil
}

// migrateDNSLookupMode 迁移DNS查询模式
func (m *DNSMigrator) migrateDNSLookupMode(config, dnsService map[string]interface{}) error {
	if global, ok := config["global"].(map[string]interface{}); ok {
		if lookupMode, exists := global["dns_lookup_mode"]; exists {
			dnsService["lookup_mode"] = lookupMode
			fmt.Printf("  ✅ 迁移 global.dns_lookup_mode: %v -> dns_service.lookup_mode\n", lookupMode)
			delete(global, "dns_lookup_mode")
		}
	}
	return nil
}

// migrateDNSServers 迁移DNS服务器配置
func (m *DNSMigrator) migrateDNSServers(config, dnsService map[string]interface{}) error {
	// 检查是否有现有的DNS服务器配置
	if dns, ok := config["dns"].(map[string]interface{}); ok {
		if servers, exists := dns["servers"]; exists {
			if servers, ok := servers.(map[string]interface{}); ok {
				dnsService["servers"] = servers
				fmt.Printf("  ✅ 迁移 dns.servers -> dns_service.servers\n")
			}
		}
	}
	return nil
}

// migrateDNSCache 迁移DNS缓存配置
func (m *DNSMigrator) migrateDNSCache(config, dnsService map[string]interface{}) error {
	// 检查缓存配置中的DNS缓存
	if cache, ok := config["cache"].(map[string]interface{}); ok {
		if dnsCache, exists := cache["dns"]; exists {
			if dnsCacheConfig, ok := dnsCache.(map[string]interface{}); ok {
				// 迁移DNS缓存配置到dns_service.cache
				if cacheConfig, ok := dnsService["cache"].(map[string]interface{}); ok {
					for key, value := range dnsCacheConfig {
						cacheConfig[key] = value
					}
				}
				fmt.Printf("  ✅ 迁移 cache.dns -> dns_service.cache\n")
				delete(cache, "dns")
			}
		}
	}
	return nil
}

// migrateDNSTimeout 迁移DNS超时配置
func (m *DNSMigrator) migrateDNSTimeout(config, dnsService map[string]interface{}) error {
	// 检查DNS配置中的超时设置
	if dns, ok := config["dns"].(map[string]interface{}); ok {
		if timeout, exists := dns["timeout"]; exists {
			dnsService["timeout"] = timeout
			fmt.Printf("  ✅ 迁移 dns.timeout: %v -> dns_service.timeout\n", timeout)
		}
		if retries, exists := dns["retries"]; exists {
			dnsService["retries"] = retries
			fmt.Printf("  ✅ 迁移 dns.retries: %v -> dns_service.retries\n", retries)
		}
	}
	return nil
}

// removeOldDNSConfig 移除旧的DNS配置
func (m *DNSMigrator) removeOldDNSConfig(config map[string]interface{}) {
	// 移除旧的dns节
	if _, exists := config["dns"]; exists {
		delete(config, "dns")
		fmt.Printf("  ✅ 移除旧的 dns 配置节\n")
	}

	// 从global中移除DNS相关字段
	if global, ok := config["global"].(map[string]interface{}); ok {
		dnsFields := []string{
			"dns_lookup_mode",
			"dns_servers",
			"dns_timeout",
			"dns_retries",
			"dns_cache_enabled",
			"dns_cache_ttl",
		}
		
		for _, field := range dnsFields {
			if _, exists := global[field]; exists {
				delete(global, field)
				fmt.Printf("  ✅ 移除 global.%s\n", field)
			}
		}
	}
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run dns_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run dns_migration.go config.yaml config_dns_migrated.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewDNSMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("DNS配置迁移失败: %v", err)
	}

	fmt.Println("\n🎉 DNS配置迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - DNS配置已统一到 dns_service 节")
	fmt.Println("  - 移除了重复的DNS配置字段")
	fmt.Println("  - 添加了完整的DNS服务器配置")
	fmt.Println("  - 添加了DNS缓存和反向查询配置")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
