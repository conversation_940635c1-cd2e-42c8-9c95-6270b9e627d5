package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	configFile string
	errors     []string
	warnings   []string
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(configFile string) *ConfigValidator {
	return &ConfigValidator{
		configFile: configFile,
		errors:     []string{},
		warnings:   []string{},
	}
}

// Validate 验证配置文件
func (v *ConfigValidator) Validate() error {
	fmt.Printf("🔍 开始验证配置文件: %s\n", v.configFile)

	// 读取配置文件
	data, err := ioutil.ReadFile(v.configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 执行各项验证
	v.validateGlobalConfig(config)
	v.validateDNSServiceConfig(config)
	v.validateCacheConfig(config)
	v.validateServerConfig(config)
	v.validateProxyConfig(config)
	v.validateLoggingConfig(config)
	v.validateMonitoringConfig(config)
	v.validateSecurityConfig(config)

	// 检查配置冲突
	v.checkConfigConflicts(config)

	// 输出验证结果
	v.printResults()

	if len(v.errors) > 0 {
		return fmt.Errorf("配置验证失败，发现 %d 个错误", len(v.errors))
	}

	return nil
}

// validateGlobalConfig 验证全局配置
func (v *ConfigValidator) validateGlobalConfig(config map[string]interface{}) {
	global, ok := config["global"].(map[string]interface{})
	if !ok {
		v.addError("缺少global配置节")
		return
	}

	// 检查是否还有旧的DNS配置
	oldDNSFields := []string{
		"dns_lookup_mode", "reverse_dns_lookup", "custom_dns_servers",
		"http_proxy_dns", "dns_cache_ttl", "dns_no_cache",
		"ip_version_priority", "default_dns_timeout",
	}

	for _, field := range oldDNSFields {
		if _, exists := global[field]; exists {
			v.addWarning(fmt.Sprintf("global.%s 已废弃，请使用 dns_service 配置", field))
		}
	}

	// 验证必需字段
	if _, exists := global["enable"]; !exists {
		v.addError("global.enable 字段缺失")
	}

	if _, exists := global["proxy_file"]; !exists {
		v.addError("global.proxy_file 字段缺失")
	}
}

// validateDNSServiceConfig 验证DNS服务配置
func (v *ConfigValidator) validateDNSServiceConfig(config map[string]interface{}) {
	dnsService, ok := config["dns_service"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少dns_service配置节")
		return
	}

	// 验证lookup_mode
	if mode, ok := dnsService["lookup_mode"].(string); ok {
		validModes := []string{"system", "custom", "hybrid"}
		if !v.contains(validModes, mode) {
			v.addError(fmt.Sprintf("dns_service.lookup_mode 值无效: %s，有效值: %v", mode, validModes))
		}
	}

	// 验证timeout
	if timeout, ok := dnsService["timeout"].(string); ok {
		if !v.isValidDuration(timeout) {
			v.addError(fmt.Sprintf("dns_service.timeout 格式无效: %s", timeout))
		}
	}

	// 验证retries
	if retries, ok := dnsService["retries"].(int); ok {
		if retries < 0 {
			v.addError(fmt.Sprintf("dns_service.retries 不能为负数: %d", retries))
		}
	}

	// 验证缓存配置
	if cache, ok := dnsService["cache"].(map[string]interface{}); ok {
		v.validateDNSCacheConfig(cache)
	}

	// 验证服务器配置
	if servers, ok := dnsService["servers"].(map[string]interface{}); ok {
		v.validateDNSServersConfig(servers)
	}

	// 验证IP版本优先级
	if priority, ok := dnsService["ip_version_priority"].(string); ok {
		validPriorities := []string{"ipv4", "ipv6", "dual"}
		if !v.contains(validPriorities, priority) {
			v.addError(fmt.Sprintf("dns_service.ip_version_priority 值无效: %s，有效值: %v", priority, validPriorities))
		}
	}

	// 验证反向查询配置
	if reverseLookup, ok := dnsService["reverse_lookup"].(map[string]interface{}); ok {
		v.validateDNSReverseLookupConfig(reverseLookup)
	}
}

// validateDNSCacheConfig 验证DNS缓存配置
func (v *ConfigValidator) validateDNSCacheConfig(cache map[string]interface{}) {
	if ttl, ok := cache["ttl"].(string); ok {
		if !v.isValidDuration(ttl) {
			v.addError(fmt.Sprintf("dns_service.cache.ttl 格式无效: %s", ttl))
		}
	}

	if interval, ok := cache["cleanup_interval"].(string); ok {
		if !v.isValidDuration(interval) {
			v.addError(fmt.Sprintf("dns_service.cache.cleanup_interval 格式无效: %s", interval))
		}
	}

	if maxSize, ok := cache["max_size"].(int); ok {
		if maxSize < 1 {
			v.addError(fmt.Sprintf("dns_service.cache.max_size 必须大于0: %d", maxSize))
		}
	}
}

// validateDNSServersConfig 验证DNS服务器配置
func (v *ConfigValidator) validateDNSServersConfig(servers map[string]interface{}) {
	// 验证主服务器
	if primary, ok := servers["primary"].([]interface{}); ok {
		for i, server := range primary {
			if serverMap, ok := server.(map[string]interface{}); ok {
				v.validateDNSServerEntry(serverMap, fmt.Sprintf("dns_service.servers.primary[%d]", i))
			}
		}
	}

	// 验证备用服务器
	if secondary, ok := servers["secondary"].([]interface{}); ok {
		for i, server := range secondary {
			if serverMap, ok := server.(map[string]interface{}); ok {
				v.validateDNSServerEntry(serverMap, fmt.Sprintf("dns_service.servers.secondary[%d]", i))
			}
		}
	}

	// 验证回退策略
	if fallback, ok := servers["fallback"].(string); ok {
		validFallbacks := []string{"system", "none"}
		if !v.contains(validFallbacks, fallback) {
			v.addError(fmt.Sprintf("dns_service.servers.fallback 值无效: %s，有效值: %v", fallback, validFallbacks))
		}
	}
}

// validateDNSServerEntry 验证DNS服务器条目
func (v *ConfigValidator) validateDNSServerEntry(server map[string]interface{}, prefix string) {
	// 验证server字段
	if serverAddr, ok := server["server"].(string); ok {
		if !v.isValidServerAddress(serverAddr) {
			v.addError(fmt.Sprintf("%s.server 地址格式无效: %s", prefix, serverAddr))
		}
	} else {
		v.addError(fmt.Sprintf("%s.server 字段缺失", prefix))
	}

	// 验证protocol字段
	if protocol, ok := server["protocol"].(string); ok {
		validProtocols := []string{"udp", "tcp", "doh", "dot"}
		if !v.contains(validProtocols, protocol) {
			v.addError(fmt.Sprintf("%s.protocol 值无效: %s，有效值: %v", prefix, protocol, validProtocols))
		}
	}

	// 验证timeout字段
	if timeout, ok := server["timeout"].(int); ok {
		if timeout < 1000 || timeout > 30000 {
			v.addError(fmt.Sprintf("%s.timeout 超出范围: %d，有效范围: 1000-30000", prefix, timeout))
		}
	}

	// 验证priority字段
	if priority, ok := server["priority"].(int); ok {
		if priority < 0 || priority > 100 {
			v.addError(fmt.Sprintf("%s.priority 超出范围: %d，有效范围: 0-100", prefix, priority))
		}
	}
}

// validateDNSReverseLookupConfig 验证反向DNS查询配置
func (v *ConfigValidator) validateDNSReverseLookupConfig(reverseLookup map[string]interface{}) {
	if mode, ok := reverseLookup["mode"].(string); ok {
		validModes := []string{"no", "dns", "file"}
		if !v.contains(validModes, mode) {
			v.addError(fmt.Sprintf("dns_service.reverse_lookup.mode 值无效: %s，有效值: %v", mode, validModes))
		}

		// 如果是file模式，检查source字段
		if mode == "file" {
			if source, ok := reverseLookup["source"].(string); ok {
				if source == "" {
					v.addError("dns_service.reverse_lookup.source 不能为空（当mode为file时）")
				}
			} else {
				v.addError("dns_service.reverse_lookup.source 字段缺失（当mode为file时）")
			}
		}
	}
}

// validateCacheConfig 验证缓存配置
func (v *ConfigValidator) validateCacheConfig(config map[string]interface{}) {
	cache, ok := config["cache"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少cache配置节")
		return
	}

	// 检查是否还有旧的DNS缓存配置
	if _, exists := cache["dns"]; exists {
		v.addWarning("cache.dns 已废弃，请使用 dns_service.cache 配置")
	}
}

// validateServerConfig 验证服务器配置
func (v *ConfigValidator) validateServerConfig(config map[string]interface{}) {
	server, ok := config["server"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少server配置节")
		return
	}

	// 验证端口
	if port, ok := server["port"].(int); ok {
		if port < 1 || port > 65535 {
			v.addError(fmt.Sprintf("server.port 超出范围: %d，有效范围: 1-65535", port))
		}
	}
}

// validateProxyConfig 验证代理配置
func (v *ConfigValidator) validateProxyConfig(config map[string]interface{}) {
	proxy, ok := config["proxy"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少proxy配置节")
		return
	}

	// 验证策略
	if strategy, ok := proxy["strategy"].(string); ok {
		validStrategies := []string{"random", "sequential", "quality", "smart"}
		if !v.contains(validStrategies, strategy) {
			v.addError(fmt.Sprintf("proxy.strategy 值无效: %s，有效值: %v", strategy, validStrategies))
		}
	}
}

// validateLoggingConfig 验证日志配置
func (v *ConfigValidator) validateLoggingConfig(config map[string]interface{}) {
	logging, ok := config["logging"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少logging配置节")
		return
	}

	// 验证日志级别
	if level, ok := logging["level"].(string); ok {
		validLevels := []string{"debug", "info", "warn", "error", "fatal"}
		if !v.contains(validLevels, level) {
			v.addError(fmt.Sprintf("logging.level 值无效: %s，有效值: %v", level, validLevels))
		}
	}
}

// validateMonitoringConfig 验证监控配置
func (v *ConfigValidator) validateMonitoringConfig(config map[string]interface{}) {
	monitoring, ok := config["monitoring"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少monitoring配置节")
		return
	}

	// 验证端口
	if port, ok := monitoring["port"].(int); ok {
		if port < 1 || port > 65535 {
			v.addError(fmt.Sprintf("monitoring.port 超出范围: %d，有效范围: 1-65535", port))
		}
	}
}

// validateSecurityConfig 验证安全配置
func (v *ConfigValidator) validateSecurityConfig(config map[string]interface{}) {
	security, ok := config["security"].(map[string]interface{})
	if !ok {
		v.addWarning("缺少security配置节")
		return
	}

	// 这里可以添加更多安全配置验证
	_ = security
}

// checkConfigConflicts 检查配置冲突
func (v *ConfigValidator) checkConfigConflicts(config map[string]interface{}) {
	// 检查端口冲突
	ports := make(map[int][]string)

	if server, ok := config["server"].(map[string]interface{}); ok {
		if port, ok := server["port"].(int); ok {
			ports[port] = append(ports[port], "server.port")
		}
	}

	if monitoring, ok := config["monitoring"].(map[string]interface{}); ok {
		if port, ok := monitoring["port"].(int); ok {
			ports[port] = append(ports[port], "monitoring.port")
		}
	}

	for port, configs := range ports {
		if len(configs) > 1 {
			v.addError(fmt.Sprintf("端口冲突: %d 被以下配置使用: %v", port, configs))
		}
	}
}

// 辅助方法
func (v *ConfigValidator) addError(msg string) {
	v.errors = append(v.errors, msg)
}

func (v *ConfigValidator) addWarning(msg string) {
	v.warnings = append(v.warnings, msg)
}

func (v *ConfigValidator) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func (v *ConfigValidator) isValidDuration(duration string) bool {
	_, err := time.ParseDuration(duration)
	return err == nil
}

func (v *ConfigValidator) isValidServerAddress(addr string) bool {
	// 简单的服务器地址验证
	if strings.HasPrefix(addr, "https://") {
		return true // DoH地址
	}

	// IP:Port 格式
	parts := strings.Split(addr, ":")
	if len(parts) != 2 {
		return false
	}

	// 验证IP地址（简单验证）
	ipPattern := `^(\d{1,3}\.){3}\d{1,3}$`
	matched, _ := regexp.MatchString(ipPattern, parts[0])
	if !matched {
		return false
	}

	// 验证端口
	port, err := strconv.Atoi(parts[1])
	if err != nil || port < 1 || port > 65535 {
		return false
	}

	return true
}

func (v *ConfigValidator) printResults() {
	fmt.Println("\n📊 配置验证结果:")
	fmt.Println(strings.Repeat("=", 50))

	if len(v.errors) == 0 && len(v.warnings) == 0 {
		fmt.Println("✅ 配置验证通过，未发现问题")
		return
	}

	if len(v.errors) > 0 {
		fmt.Printf("❌ 发现 %d 个错误:\n", len(v.errors))
		for i, err := range v.errors {
			fmt.Printf("  %d. %s\n", i+1, err)
		}
		fmt.Println()
	}

	if len(v.warnings) > 0 {
		fmt.Printf("⚠️  发现 %d 个警告:\n", len(v.warnings))
		for i, warning := range v.warnings {
			fmt.Printf("  %d. %s\n", i+1, warning)
		}
		fmt.Println()
	}

	if len(v.errors) == 0 {
		fmt.Println("✅ 配置验证通过（仅有警告）")
	} else {
		fmt.Println("❌ 配置验证失败")
	}
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run config_validator.go <config_file>")
		fmt.Println("示例: go run config_validator.go config.yaml")
		os.Exit(1)
	}

	configFile := os.Args[1]

	// 检查配置文件是否存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", configFile)
	}

	// 执行验证
	validator := NewConfigValidator(configFile)
	if err := validator.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	fmt.Println("\n🎉 配置验证完成!")
}
