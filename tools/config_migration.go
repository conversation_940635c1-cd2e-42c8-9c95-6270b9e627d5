package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// LegacyGlobalConfig 旧版本的全局配置结构
type LegacyGlobalConfig struct {
	Enable                   bool                  `yaml:"enable"`
	ProxyFile                string                `yaml:"proxy_file"`
	DNSLookupMode            string                `yaml:"dns_lookup_mode"`
	ReverseDNSLookup         string                `yaml:"reverse_dns_lookup"`
	CustomDNSServers         []LegacyDNSServer     `yaml:"custom_dns_servers"`
	HTTPProxyDNS             string                `yaml:"http_proxy_dns"`
	IPRotationMode           string                `yaml:"ip_rotation_mode"`
	MinProxyPoolSize         int                   `yaml:"min_proxy_pool_size"`
	MaxProxyFetchAttempts    int                   `yaml:"max_proxy_fetch_attempts"`
	DNSCacheTTL              int                   `yaml:"dns_cache_ttl"`
	DNSNoCache               bool                  `yaml:"dns_no_cache"`
	IPVersionPriority        string                `yaml:"ip_version_priority"`
	DefaultDNSTimeout        int                   `yaml:"default_dns_timeout"`
	RetryProxyReusePolicy    string                `yaml:"retry_proxy_reuse_policy"`
}

// LegacyDNSServer 旧版本的DNS服务器配置
type LegacyDNSServer struct {
	Server   string   `yaml:"server"`
	Protocol string   `yaml:"protocol"`
	Timeout  int      `yaml:"timeout"`
	Priority int      `yaml:"priority"`
	Tags     []string `yaml:"tags"`
}

// LegacyCacheConfig 旧版本的缓存配置
type LegacyCacheConfig struct {
	Enabled         bool              `yaml:"enabled"`
	Type            string            `yaml:"type"`
	TTL             string            `yaml:"ttl"`
	Size            int               `yaml:"size"`
	CleanupInterval string            `yaml:"cleanup_interval"`
	KeyPrefixes     map[string]string `yaml:"key_prefixes"`
	DNS             *LegacyDNSCache   `yaml:"dns"`
}

// LegacyDNSCache 旧版本的DNS缓存配置
type LegacyDNSCache struct {
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
}

// NewDNSServiceConfig 新版本的DNS服务配置
type NewDNSServiceConfig struct {
	Enabled           bool                        `yaml:"enabled"`
	LookupMode        string                      `yaml:"lookup_mode"`
	Timeout           string                      `yaml:"timeout"`
	Retries           int                         `yaml:"retries"`
	Cache             *NewDNSServiceCacheConfig   `yaml:"cache"`
	Servers           *NewDNSServersConfig        `yaml:"servers"`
	IPVersionPriority string                      `yaml:"ip_version_priority"`
	ReverseLookup     *NewDNSReverseLookupConfig  `yaml:"reverse_lookup"`
}

// NewDNSServiceCacheConfig 新版本的DNS缓存配置
type NewDNSServiceCacheConfig struct {
	Enabled         bool   `yaml:"enabled"`
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
	MaxSize         int    `yaml:"max_size"`
}

// NewDNSServersConfig 新版本的DNS服务器配置
type NewDNSServersConfig struct {
	Primary   []NewDNSServerEntry `yaml:"primary"`
	Secondary []NewDNSServerEntry `yaml:"secondary"`
	Fallback  string              `yaml:"fallback"`
}

// NewDNSServerEntry 新版本的DNS服务器条目
type NewDNSServerEntry struct {
	Server   string   `yaml:"server"`
	Protocol string   `yaml:"protocol"`
	Timeout  int      `yaml:"timeout"`
	Priority int      `yaml:"priority"`
	Tags     []string `yaml:"tags"`
}

// NewDNSReverseLookupConfig 新版本的反向DNS查询配置
type NewDNSReverseLookupConfig struct {
	Enabled bool   `yaml:"enabled"`
	Mode    string `yaml:"mode"`
	Source  string `yaml:"source,omitempty"`
}

// ConfigMigrator 配置迁移器
type ConfigMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewConfigMigrator 创建配置迁移器
func NewConfigMigrator(inputFile, outputFile string) *ConfigMigrator {
	backupFile := fmt.Sprintf("%s.backup.%d", inputFile, time.Now().Unix())
	return &ConfigMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行配置迁移
func (m *ConfigMigrator) Migrate() error {
	fmt.Printf("🔄 开始配置迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migrateConfig(config); err != nil {
		return fmt.Errorf("迁移配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ 配置迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *ConfigMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migrateConfig 迁移配置
func (m *ConfigMigrator) migrateConfig(config map[string]interface{}) error {
	fmt.Println("📋 开始DNS配置迁移...")

	// 提取global中的DNS配置
	global, ok := config["global"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("未找到global配置节")
	}

	// 创建新的DNS服务配置
	dnsService := &NewDNSServiceConfig{
		Enabled: true,
		Retries: 3,
	}

	// 迁移DNS查找模式
	if mode, ok := global["dns_lookup_mode"].(string); ok {
		dnsService.LookupMode = mode
		delete(global, "dns_lookup_mode")
		fmt.Printf("  ✅ 迁移 dns_lookup_mode: %s\n", mode)
	}

	// 迁移DNS超时
	if timeout, ok := global["default_dns_timeout"].(int); ok {
		dnsService.Timeout = fmt.Sprintf("%dms", timeout)
		delete(global, "default_dns_timeout")
		fmt.Printf("  ✅ 迁移 default_dns_timeout: %dms\n", timeout)
	} else {
		dnsService.Timeout = "5s"
	}

	// 迁移IP版本优先级
	if priority, ok := global["ip_version_priority"].(string); ok {
		dnsService.IPVersionPriority = priority
		delete(global, "ip_version_priority")
		fmt.Printf("  ✅ 迁移 ip_version_priority: %s\n", priority)
	} else {
		dnsService.IPVersionPriority = "ipv4"
	}

	// 迁移DNS缓存配置
	dnsService.Cache = &NewDNSServiceCacheConfig{
		Enabled: true,
		MaxSize: 1000,
	}

	if cacheTTL, ok := global["dns_cache_ttl"].(int); ok {
		dnsService.Cache.TTL = fmt.Sprintf("%ds", cacheTTL)
		delete(global, "dns_cache_ttl")
		fmt.Printf("  ✅ 迁移 dns_cache_ttl: %ds\n", cacheTTL)
	} else {
		dnsService.Cache.TTL = "300s"
	}

	if noCache, ok := global["dns_no_cache"].(bool); ok {
		dnsService.Cache.Enabled = !noCache
		delete(global, "dns_no_cache")
		fmt.Printf("  ✅ 迁移 dns_no_cache: %t -> cache.enabled: %t\n", noCache, !noCache)
	}

	dnsService.Cache.CleanupInterval = "600s"

	// 迁移反向DNS查询配置
	dnsService.ReverseLookup = &NewDNSReverseLookupConfig{
		Enabled: true,
		Mode:    "dns",
	}

	if reverseLookup, ok := global["reverse_dns_lookup"].(string); ok {
		if strings.HasPrefix(reverseLookup, "file:") {
			dnsService.ReverseLookup.Mode = "file"
			dnsService.ReverseLookup.Source = strings.TrimPrefix(reverseLookup, "file:")
		} else if reverseLookup == "no" {
			dnsService.ReverseLookup.Enabled = false
			dnsService.ReverseLookup.Mode = "no"
		} else {
			dnsService.ReverseLookup.Mode = reverseLookup
		}
		delete(global, "reverse_dns_lookup")
		fmt.Printf("  ✅ 迁移 reverse_dns_lookup: %s\n", reverseLookup)
	}

	// 迁移自定义DNS服务器
	if servers, ok := global["custom_dns_servers"].([]interface{}); ok {
		dnsService.Servers = &NewDNSServersConfig{
			Primary:   []NewDNSServerEntry{},
			Secondary: []NewDNSServerEntry{},
			Fallback:  "system",
		}

		for i, server := range servers {
			if serverMap, ok := server.(map[string]interface{}); ok {
				entry := NewDNSServerEntry{}
				if s, ok := serverMap["server"].(string); ok {
					entry.Server = s
				}
				if p, ok := serverMap["protocol"].(string); ok {
					entry.Protocol = p
				}
				if t, ok := serverMap["timeout"].(int); ok {
					entry.Timeout = t
				}
				if pr, ok := serverMap["priority"].(int); ok {
					entry.Priority = pr
				}
				if tags, ok := serverMap["tags"].([]interface{}); ok {
					for _, tag := range tags {
						if tagStr, ok := tag.(string); ok {
							entry.Tags = append(entry.Tags, tagStr)
						}
					}
				}

				// 根据优先级分配到主或备用服务器
				if entry.Priority <= 2 {
					dnsService.Servers.Primary = append(dnsService.Servers.Primary, entry)
				} else {
					dnsService.Servers.Secondary = append(dnsService.Servers.Secondary, entry)
				}
			}
			fmt.Printf("  ✅ 迁移 custom_dns_servers[%d]\n", i)
		}
		delete(global, "custom_dns_servers")
	}

	// 移除HTTP代理DNS配置（这个配置可能需要移到其他地方）
	if httpProxyDNS, ok := global["http_proxy_dns"].(string); ok {
		delete(global, "http_proxy_dns")
		fmt.Printf("  ⚠️  移除 http_proxy_dns: %s (需要手动配置到相应模块)\n", httpProxyDNS)
	}

	// 将新的DNS服务配置添加到配置中
	config["dns_service"] = dnsService

	// 从cache配置中移除DNS相关配置
	if cache, ok := config["cache"].(map[string]interface{}); ok {
		if _, exists := cache["dns"]; exists {
			delete(cache, "dns")
			fmt.Printf("  ✅ 从cache配置中移除DNS配置\n")
		}
	}

	fmt.Println("✅ DNS配置迁移完成")
	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run config_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run config_migration.go config.yaml config_new.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewConfigMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("配置迁移失败: %v", err)
	}

	fmt.Println("\n🎉 配置迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - 所有DNS相关配置已统一到 dns_service 节")
	fmt.Println("  - global 中的DNS配置已移除")
	fmt.Println("  - cache 中的DNS配置已移除")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
