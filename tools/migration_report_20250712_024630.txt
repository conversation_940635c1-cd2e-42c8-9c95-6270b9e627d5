🔍 开始对比配置文件:
  旧配置: ../config.yaml
  新配置: ../config_final_optimized.yaml
============================================================

📋 Global配置对比:
----------------------------------------
❌ 已移除的DNS相关字段:

✅ 保留的字段:
  - banned_domains: [map[domain:malicious-site.com duration:86400] map[domain:spam-domain.net duration:24h] map[domain:blocked-forever.com duration:reboot]]
  - retry_proxy_reuse_policy: cooldown
  - rule_priority: 50
  - default_process_stage: pre
  - global_banned_ips: [map[duration:3600 ip:***********00] map[duration:1h30m ip:*********] map[duration:reboot ip:***********]]
  - retry_proxy_cooldown_time: 60
  - retry_proxy_global_tracking: true
  - blocked_ips: [*********** ************ *********]
  - enable: true
  - excluded_scope: all
  - proxy_file: ./proxies.txt
  - trusted_ips: [127.0.0.1 ::1 *********** ********]
  - excluded_patterns: [*.local localhost:* 127.0.0.1:* *.internal.company.com]
  - ip_rotation_mode: smart
  - max_proxy_fetch_attempts: 3
  - min_proxy_pool_size: 10

📋 DNS配置对比:
----------------------------------------
🔄 配置迁移映射:
  dns_lookup_mode: (未设置) → dns_service.lookup_mode: custom (默认值)

✅ 新的DNS服务配置结构:
  reverse_lookup:
    enabled: true
    mode: dns
    source: 
  servers:
    fallback: system
    primary: [2个条目]
    secondary: [2个条目]
  timeout: 5s
  cache:
    cleanup_interval: 600s
    enabled: true
    max_size: 1000
    ttl: 300s
  enabled: true
  ip_version_priority: ipv4
  lookup_mode: custom
  retries: 3

📋 Cache配置对比:
----------------------------------------

✅ 保留的缓存配置:
  - enabled: true
  - global: map[cleanup_interval:300s default_size:1000 default_ttl:3600s enabled:true eviction_policy:lru max_memory_usage:512MB]
  - key_prefixes: map[auth_cache:auth:cache: dns_cache:dns:cache: proxy_list:proxy:list proxy_status:proxy:status: rate_limit:rate:limit: regex_cache:regex:cache: user_session:user:session:]
  - size: 1000
  - type: memory
  - cleanup_interval: 300s
  - modules: map[auth:map[cleanup_interval:1800s compression:false enabled:true eviction_policy:lru size:100 ttl:3600s] dns:map[cleanup_interval:600s compression:false enabled:true eviction_policy:lru size:500 ttl:300s] proxy:map[cleanup_interval:900s compression:false enabled:true eviction_policy:lru size:200 ttl:1800s] rate:map[cleanup_interval:600s compression:false enabled:true eviction_policy:fifo size:1000 ttl:300s] regex:map[cleanup_interval:7200s compression:false enabled:true eviction_policy:lfu size:50 ttl:86400s] session:map[cleanup_interval:3600s compression:false enabled:true eviction_policy:lru size:100 ttl:7200s]]
  - policies: map[compression:map[algorithm:gzip enabled:false level:6 min_size:1024] invalidation:map[events:[] patterns:[] strategy:ttl] sync:map[enabled:false interval:60s mode:master] warmup:map[batch_size:100 enabled:false on_startup:false sources:[]]]
  - storage: map[file:map[compression:true directory:./cache max_file_size:100MB max_files:1000] memory:map[eviction_policy:lru max_memory:256MB max_size:10000 shards:16] redis:map[address:localhost:6379 database:0 dial_timeout:5s max_retries:3 password: pool_size:10] type:memory]
  - ttl: 3600s

📋 Timeouts配置对比:
----------------------------------------
🔄 超时配置迁移映射:
  server.read_timeout: 30s → timeouts.network.read: 30s
  server.write_timeout: 30s → timeouts.network.write: 30s
  server.idle_timeout: 120s → timeouts.network.idle: 120s
  server.connect_timeout: 10s → timeouts.network.connect: 10s
  proxy.retry_interval: 1s → timeouts.proxy.retry: 1s
  proxy.max_retry_interval: 30s → timeouts.proxy.max_retry: 30s
  proxy.health_check.timeout: 10s → timeouts.proxy.health_check: 10s
  dns_service.timeout: 5s → timeouts.dns.query: 5s
  cache.cleanup_interval: 300s → timeouts.cache.cleanup: 300s

✅ 新的统一超时配置结构:
  proxy:
    cooldown: 60s
    health_check: 10s
    max_retry: 30s
    retry: 1s
  action:
    bypass: 30s
    default: 10s
    request: 30s
  cache:
    cleanup: 300s
  dns:
    query: 5s
    resolve: 5s
  monitoring:
    collection: 3s
    interval: 5s
  network:
    connect: 10s
    idle: 120s
    read: 30s
    write: 30s

📋 Ports配置对比:
----------------------------------------
🔄 端口配置迁移映射:
  server.port: 8080 → ports.http: 8080
  server.https_port: 8443 → ports.https: 8443
  server.socks_port: 1080 → ports.socks: 1080
  monitoring.port: 9090 → ports.monitoring: 9090
  server.profiling.port: 6060 → ports.debug: 6060

✅ 新的统一端口配置结构:
  debug: 6060
  dhcp: 67
  https: 8443
  admin: 8081
  conflict_detection:
    auto_resolve: false
    check_local: true
    enabled: true
    exclude_ports: [0个条目]
    fallback_ports: [4个条目]
  monitoring: 9090
  ranges:
    user_start: 1024
    dynamic_end: 65535
    dynamic_start: 49152
    reserved_end: 1023
    reserved_start: 1
    user_end: 49151
  socks: 1080
  dns: 53
  http: 8080

🔍 端口冲突检测配置:
  conflict_detection:
    enabled: true
    exclude_ports: [0个条目]
    fallback_ports: [4个条目]
    auto_resolve: false
    check_local: true

📊 端口范围配置:
  ranges:
    dynamic_end: 65535
    dynamic_start: 49152
    reserved_end: 1023
    reserved_start: 1
    user_end: 49151
    user_start: 1024

📋 Cache层次化配置对比:
----------------------------------------
🔄 缓存配置层次化迁移:
  cache.enabled: true → cache.global.enabled: true
  cache.ttl: 3600s → cache.global.default_ttl: 3600s
  cache.size: 1000 → cache.global.default_size: 1000
  cache.cleanup_interval: 300s → cache.global.cleanup_interval: 300s

📦 新增模块缓存配置:
  dns模块: TTL=300s, Size=500, Policy=lru
  proxy模块: TTL=1800s, Size=200, Policy=lru
  session模块: TTL=7200s, Size=100, Policy=lru
  regex模块: TTL=86400s, Size=50, Policy=lfu
  auth模块: TTL=3600s, Size=100, Policy=lru
  rate模块: TTL=300s, Size=1000, Policy=fifo

📋 新增缓存策略配置:
  warmup策略: enabled=false
  sync策略: enabled=false
  compression策略: enabled=false
  cache.type: memory → cache.storage.type: memory

💾 新增存储后端配置:
  memory存储: 已配置
  redis存储: 已配置
  file存储: 已配置

✅ 新的层次化缓存配置结构:
  global:
    enabled: true
    eviction_policy: lru
    max_memory_usage: 512MB
    cleanup_interval: 300s
    default_size: 1000
    default_ttl: 3600s
  modules:
    session:
      cleanup_interval: 3600s
      compression: false
      enabled: true
      eviction_policy: lru
      size: 100
      ttl: 7200s
    auth:
      cleanup_interval: 1800s
      compression: false
      enabled: true
      eviction_policy: lru
      size: 100
      ttl: 3600s
    dns:
      cleanup_interval: 600s
      compression: false
      enabled: true
      eviction_policy: lru
      size: 500
      ttl: 300s
    proxy:
      cleanup_interval: 900s
      compression: false
      enabled: true
      eviction_policy: lru
      size: 200
      ttl: 1800s
    rate:
      cleanup_interval: 600s
      compression: false
      enabled: true
      eviction_policy: fifo
      size: 1000
      ttl: 300s
    regex:
      cleanup_interval: 7200s
      compression: false
      enabled: true
      eviction_policy: lfu
      size: 50
      ttl: 86400s
  key_prefixes:
    user_session: user:session:
    auth_cache: auth:cache:
    dns_cache: dns:cache:
    proxy_list: proxy:list
    proxy_status: proxy:status:
    rate_limit: rate:limit:
    regex_cache: regex:cache:
  policies:
    invalidation:
      patterns: []
      strategy: ttl
      events: []
    sync:
      mode: master
      enabled: false
      interval: 60s
    warmup:
      batch_size: 100
      enabled: false
      on_startup: false
      sources: []
    compression:
      enabled: false
      level: 6
      min_size: 1024
      algorithm: gzip
  storage:
    type: memory
    file:
      max_file_size: 100MB
      max_files: 1000
      compression: true
      directory: ./cache
    memory:
      shards: 16
      eviction_policy: lru
      max_memory: 256MB
      max_size: 10000
    redis:
      max_retries: 3
      password: 
      pool_size: 10
      address: localhost:6379
      database: 0
      dial_timeout: 5s

📊 配置文件大小对比:
----------------------------------------
旧配置大小: 41216 字节
新配置大小: 29515 字节
减少大小: 11701 字节 (28.4%)

旧配置行数: 1267 行
新配置行数: 1091 行
减少行数: 176 行 (13.9%)

🎉 配置对比完成!
