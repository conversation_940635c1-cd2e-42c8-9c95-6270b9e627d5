package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// ConfigComparison 配置对比工具
type ConfigComparison struct {
	oldConfigFile string
	newConfigFile string
}

// NewConfigComparison 创建配置对比工具
func NewConfigComparison(oldFile, newFile string) *ConfigComparison {
	return &ConfigComparison{
		oldConfigFile: oldFile,
		newConfigFile: newFile,
	}
}

// Compare 对比配置文件
func (c *ConfigComparison) Compare() error {
	fmt.Printf("🔍 开始对比配置文件:\n")
	fmt.Printf("  旧配置: %s\n", c.oldConfigFile)
	fmt.Printf("  新配置: %s\n", c.newConfigFile)
	fmt.Println(strings.Repeat("=", 60))

	// 读取旧配置
	oldData, err := ioutil.ReadFile(c.oldConfigFile)
	if err != nil {
		return fmt.Errorf("读取旧配置失败: %v", err)
	}

	var oldConfig map[string]interface{}
	if err := yaml.Unmarshal(oldData, &oldConfig); err != nil {
		return fmt.Errorf("解析旧配置失败: %v", err)
	}

	// 读取新配置
	newData, err := ioutil.ReadFile(c.newConfigFile)
	if err != nil {
		return fmt.Errorf("读取新配置失败: %v", err)
	}

	var newConfig map[string]interface{}
	if err := yaml.Unmarshal(newData, &newConfig); err != nil {
		return fmt.Errorf("解析新配置失败: %v", err)
	}

	// 执行对比
	c.compareGlobalConfig(oldConfig, newConfig)
	c.compareDNSConfig(oldConfig, newConfig)
	c.compareCacheConfig(oldConfig, newConfig)
	c.compareTimeoutsConfig(oldConfig, newConfig)
	c.comparePortsConfig(oldConfig, newConfig)
	c.showConfigSizeComparison(oldData, newData)

	return nil
}

// compareGlobalConfig 对比全局配置
func (c *ConfigComparison) compareGlobalConfig(oldConfig, newConfig map[string]interface{}) {
	fmt.Println("\n📋 Global配置对比:")
	fmt.Println(strings.Repeat("-", 40))

	oldGlobal, _ := oldConfig["global"].(map[string]interface{})
	newGlobal, _ := newConfig["global"].(map[string]interface{})

	// 检查移除的DNS相关字段
	removedDNSFields := []string{
		"dns_lookup_mode", "reverse_dns_lookup", "custom_dns_servers",
		"http_proxy_dns", "dns_cache_ttl", "dns_no_cache",
		"ip_version_priority", "default_dns_timeout",
	}

	fmt.Println("❌ 已移除的DNS相关字段:")
	for _, field := range removedDNSFields {
		if value, exists := oldGlobal[field]; exists {
			fmt.Printf("  - %s: %v\n", field, value)
		}
	}

	// 检查保留的字段
	fmt.Println("\n✅ 保留的字段:")
	for key, value := range newGlobal {
		if !c.contains(removedDNSFields, key) {
			fmt.Printf("  - %s: %v\n", key, value)
		}
	}
}

// compareDNSConfig 对比DNS配置
func (c *ConfigComparison) compareDNSConfig(oldConfig, newConfig map[string]interface{}) {
	fmt.Println("\n📋 DNS配置对比:")
	fmt.Println(strings.Repeat("-", 40))

	oldGlobal, _ := oldConfig["global"].(map[string]interface{})
	newDNSService, _ := newConfig["dns_service"].(map[string]interface{})

	fmt.Println("🔄 配置迁移映射:")

	// DNS查找模式
	if oldMode, exists := oldGlobal["dns_lookup_mode"]; exists {
		newMode := newDNSService["lookup_mode"]
		fmt.Printf("  dns_lookup_mode: %v → dns_service.lookup_mode: %v\n", oldMode, newMode)
	} else {
		newMode := newDNSService["lookup_mode"]
		fmt.Printf("  dns_lookup_mode: (未设置) → dns_service.lookup_mode: %v (默认值)\n", newMode)
	}

	// DNS超时
	if oldTimeout, exists := oldGlobal["default_dns_timeout"]; exists {
		newTimeout := newDNSService["timeout"]
		fmt.Printf("  default_dns_timeout: %v → dns_service.timeout: %v\n", oldTimeout, newTimeout)
	}

	// DNS缓存TTL
	if oldTTL, exists := oldGlobal["dns_cache_ttl"]; exists {
		if cache, ok := newDNSService["cache"].(map[string]interface{}); ok {
			newTTL := cache["ttl"]
			fmt.Printf("  dns_cache_ttl: %v → dns_service.cache.ttl: %v\n", oldTTL, newTTL)
		}
	}

	// IP版本优先级
	if oldPriority, exists := oldGlobal["ip_version_priority"]; exists {
		newPriority := newDNSService["ip_version_priority"]
		fmt.Printf("  ip_version_priority: %v → dns_service.ip_version_priority: %v\n", oldPriority, newPriority)
	}

	// 反向DNS查询
	if oldReverse, exists := oldGlobal["reverse_dns_lookup"]; exists {
		if reverseLookup, ok := newDNSService["reverse_lookup"].(map[string]interface{}); ok {
			newMode := reverseLookup["mode"]
			fmt.Printf("  reverse_dns_lookup: %v → dns_service.reverse_lookup.mode: %v\n", oldReverse, newMode)
		}
	}

	// 自定义DNS服务器
	if oldServers, exists := oldGlobal["custom_dns_servers"]; exists {
		newServers := newDNSService["servers"]
		fmt.Printf("  custom_dns_servers: %d个服务器 → dns_service.servers: %v\n", 
			c.countServers(oldServers), newServers)
	}

	fmt.Println("\n✅ 新的DNS服务配置结构:")
	c.printDNSServiceConfig(newDNSService, "  ")
}

// compareCacheConfig 对比缓存配置
func (c *ConfigComparison) compareCacheConfig(oldConfig, newConfig map[string]interface{}) {
	fmt.Println("\n📋 Cache配置对比:")
	fmt.Println(strings.Repeat("-", 40))

	oldCache, _ := oldConfig["cache"].(map[string]interface{})
	newCache, _ := newConfig["cache"].(map[string]interface{})

	// 检查移除的DNS缓存配置
	if oldDNSCache, exists := oldCache["dns"]; exists {
		fmt.Printf("❌ 已移除: cache.dns: %v\n", oldDNSCache)
		fmt.Printf("✅ 迁移到: dns_service.cache\n")
	}

	fmt.Println("\n✅ 保留的缓存配置:")
	for key, value := range newCache {
		if key != "dns" {
			fmt.Printf("  - %s: %v\n", key, value)
		}
	}
}

// compareTimeoutsConfig 对比超时配置
func (c *ConfigComparison) compareTimeoutsConfig(oldConfig, newConfig map[string]interface{}) {
	fmt.Println("\n📋 Timeouts配置对比:")
	fmt.Println(strings.Repeat("-", 40))

	newTimeouts, hasNewTimeouts := newConfig["timeouts"].(map[string]interface{})
	if !hasNewTimeouts {
		fmt.Println("⚠️  新配置中未找到timeouts配置节")
		return
	}

	fmt.Println("🔄 超时配置迁移映射:")

	// 检查网络超时迁移
	if server, ok := oldConfig["server"].(map[string]interface{}); ok {
		c.compareNetworkTimeouts(server, newTimeouts)
	}

	// 检查代理超时迁移
	if proxy, ok := oldConfig["proxy"].(map[string]interface{}); ok {
		c.compareProxyTimeouts(proxy, newTimeouts)
	}

	// 检查DNS超时迁移
	if dnsService, ok := oldConfig["dns_service"].(map[string]interface{}); ok {
		c.compareDNSTimeouts(dnsService, newTimeouts)
	}

	// 检查缓存超时迁移
	if cache, ok := oldConfig["cache"].(map[string]interface{}); ok {
		c.compareCacheTimeouts(cache, newTimeouts)
	}

	fmt.Println("\n✅ 新的统一超时配置结构:")
	c.printTimeoutsConfig(newTimeouts, "  ")
}

// compareNetworkTimeouts 对比网络超时配置
func (c *ConfigComparison) compareNetworkTimeouts(server map[string]interface{}, timeouts map[string]interface{}) {
	if network, ok := timeouts["network"].(map[string]interface{}); ok {
		timeoutMappings := map[string]string{
			"read_timeout":    "read",
			"write_timeout":   "write",
			"idle_timeout":    "idle",
			"connect_timeout": "connect",
		}

		for oldKey, newKey := range timeoutMappings {
			if oldValue, exists := server[oldKey]; exists {
				newValue := network[newKey]
				fmt.Printf("  server.%s: %v → timeouts.network.%s: %v\n", oldKey, oldValue, newKey, newValue)
			}
		}
	}
}

// compareProxyTimeouts 对比代理超时配置
func (c *ConfigComparison) compareProxyTimeouts(proxy map[string]interface{}, timeouts map[string]interface{}) {
	if proxyTimeouts, ok := timeouts["proxy"].(map[string]interface{}); ok {
		// 重试间隔
		if oldValue, exists := proxy["retry_interval"]; exists {
			newValue := proxyTimeouts["retry"]
			fmt.Printf("  proxy.retry_interval: %v → timeouts.proxy.retry: %v\n", oldValue, newValue)
		}

		// 最大重试间隔
		if oldValue, exists := proxy["max_retry_interval"]; exists {
			newValue := proxyTimeouts["max_retry"]
			fmt.Printf("  proxy.max_retry_interval: %v → timeouts.proxy.max_retry: %v\n", oldValue, newValue)
		}

		// 健康检查超时
		if healthCheck, exists := proxy["health_check"].(map[string]interface{}); exists {
			if oldValue, exists := healthCheck["timeout"]; exists {
				newValue := proxyTimeouts["health_check"]
				fmt.Printf("  proxy.health_check.timeout: %v → timeouts.proxy.health_check: %v\n", oldValue, newValue)
			}
		}
	}
}

// compareDNSTimeouts 对比DNS超时配置
func (c *ConfigComparison) compareDNSTimeouts(dnsService map[string]interface{}, timeouts map[string]interface{}) {
	if dnsTimeouts, ok := timeouts["dns"].(map[string]interface{}); ok {
		if oldValue, exists := dnsService["timeout"]; exists {
			newValue := dnsTimeouts["query"]
			fmt.Printf("  dns_service.timeout: %v → timeouts.dns.query: %v\n", oldValue, newValue)
		}
	}
}

// compareCacheTimeouts 对比缓存超时配置
func (c *ConfigComparison) compareCacheTimeouts(cache map[string]interface{}, timeouts map[string]interface{}) {
	if cacheTimeouts, ok := timeouts["cache"].(map[string]interface{}); ok {
		if oldValue, exists := cache["cleanup_interval"]; exists {
			newValue := cacheTimeouts["cleanup"]
			fmt.Printf("  cache.cleanup_interval: %v → timeouts.cache.cleanup: %v\n", oldValue, newValue)
		}
	}
}

// printTimeoutsConfig 打印超时配置
func (c *ConfigComparison) printTimeoutsConfig(timeouts map[string]interface{}, indent string) {
	for category, config := range timeouts {
		fmt.Printf("%s%s:\n", indent, category)
		if configMap, ok := config.(map[string]interface{}); ok {
			for key, value := range configMap {
				fmt.Printf("%s  %s: %v\n", indent, key, value)
			}
		}
	}
}

// comparePortsConfig 对比端口配置
func (c *ConfigComparison) comparePortsConfig(oldConfig, newConfig map[string]interface{}) {
	fmt.Println("\n📋 Ports配置对比:")
	fmt.Println(strings.Repeat("-", 40))

	newPorts, hasNewPorts := newConfig["ports"].(map[string]interface{})
	if !hasNewPorts {
		fmt.Println("⚠️  新配置中未找到ports配置节")
		return
	}

	fmt.Println("🔄 端口配置迁移映射:")

	// 检查服务器端口迁移
	if server, ok := oldConfig["server"].(map[string]interface{}); ok {
		c.compareServerPorts(server, newPorts)
	}

	// 检查监控端口迁移
	if monitoring, ok := oldConfig["monitoring"].(map[string]interface{}); ok {
		c.compareMonitoringPorts(monitoring, newPorts)
	}

	// 检查调试端口迁移
	c.compareDebugPorts(oldConfig, newPorts)

	fmt.Println("\n✅ 新的统一端口配置结构:")
	c.printPortsConfig(newPorts, "  ")

	// 显示端口冲突检测信息
	if conflictDetection, ok := newPorts["conflict_detection"].(map[string]interface{}); ok {
		fmt.Println("\n🔍 端口冲突检测配置:")
		c.printPortsConfig(map[string]interface{}{"conflict_detection": conflictDetection}, "  ")
	}

	// 显示端口范围配置
	if ranges, ok := newPorts["ranges"].(map[string]interface{}); ok {
		fmt.Println("\n📊 端口范围配置:")
		c.printPortsConfig(map[string]interface{}{"ranges": ranges}, "  ")
	}
}

// compareServerPorts 对比服务器端口配置
func (c *ConfigComparison) compareServerPorts(server map[string]interface{}, ports map[string]interface{}) {
	portMappings := map[string]string{
		"port":        "http",
		"https_port":  "https",
		"socks_port":  "socks",
	}

	for oldKey, newKey := range portMappings {
		if oldValue, exists := server[oldKey]; exists {
			newValue := ports[newKey]
			fmt.Printf("  server.%s: %v → ports.%s: %v\n", oldKey, oldValue, newKey, newValue)
		}
	}
}

// compareMonitoringPorts 对比监控端口配置
func (c *ConfigComparison) compareMonitoringPorts(monitoring map[string]interface{}, ports map[string]interface{}) {
	if oldValue, exists := monitoring["port"]; exists {
		newValue := ports["monitoring"]
		fmt.Printf("  monitoring.port: %v → ports.monitoring: %v\n", oldValue, newValue)
	}
}

// compareDebugPorts 对比调试端口配置
func (c *ConfigComparison) compareDebugPorts(oldConfig map[string]interface{}, ports map[string]interface{}) {
	// 检查debug配置
	if debug, ok := oldConfig["debug"].(map[string]interface{}); ok {
		if oldValue, exists := debug["profile_port"]; exists {
			newValue := ports["debug"]
			fmt.Printf("  debug.profile_port: %v → ports.debug: %v\n", oldValue, newValue)
		}
	}

	// 检查server.profiling配置
	if server, ok := oldConfig["server"].(map[string]interface{}); ok {
		if profiling, ok := server["profiling"].(map[string]interface{}); ok {
			if oldValue, exists := profiling["port"]; exists {
				newValue := ports["debug"]
				fmt.Printf("  server.profiling.port: %v → ports.debug: %v\n", oldValue, newValue)
			}
		}
	}
}

// printPortsConfig 打印端口配置
func (c *ConfigComparison) printPortsConfig(ports map[string]interface{}, indent string) {
	for key, value := range ports {
		switch v := value.(type) {
		case map[string]interface{}:
			fmt.Printf("%s%s:\n", indent, key)
			c.printPortsConfig(v, indent+"  ")
		case []interface{}:
			fmt.Printf("%s%s: [%d个条目]\n", indent, key, len(v))
		default:
			fmt.Printf("%s%s: %v\n", indent, key, value)
		}
	}
}

// showConfigSizeComparison 显示配置文件大小对比
func (c *ConfigComparison) showConfigSizeComparison(oldData, newData []byte) {
	fmt.Println("\n📊 配置文件大小对比:")
	fmt.Println(strings.Repeat("-", 40))

	oldSize := len(oldData)
	newSize := len(newData)
	reduction := oldSize - newSize
	reductionPercent := float64(reduction) / float64(oldSize) * 100

	fmt.Printf("旧配置大小: %d 字节\n", oldSize)
	fmt.Printf("新配置大小: %d 字节\n", newSize)
	if reduction > 0 {
		fmt.Printf("减少大小: %d 字节 (%.1f%%)\n", reduction, reductionPercent)
	} else {
		fmt.Printf("增加大小: %d 字节 (%.1f%%)\n", -reduction, -reductionPercent)
	}

	// 统计行数
	oldLines := strings.Count(string(oldData), "\n")
	newLines := strings.Count(string(newData), "\n")
	lineReduction := oldLines - newLines
	lineReductionPercent := float64(lineReduction) / float64(oldLines) * 100

	fmt.Printf("\n旧配置行数: %d 行\n", oldLines)
	fmt.Printf("新配置行数: %d 行\n", newLines)
	if lineReduction > 0 {
		fmt.Printf("减少行数: %d 行 (%.1f%%)\n", lineReduction, lineReductionPercent)
	} else {
		fmt.Printf("增加行数: %d 行 (%.1f%%)\n", -lineReduction, -lineReductionPercent)
	}
}

// printDNSServiceConfig 打印DNS服务配置
func (c *ConfigComparison) printDNSServiceConfig(dnsService map[string]interface{}, indent string) {
	for key, value := range dnsService {
		switch v := value.(type) {
		case map[string]interface{}:
			fmt.Printf("%s%s:\n", indent, key)
			c.printDNSServiceConfig(v, indent+"  ")
		case []interface{}:
			fmt.Printf("%s%s: [%d个条目]\n", indent, key, len(v))
		default:
			fmt.Printf("%s%s: %v\n", indent, key, value)
		}
	}
}

// countServers 计算服务器数量
func (c *ConfigComparison) countServers(servers interface{}) int {
	if serverList, ok := servers.([]interface{}); ok {
		return len(serverList)
	}
	return 0
}

// contains 检查切片是否包含元素
func (c *ConfigComparison) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("使用方法: go run config_comparison.go <old_config> <new_config>")
		fmt.Println("示例: go run config_comparison.go config.yaml config_migrated.yaml")
		os.Exit(1)
	}

	oldFile := os.Args[1]
	newFile := os.Args[2]

	// 检查文件是否存在
	if _, err := os.Stat(oldFile); os.IsNotExist(err) {
		log.Fatalf("旧配置文件不存在: %s", oldFile)
	}

	if _, err := os.Stat(newFile); os.IsNotExist(err) {
		log.Fatalf("新配置文件不存在: %s", newFile)
	}

	// 执行对比
	comparison := NewConfigComparison(oldFile, newFile)
	if err := comparison.Compare(); err != nil {
		log.Fatalf("配置对比失败: %v", err)
	}

	fmt.Println("\n🎉 配置对比完成!")
}
