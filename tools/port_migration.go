package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// PortMigrator 端口配置迁移器
type PortMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewPortMigrator 创建端口配置迁移器
func NewPortMigrator(inputFile, outputFile string) *PortMigrator {
	backupFile := fmt.Sprintf("%s.port_backup.%d", inputFile, time.Now().Unix())
	return &PortMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行端口配置迁移
func (m *PortMigrator) Migrate() error {
	fmt.Printf("🔄 开始端口配置迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migratePorts(config); err != nil {
		return fmt.Errorf("迁移端口配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ 端口配置迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *PortMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migratePorts 迁移端口配置
func (m *PortMigrator) migratePorts(config map[string]interface{}) error {
	fmt.Println("📋 开始端口配置迁移...")

	// 创建统一的端口配置
	ports := map[string]interface{}{
		"http":       8080,  // 默认HTTP端口
		"https":      8443,  // 默认HTTPS端口
		"socks":      1080,  // 默认SOCKS端口
		"monitoring": 9090,  // 默认监控端口
		"debug":      6060,  // 默认调试端口
		"admin":      8081,  // 默认管理端口
		"dns":        53,    // 默认DNS端口
		"dhcp":       67,    // 默认DHCP端口
	}

	// 端口冲突检测配置
	conflictDetection := map[string]interface{}{
		"enabled":        true,
		"check_local":    true,
		"auto_resolve":   false,
		"exclude_ports":  []int{},
		"fallback_ports": []int{8082, 8083, 8084, 8085},
	}

	// 端口范围配置
	ranges := map[string]interface{}{
		"dynamic_start":  49152,
		"dynamic_end":    65535,
		"reserved_start": 1,
		"reserved_end":   1023,
		"user_start":     1024,
		"user_end":       49151,
	}

	// 迁移服务器端口配置
	if err := m.migrateServerPorts(config, ports); err != nil {
		return err
	}

	// 迁移监控端口配置
	if err := m.migrateMonitoringPorts(config, ports); err != nil {
		return err
	}

	// 迁移调试端口配置
	if err := m.migrateDebugPorts(config, ports); err != nil {
		return err
	}

	// 检测端口冲突
	if err := m.detectPortConflicts(ports, conflictDetection); err != nil {
		return err
	}

	// 构建完整的端口配置
	portsConfig := map[string]interface{}{
		"http":               ports["http"],
		"https":              ports["https"],
		"socks":              ports["socks"],
		"monitoring":         ports["monitoring"],
		"debug":              ports["debug"],
		"admin":              ports["admin"],
		"dns":                ports["dns"],
		"dhcp":               ports["dhcp"],
		"ranges":             ranges,
		"conflict_detection": conflictDetection,
	}

	// 将端口配置添加到主配置中
	config["ports"] = portsConfig

	fmt.Println("✅ 端口配置迁移完成")
	return nil
}

// migrateServerPorts 迁移服务器端口配置
func (m *PortMigrator) migrateServerPorts(config map[string]interface{}, ports map[string]interface{}) error {
	server, ok := config["server"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到server配置节")
		return nil
	}

	// 迁移HTTP端口
	if port, exists := server["port"]; exists {
		if portInt, ok := port.(int); ok && portInt > 0 {
			ports["http"] = portInt
			fmt.Printf("  ✅ 迁移 server.port: %d -> ports.http\n", portInt)
		}
	}

	// 迁移HTTPS端口
	if httpsPort, exists := server["https_port"]; exists {
		if portInt, ok := httpsPort.(int); ok && portInt > 0 {
			ports["https"] = portInt
			fmt.Printf("  ✅ 迁移 server.https_port: %d -> ports.https\n", portInt)
		}
	}

	// 迁移SOCKS端口
	if socksPort, exists := server["socks_port"]; exists {
		if portInt, ok := socksPort.(int); ok && portInt > 0 {
			ports["socks"] = portInt
			fmt.Printf("  ✅ 迁移 server.socks_port: %d -> ports.socks\n", portInt)
		}
	}

	return nil
}

// migrateMonitoringPorts 迁移监控端口配置
func (m *PortMigrator) migrateMonitoringPorts(config map[string]interface{}, ports map[string]interface{}) error {
	monitoring, ok := config["monitoring"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到monitoring配置节")
		return nil
	}

	// 迁移监控端口
	if port, exists := monitoring["port"]; exists {
		if portInt, ok := port.(int); ok && portInt > 0 {
			ports["monitoring"] = portInt
			fmt.Printf("  ✅ 迁移 monitoring.port: %d -> ports.monitoring\n", portInt)
		}
	}

	return nil
}

// migrateDebugPorts 迁移调试端口配置
func (m *PortMigrator) migrateDebugPorts(config map[string]interface{}, ports map[string]interface{}) error {
	// 检查是否有调试配置
	if debug, ok := config["debug"].(map[string]interface{}); ok {
		if profilePort, exists := debug["profile_port"]; exists {
			if portInt, ok := profilePort.(int); ok && portInt > 0 {
				ports["debug"] = portInt
				fmt.Printf("  ✅ 迁移 debug.profile_port: %d -> ports.debug\n", portInt)
			}
		}
	}

	// 检查是否有性能分析配置
	if server, ok := config["server"].(map[string]interface{}); ok {
		if profiling, ok := server["profiling"].(map[string]interface{}); ok {
			if profilePort, exists := profiling["port"]; exists {
				if portInt, ok := profilePort.(int); ok && portInt > 0 {
					ports["debug"] = portInt
					fmt.Printf("  ✅ 迁移 server.profiling.port: %d -> ports.debug\n", portInt)
				}
			}
		}
	}

	return nil
}

// detectPortConflicts 检测端口冲突
func (m *PortMigrator) detectPortConflicts(ports map[string]interface{}, conflictDetection map[string]interface{}) error {
	fmt.Println("  🔍 检测端口冲突...")

	usedPorts := make(map[int][]string)
	conflicts := []string{}

	// 收集所有使用的端口
	for service, port := range ports {
		if portInt, ok := port.(int); ok {
			usedPorts[portInt] = append(usedPorts[portInt], service)
		}
	}

	// 检测冲突
	for port, services := range usedPorts {
		if len(services) > 1 {
			conflict := fmt.Sprintf("端口 %d 被多个服务使用: %v", port, services)
			conflicts = append(conflicts, conflict)
			fmt.Printf("  ⚠️  %s\n", conflict)
		}
	}

	// 检测系统保留端口
	for service, port := range ports {
		if portInt, ok := port.(int); ok && portInt < 1024 {
			warning := fmt.Sprintf("服务 %s 使用系统保留端口: %d", service, portInt)
			fmt.Printf("  ⚠️  %s\n", warning)
		}
	}

	if len(conflicts) > 0 {
		fmt.Printf("  ⚠️  发现 %d 个端口冲突\n", len(conflicts))
		conflictDetection["conflicts"] = conflicts
	} else {
		fmt.Printf("  ✅ 未发现端口冲突\n")
	}

	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run port_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run port_migration.go config.yaml config_port_migrated.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewPortMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("端口配置迁移失败: %v", err)
	}

	fmt.Println("\n🎉 端口配置迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - 所有端口相关配置已统一到 ports 节")
	fmt.Println("  - server 中的端口配置已迁移到 ports")
	fmt.Println("  - monitoring 中的端口配置已迁移到 ports.monitoring")
	fmt.Println("  - debug 中的端口配置已迁移到 ports.debug")
	fmt.Println("  - 已添加端口冲突检测和范围配置")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
