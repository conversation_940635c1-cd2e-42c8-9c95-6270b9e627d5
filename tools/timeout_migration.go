package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// TimeoutMigrator 超时配置迁移器
type TimeoutMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewTimeoutMigrator 创建超时配置迁移器
func NewTimeoutMigrator(inputFile, outputFile string) *TimeoutMigrator {
	backupFile := fmt.Sprintf("%s.timeout_backup.%d", inputFile, time.Now().Unix())
	return &TimeoutMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行超时配置迁移
func (m *TimeoutMigrator) Migrate() error {
	fmt.Printf("🔄 开始超时配置迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migrateTimeouts(config); err != nil {
		return fmt.Errorf("迁移超时配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ 超时配置迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *TimeoutMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migrateTimeouts 迁移超时配置
func (m *TimeoutMigrator) migrateTimeouts(config map[string]interface{}) error {
	fmt.Println("📋 开始超时配置迁移...")

	// 创建统一的超时配置
	timeouts := map[string]interface{}{
		"network": map[string]interface{}{},
		"dns":     map[string]interface{}{},
		"proxy":   map[string]interface{}{},
		"monitoring": map[string]interface{}{},
		"cache":   map[string]interface{}{},
		"action":  map[string]interface{}{},
	}

	// 迁移服务器超时配置
	if err := m.migrateServerTimeouts(config, timeouts); err != nil {
		return err
	}

	// 迁移代理超时配置
	if err := m.migrateProxyTimeouts(config, timeouts); err != nil {
		return err
	}

	// 迁移DNS超时配置
	if err := m.migrateDNSTimeouts(config, timeouts); err != nil {
		return err
	}

	// 迁移缓存超时配置
	if err := m.migrateCacheTimeouts(config, timeouts); err != nil {
		return err
	}

	// 迁移监控超时配置
	if err := m.migrateMonitoringTimeouts(config, timeouts); err != nil {
		return err
	}

	// 设置默认的动作超时
	m.setDefaultActionTimeouts(timeouts)

	// 将超时配置添加到主配置中
	config["timeouts"] = timeouts

	fmt.Println("✅ 超时配置迁移完成")
	return nil
}

// migrateServerTimeouts 迁移服务器超时配置
func (m *TimeoutMigrator) migrateServerTimeouts(config map[string]interface{}, timeouts map[string]interface{}) error {
	server, ok := config["server"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到server配置节")
		return nil
	}

	network := timeouts["network"].(map[string]interface{})

	// 迁移各种超时配置
	if readTimeout, exists := server["read_timeout"]; exists {
		network["read"] = readTimeout
		fmt.Printf("  ✅ 迁移 server.read_timeout: %v -> timeouts.network.read\n", readTimeout)
	} else {
		network["read"] = "30s"
		fmt.Printf("  ✅ 设置默认 timeouts.network.read: 30s\n")
	}

	if writeTimeout, exists := server["write_timeout"]; exists {
		network["write"] = writeTimeout
		fmt.Printf("  ✅ 迁移 server.write_timeout: %v -> timeouts.network.write\n", writeTimeout)
	} else {
		network["write"] = "30s"
		fmt.Printf("  ✅ 设置默认 timeouts.network.write: 30s\n")
	}

	if idleTimeout, exists := server["idle_timeout"]; exists {
		network["idle"] = idleTimeout
		fmt.Printf("  ✅ 迁移 server.idle_timeout: %v -> timeouts.network.idle\n", idleTimeout)
	} else {
		network["idle"] = "120s"
		fmt.Printf("  ✅ 设置默认 timeouts.network.idle: 120s\n")
	}

	if connectTimeout, exists := server["connect_timeout"]; exists {
		network["connect"] = connectTimeout
		fmt.Printf("  ✅ 迁移 server.connect_timeout: %v -> timeouts.network.connect\n", connectTimeout)
	} else {
		network["connect"] = "10s"
		fmt.Printf("  ✅ 设置默认 timeouts.network.connect: 10s\n")
	}

	return nil
}

// migrateProxyTimeouts 迁移代理超时配置
func (m *TimeoutMigrator) migrateProxyTimeouts(config map[string]interface{}, timeouts map[string]interface{}) error {
	proxy, ok := config["proxy"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到proxy配置节")
		return nil
	}

	proxyTimeouts := timeouts["proxy"].(map[string]interface{})

	// 迁移重试间隔
	if retryInterval, exists := proxy["retry_interval"]; exists {
		proxyTimeouts["retry"] = retryInterval
		fmt.Printf("  ✅ 迁移 proxy.retry_interval: %v -> timeouts.proxy.retry\n", retryInterval)
	} else {
		proxyTimeouts["retry"] = "1s"
		fmt.Printf("  ✅ 设置默认 timeouts.proxy.retry: 1s\n")
	}

	// 迁移最大重试间隔
	if maxRetryInterval, exists := proxy["max_retry_interval"]; exists {
		proxyTimeouts["max_retry"] = maxRetryInterval
		fmt.Printf("  ✅ 迁移 proxy.max_retry_interval: %v -> timeouts.proxy.max_retry\n", maxRetryInterval)
	} else {
		proxyTimeouts["max_retry"] = "30s"
		fmt.Printf("  ✅ 设置默认 timeouts.proxy.max_retry: 30s\n")
	}

	// 迁移健康检查超时
	if healthCheck, exists := proxy["health_check"].(map[string]interface{}); exists {
		if timeout, exists := healthCheck["timeout"]; exists {
			proxyTimeouts["health_check"] = timeout
			fmt.Printf("  ✅ 迁移 proxy.health_check.timeout: %v -> timeouts.proxy.health_check\n", timeout)
		}
	}

	// 设置默认健康检查超时
	if _, exists := proxyTimeouts["health_check"]; !exists {
		proxyTimeouts["health_check"] = "3s"
		fmt.Printf("  ✅ 设置默认 timeouts.proxy.health_check: 3s\n")
	}

	// 从global配置中迁移代理冷却时间
	if global, ok := config["global"].(map[string]interface{}); ok {
		if cooldownTime, exists := global["retry_proxy_cooldown_time"]; exists {
			proxyTimeouts["cooldown"] = fmt.Sprintf("%vs", cooldownTime)
			fmt.Printf("  ✅ 迁移 global.retry_proxy_cooldown_time: %v -> timeouts.proxy.cooldown\n", cooldownTime)
		}
	}

	// 设置默认冷却时间
	if _, exists := proxyTimeouts["cooldown"]; !exists {
		proxyTimeouts["cooldown"] = "60s"
		fmt.Printf("  ✅ 设置默认 timeouts.proxy.cooldown: 60s\n")
	}

	return nil
}

// migrateDNSTimeouts 迁移DNS超时配置
func (m *TimeoutMigrator) migrateDNSTimeouts(config map[string]interface{}, timeouts map[string]interface{}) error {
	dnsTimeouts := timeouts["dns"].(map[string]interface{})

	// 从dns_service配置中迁移
	if dnsService, ok := config["dns_service"].(map[string]interface{}); ok {
		if timeout, exists := dnsService["timeout"]; exists {
			dnsTimeouts["query"] = timeout
			fmt.Printf("  ✅ 迁移 dns_service.timeout: %v -> timeouts.dns.query\n", timeout)
		}
	}

	// 设置默认DNS超时
	if _, exists := dnsTimeouts["query"]; !exists {
		dnsTimeouts["query"] = "5s"
		fmt.Printf("  ✅ 设置默认 timeouts.dns.query: 5s\n")
	}

	dnsTimeouts["resolve"] = "5s"
	fmt.Printf("  ✅ 设置默认 timeouts.dns.resolve: 5s\n")

	return nil
}

// migrateCacheTimeouts 迁移缓存超时配置
func (m *TimeoutMigrator) migrateCacheTimeouts(config map[string]interface{}, timeouts map[string]interface{}) error {
	cache, ok := config["cache"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到cache配置节")
		return nil
	}

	cacheTimeouts := timeouts["cache"].(map[string]interface{})

	// 迁移清理间隔
	if cleanupInterval, exists := cache["cleanup_interval"]; exists {
		cacheTimeouts["cleanup"] = cleanupInterval
		fmt.Printf("  ✅ 迁移 cache.cleanup_interval: %v -> timeouts.cache.cleanup\n", cleanupInterval)
	} else {
		cacheTimeouts["cleanup"] = "300s"
		fmt.Printf("  ✅ 设置默认 timeouts.cache.cleanup: 300s\n")
	}

	return nil
}

// migrateMonitoringTimeouts 迁移监控超时配置
func (m *TimeoutMigrator) migrateMonitoringTimeouts(config map[string]interface{}, timeouts map[string]interface{}) error {
	monitoring, ok := config["monitoring"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到monitoring配置节")
		return nil
	}

	monitoringTimeouts := timeouts["monitoring"].(map[string]interface{})

	// 从统计配置中迁移间隔
	if statistics, exists := monitoring["statistics"].(map[string]interface{}); exists {
		if interval, exists := statistics["interval"]; exists {
			monitoringTimeouts["interval"] = interval
			fmt.Printf("  ✅ 迁移 monitoring.statistics.interval: %v -> timeouts.monitoring.interval\n", interval)
		}
	}

	// 设置默认监控间隔
	if _, exists := monitoringTimeouts["interval"]; !exists {
		monitoringTimeouts["interval"] = "5s"
		fmt.Printf("  ✅ 设置默认 timeouts.monitoring.interval: 5s\n")
	}

	monitoringTimeouts["collection"] = "3s"
	fmt.Printf("  ✅ 设置默认 timeouts.monitoring.collection: 3s\n")

	return nil
}

// setDefaultActionTimeouts 设置默认动作超时
func (m *TimeoutMigrator) setDefaultActionTimeouts(timeouts map[string]interface{}) {
	actionTimeouts := timeouts["action"].(map[string]interface{})

	actionTimeouts["default"] = "10s"
	actionTimeouts["request"] = "30s"
	actionTimeouts["bypass"] = "30s"

	fmt.Printf("  ✅ 设置默认动作超时配置\n")
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run timeout_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run timeout_migration.go config.yaml config_timeout_migrated.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewTimeoutMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("超时配置迁移失败: %v", err)
	}

	fmt.Println("\n🎉 超时配置迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - 所有超时相关配置已统一到 timeouts 节")
	fmt.Println("  - server 中的超时配置已迁移到 timeouts.network")
	fmt.Println("  - proxy 中的超时配置已迁移到 timeouts.proxy")
	fmt.Println("  - dns_service 中的超时配置已迁移到 timeouts.dns")
	fmt.Println("  - cache 中的超时配置已迁移到 timeouts.cache")
	fmt.Println("  - monitoring 中的超时配置已迁移到 timeouts.monitoring")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
