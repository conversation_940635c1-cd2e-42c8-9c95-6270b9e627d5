package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// CacheMigrator 缓存配置迁移器
type CacheMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewCacheMigrator 创建缓存配置迁移器
func NewCacheMigrator(inputFile, outputFile string) *CacheMigrator {
	backupFile := fmt.Sprintf("%s.cache_backup.%d", inputFile, time.Now().Unix())
	return &CacheMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行缓存配置迁移
func (m *CacheMigrator) Migrate() error {
	fmt.Printf("🔄 开始缓存配置迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migrateCache(config); err != nil {
		return fmt.Errorf("迁移缓存配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ 缓存配置迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *CacheMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migrateCache 迁移缓存配置
func (m *CacheMigrator) migrateCache(config map[string]interface{}) error {
	fmt.Println("📋 开始缓存配置迁移...")

	cache, ok := config["cache"].(map[string]interface{})
	if !ok {
		fmt.Println("  ⚠️  未找到cache配置节")
		return nil
	}

	// 创建新的层次化缓存配置
	newCache := map[string]interface{}{
		"global":       map[string]interface{}{},
		"modules":      map[string]interface{}{},
		"key_prefixes": map[string]interface{}{},
		"policies":     map[string]interface{}{},
		"storage":      map[string]interface{}{},
	}

	// 迁移全局缓存配置
	if err := m.migrateGlobalCache(cache, newCache); err != nil {
		return err
	}

	// 迁移模块缓存配置
	if err := m.migrateModuleCache(cache, newCache); err != nil {
		return err
	}

	// 迁移键前缀配置
	if err := m.migrateKeyPrefixes(cache, newCache); err != nil {
		return err
	}

	// 迁移存储配置
	if err := m.migrateStorageConfig(cache, newCache); err != nil {
		return err
	}

	// 设置缓存策略
	if err := m.setCachePolicies(newCache); err != nil {
		return err
	}

	// 保留向后兼容字段
	m.preserveBackwardCompatibility(cache, newCache)

	// 替换原缓存配置
	config["cache"] = newCache

	fmt.Println("✅ 缓存配置迁移完成")
	return nil
}

// migrateGlobalCache 迁移全局缓存配置
func (m *CacheMigrator) migrateGlobalCache(oldCache, newCache map[string]interface{}) error {
	global := newCache["global"].(map[string]interface{})

	// 迁移启用状态
	if enabled, exists := oldCache["enabled"]; exists {
		global["enabled"] = enabled
		fmt.Printf("  ✅ 迁移 cache.enabled: %v -> cache.global.enabled\n", enabled)
	} else {
		global["enabled"] = true
		fmt.Printf("  ✅ 设置默认 cache.global.enabled: true\n")
	}

	// 迁移默认TTL
	if ttl, exists := oldCache["ttl"]; exists {
		global["default_ttl"] = ttl
		fmt.Printf("  ✅ 迁移 cache.ttl: %v -> cache.global.default_ttl\n", ttl)
	} else {
		global["default_ttl"] = "3600s"
		fmt.Printf("  ✅ 设置默认 cache.global.default_ttl: 3600s\n")
	}

	// 迁移默认大小
	if size, exists := oldCache["size"]; exists {
		global["default_size"] = size
		fmt.Printf("  ✅ 迁移 cache.size: %v -> cache.global.default_size\n", size)
	} else {
		global["default_size"] = 1000
		fmt.Printf("  ✅ 设置默认 cache.global.default_size: 1000\n")
	}

	// 迁移清理间隔
	if cleanupInterval, exists := oldCache["cleanup_interval"]; exists {
		global["cleanup_interval"] = cleanupInterval
		fmt.Printf("  ✅ 迁移 cache.cleanup_interval: %v -> cache.global.cleanup_interval\n", cleanupInterval)
	} else {
		global["cleanup_interval"] = "300s"
		fmt.Printf("  ✅ 设置默认 cache.global.cleanup_interval: 300s\n")
	}

	// 设置其他全局配置
	global["max_memory_usage"] = "512MB"
	global["eviction_policy"] = "lru"

	fmt.Printf("  ✅ 设置默认全局缓存配置\n")
	return nil
}

// migrateModuleCache 迁移模块缓存配置
func (m *CacheMigrator) migrateModuleCache(oldCache, newCache map[string]interface{}) error {
	modules := newCache["modules"].(map[string]interface{})

	// DNS模块缓存配置
	dnsModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "300s",
		"size":             500,
		"cleanup_interval": "600s",
		"eviction_policy":  "lru",
		"compression":      false,
	}
	modules["dns"] = dnsModule
	fmt.Printf("  ✅ 创建 DNS 模块缓存配置\n")

	// 代理模块缓存配置
	proxyModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "1800s",
		"size":             200,
		"cleanup_interval": "900s",
		"eviction_policy":  "lru",
		"compression":      false,
	}
	modules["proxy"] = proxyModule
	fmt.Printf("  ✅ 创建 Proxy 模块缓存配置\n")

	// 会话模块缓存配置
	sessionModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "7200s",
		"size":             100,
		"cleanup_interval": "3600s",
		"eviction_policy":  "lru",
		"compression":      false,
	}
	modules["session"] = sessionModule
	fmt.Printf("  ✅ 创建 Session 模块缓存配置\n")

	// 正则表达式模块缓存配置
	regexModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "86400s", // 24小时
		"size":             50,
		"cleanup_interval": "7200s",
		"eviction_policy":  "lfu", // 最少使用频率
		"compression":      false,
	}
	modules["regex"] = regexModule
	fmt.Printf("  ✅ 创建 Regex 模块缓存配置\n")

	// 认证模块缓存配置
	authModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "3600s",
		"size":             100,
		"cleanup_interval": "1800s",
		"eviction_policy":  "lru",
		"compression":      false,
	}
	modules["auth"] = authModule
	fmt.Printf("  ✅ 创建 Auth 模块缓存配置\n")

	// 限流模块缓存配置
	rateModule := map[string]interface{}{
		"enabled":          true,
		"ttl":              "300s",
		"size":             1000,
		"cleanup_interval": "600s",
		"eviction_policy":  "fifo", // 先进先出
		"compression":      false,
	}
	modules["rate"] = rateModule
	fmt.Printf("  ✅ 创建 Rate 模块缓存配置\n")

	return nil
}

// migrateKeyPrefixes 迁移键前缀配置
func (m *CacheMigrator) migrateKeyPrefixes(oldCache, newCache map[string]interface{}) error {
	keyPrefixes := newCache["key_prefixes"].(map[string]interface{})

	// 迁移现有键前缀
	if oldPrefixes, exists := oldCache["key_prefixes"].(map[string]interface{}); exists {
		for key, value := range oldPrefixes {
			keyPrefixes[key] = value
			fmt.Printf("  ✅ 迁移键前缀 %s: %v\n", key, value)
		}
	}

	// 设置默认键前缀
	defaultPrefixes := map[string]string{
		"proxy_list":   "proxy:list",
		"proxy_status": "proxy:status:",
		"user_session": "user:session:",
		"rate_limit":   "rate:limit:",
		"dns_cache":    "dns:cache:",
		"regex_cache":  "regex:cache:",
		"auth_cache":   "auth:cache:",
	}

	for key, value := range defaultPrefixes {
		if _, exists := keyPrefixes[key]; !exists {
			keyPrefixes[key] = value
			fmt.Printf("  ✅ 设置默认键前缀 %s: %s\n", key, value)
		}
	}

	return nil
}

// migrateStorageConfig 迁移存储配置
func (m *CacheMigrator) migrateStorageConfig(oldCache, newCache map[string]interface{}) error {
	storage := newCache["storage"].(map[string]interface{})

	// 迁移存储类型
	if cacheType, exists := oldCache["type"]; exists {
		storage["type"] = cacheType
		fmt.Printf("  ✅ 迁移 cache.type: %v -> cache.storage.type\n", cacheType)
	} else {
		storage["type"] = "memory"
		fmt.Printf("  ✅ 设置默认 cache.storage.type: memory\n")
	}

	// 设置内存存储配置
	memoryConfig := map[string]interface{}{
		"max_size":        10000,
		"max_memory":      "256MB",
		"eviction_policy": "lru",
		"shards":          16,
	}
	storage["memory"] = memoryConfig
	fmt.Printf("  ✅ 设置内存存储配置\n")

	// 设置Redis存储配置（示例）
	redisConfig := map[string]interface{}{
		"address":      "localhost:6379",
		"password":     "",
		"database":     0,
		"pool_size":    10,
		"max_retries":  3,
		"dial_timeout": "5s",
	}
	storage["redis"] = redisConfig
	fmt.Printf("  ✅ 设置Redis存储配置\n")

	// 设置文件存储配置（示例）
	fileConfig := map[string]interface{}{
		"directory":     "./cache",
		"max_file_size": "100MB",
		"max_files":     1000,
		"compression":   true,
	}
	storage["file"] = fileConfig
	fmt.Printf("  ✅ 设置文件存储配置\n")

	return nil
}

// setCachePolicies 设置缓存策略
func (m *CacheMigrator) setCachePolicies(newCache map[string]interface{}) error {
	policies := newCache["policies"].(map[string]interface{})

	// 缓存预热策略
	warmupPolicy := map[string]interface{}{
		"enabled":    false,
		"on_startup": false,
		"sources":    []string{},
		"batch_size": 100,
	}
	policies["warmup"] = warmupPolicy

	// 缓存失效策略
	invalidationPolicy := map[string]interface{}{
		"strategy": "ttl",
		"events":   []string{},
		"patterns": []string{},
	}
	policies["invalidation"] = invalidationPolicy

	// 缓存同步策略
	syncPolicy := map[string]interface{}{
		"enabled":  false,
		"mode":     "master",
		"interval": "60s",
	}
	policies["sync"] = syncPolicy

	// 缓存压缩策略
	compressionPolicy := map[string]interface{}{
		"enabled":   false,
		"algorithm": "gzip",
		"level":     6,
		"min_size":  1024,
	}
	policies["compression"] = compressionPolicy

	fmt.Printf("  ✅ 设置缓存策略配置\n")
	return nil
}

// preserveBackwardCompatibility 保留向后兼容字段
func (m *CacheMigrator) preserveBackwardCompatibility(oldCache, newCache map[string]interface{}) {
	// 保留旧字段以确保向后兼容
	if enabled, exists := oldCache["enabled"]; exists {
		newCache["enabled"] = enabled
	}
	if cacheType, exists := oldCache["type"]; exists {
		newCache["type"] = cacheType
	}
	if ttl, exists := oldCache["ttl"]; exists {
		newCache["ttl"] = ttl
	}
	if size, exists := oldCache["size"]; exists {
		newCache["size"] = size
	}
	if cleanupInterval, exists := oldCache["cleanup_interval"]; exists {
		newCache["cleanup_interval"] = cleanupInterval
	}

	fmt.Printf("  ✅ 保留向后兼容字段\n")
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run cache_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run cache_migration.go config.yaml config_cache_migrated.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewCacheMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("缓存配置迁移失败: %v", err)
	}

	fmt.Println("\n🎉 缓存配置迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - 缓存配置已层次化为 global、modules、key_prefixes、policies、storage")
	fmt.Println("  - 创建了6个专用模块缓存配置 (dns, proxy, session, regex, auth, rate)")
	fmt.Println("  - 添加了缓存策略配置 (预热、失效、同步、压缩)")
	fmt.Println("  - 添加了多种存储后端配置 (memory, redis, file)")
	fmt.Println("  - 保留了向后兼容字段")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
