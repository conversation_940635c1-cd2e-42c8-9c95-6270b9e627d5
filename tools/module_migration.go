package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// ModuleMigrator 模块启用统一管理迁移器
type ModuleMigrator struct {
	inputFile  string
	outputFile string
	backupFile string
}

// NewModuleMigrator 创建模块启用统一管理迁移器
func NewModuleMigrator(inputFile, outputFile string) *ModuleMigrator {
	backupFile := fmt.Sprintf("%s.module_backup.%d", inputFile, time.Now().Unix())
	return &ModuleMigrator{
		inputFile:  inputFile,
		outputFile: outputFile,
		backupFile: backupFile,
	}
}

// Migrate 执行模块启用统一管理迁移
func (m *ModuleMigrator) Migrate() error {
	fmt.Printf("🔄 开始模块启用统一管理迁移: %s -> %s\n", m.inputFile, m.outputFile)

	// 1. 备份原配置文件
	if err := m.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原配置失败: %v", err)
	}
	fmt.Printf("✅ 原配置已备份到: %s\n", m.backupFile)

	// 2. 读取原配置
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 3. 解析原配置
	var config map[string]interface{}
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 4. 执行迁移
	if err := m.migrateModules(config); err != nil {
		return fmt.Errorf("迁移模块配置失败: %v", err)
	}

	// 5. 写入新配置
	newData, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化新配置失败: %v", err)
	}

	if err := ioutil.WriteFile(m.outputFile, newData, 0644); err != nil {
		return fmt.Errorf("写入新配置失败: %v", err)
	}

	fmt.Printf("✅ 模块启用统一管理迁移完成: %s\n", m.outputFile)
	return nil
}

// backupOriginalConfig 备份原配置文件
func (m *ModuleMigrator) backupOriginalConfig() error {
	data, err := ioutil.ReadFile(m.inputFile)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(m.backupFile, data, 0644)
}

// migrateModules 迁移模块配置
func (m *ModuleMigrator) migrateModules(config map[string]interface{}) error {
	fmt.Println("📋 开始模块启用统一管理迁移...")

	// 创建统一的模块管理配置
	modules := map[string]interface{}{
		"enabled":      []string{},
		"disabled":     []string{},
		"dependencies": map[string]interface{}{},
		"startup_order": map[string]interface{}{},
		"lifecycle":    map[string]interface{}{},
		"health_check": map[string]interface{}{},
	}

	enabledModules := []string{}
	disabledModules := []string{}

	// 检查各模块的启用状态
	moduleConfigs := map[string]string{
		"proxy":         "proxy",
		"cache":         "cache",
		"logging":       "logging",
		"monitoring":    "monitoring",
		"security":      "security",
		"rate_limiting": "rate_limiting",
		"dns_service":   "dns_service",
	}

	// 迁移各模块的启用状态
	for moduleName, configKey := range moduleConfigs {
		if err := m.migrateModuleEnabled(config, moduleName, configKey, &enabledModules, &disabledModules); err != nil {
			return err
		}
	}

	// 检查特殊模块
	m.checkSpecialModules(config, &enabledModules, &disabledModules)

	// 设置启用和禁用的模块列表
	modules["enabled"] = enabledModules
	modules["disabled"] = disabledModules

	// 设置模块依赖关系
	if err := m.setModuleDependencies(modules); err != nil {
		return err
	}

	// 设置模块启动顺序
	if err := m.setModuleStartupOrder(modules); err != nil {
		return err
	}

	// 设置模块生命周期配置
	if err := m.setModuleLifecycle(modules); err != nil {
		return err
	}

	// 设置模块健康检查配置
	if err := m.setModuleHealthCheck(modules); err != nil {
		return err
	}

	// 将模块配置添加到主配置中
	config["modules"] = modules

	fmt.Println("✅ 模块启用统一管理迁移完成")
	return nil
}

// migrateModuleEnabled 迁移模块启用状态
func (m *ModuleMigrator) migrateModuleEnabled(config map[string]interface{}, moduleName, configKey string, enabled, disabled *[]string) error {
	if moduleConfig, ok := config[configKey].(map[string]interface{}); ok {
		if enabledValue, exists := moduleConfig["enabled"]; exists {
			if isEnabled, ok := enabledValue.(bool); ok {
				if isEnabled {
					*enabled = append(*enabled, moduleName)
					fmt.Printf("  ✅ 模块 %s: enabled=true -> modules.enabled\n", moduleName)
				} else {
					*disabled = append(*disabled, moduleName)
					fmt.Printf("  ✅ 模块 %s: enabled=false -> modules.disabled\n", moduleName)
				}
			}
		} else {
			// 如果没有enabled字段，默认为启用
			*enabled = append(*enabled, moduleName)
			fmt.Printf("  ✅ 模块 %s: 默认启用 -> modules.enabled\n", moduleName)
		}
	} else {
		// 如果模块配置不存在，默认为禁用
		*disabled = append(*disabled, moduleName)
		fmt.Printf("  ✅ 模块 %s: 配置不存在，默认禁用 -> modules.disabled\n", moduleName)
	}
	return nil
}

// checkSpecialModules 检查特殊模块
func (m *ModuleMigrator) checkSpecialModules(config map[string]interface{}, enabled, disabled *[]string) {
	// 检查server模块（总是启用）
	*enabled = append(*enabled, "server")
	fmt.Printf("  ✅ 模块 server: 核心模块，总是启用 -> modules.enabled\n")

	// 检查global模块（总是启用）
	if global, ok := config["global"].(map[string]interface{}); ok {
		if enableValue, exists := global["enable"]; exists {
			if isEnabled, ok := enableValue.(bool); ok && isEnabled {
				fmt.Printf("  ✅ 模块 global: enabled=true (核心模块)\n")
			}
		}
	}

	// 检查plugins模块
	if plugins, ok := config["plugins"].(map[string]interface{}); ok {
		if enabledValue, exists := plugins["enabled"]; exists {
			if isEnabled, ok := enabledValue.(bool); ok {
				if isEnabled {
					*enabled = append(*enabled, "plugins")
					fmt.Printf("  ✅ 模块 plugins: enabled=true -> modules.enabled\n")
				} else {
					*disabled = append(*disabled, "plugins")
					fmt.Printf("  ✅ 模块 plugins: enabled=false -> modules.disabled\n")
				}
			}
		} else {
			*disabled = append(*disabled, "plugins")
			fmt.Printf("  ✅ 模块 plugins: 默认禁用 -> modules.disabled\n")
		}
	} else {
		*disabled = append(*disabled, "plugins")
		fmt.Printf("  ✅ 模块 plugins: 配置不存在，默认禁用 -> modules.disabled\n")
	}
}

// setModuleDependencies 设置模块依赖关系
func (m *ModuleMigrator) setModuleDependencies(modules map[string]interface{}) error {
	dependencies := map[string]interface{}{
		"core": []string{"server", "logging"},
		"dependencies": map[string]interface{}{
			"proxy":         []string{"server", "logging"},
			"cache":         []string{"logging"},
			"monitoring":    []string{"server", "logging"},
			"security":      []string{"server", "logging"},
			"rate_limiting": []string{"server", "logging", "cache"},
			"dns_service":   []string{"logging", "cache"},
			"plugins":       []string{"server", "logging"},
		},
		"optional": map[string]interface{}{
			"proxy":      []string{"cache", "monitoring"},
			"monitoring": []string{"cache"},
			"security":   []string{"cache"},
		},
		"conflicts": map[string]interface{}{
			// 目前没有冲突的模块
		},
	}

	modules["dependencies"] = dependencies
	fmt.Printf("  ✅ 设置模块依赖关系配置\n")
	return nil
}

// setModuleStartupOrder 设置模块启动顺序
func (m *ModuleMigrator) setModuleStartupOrder(modules map[string]interface{}) error {
	startupOrder := map[string]interface{}{
		"phases": []map[string]interface{}{
			{
				"name":     "core",
				"modules":  []string{"logging"},
				"timeout":  "30s",
				"required": true,
			},
			{
				"name":     "infrastructure",
				"modules":  []string{"server", "cache", "dns_service"},
				"timeout":  "60s",
				"required": true,
			},
			{
				"name":     "services",
				"modules":  []string{"proxy", "security", "rate_limiting"},
				"timeout":  "60s",
				"required": false,
			},
			{
				"name":     "monitoring",
				"modules":  []string{"monitoring"},
				"timeout":  "30s",
				"required": false,
			},
			{
				"name":     "extensions",
				"modules":  []string{"plugins"},
				"timeout":  "30s",
				"required": false,
			},
		},
		"phase_delay": "5s",
		"parallel": map[string]interface{}{
			"enabled":     true,
			"max_workers": 3,
			"timeout":     "120s",
		},
	}

	modules["startup_order"] = startupOrder
	fmt.Printf("  ✅ 设置模块启动顺序配置\n")
	return nil
}

// setModuleLifecycle 设置模块生命周期配置
func (m *ModuleMigrator) setModuleLifecycle(modules map[string]interface{}) error {
	lifecycle := map[string]interface{}{
		"startup_timeout":  "300s",
		"shutdown_timeout": "60s",
		"restart_policy": map[string]interface{}{
			"enabled":            true,
			"max_retries":        3,
			"retry_delay":        "10s",
			"backoff_multiplier": 2.0,
			"max_delay":          "300s",
		},
		"graceful_shutdown": map[string]interface{}{
			"enabled":             true,
			"timeout":             "30s",
			"force_after":         "60s",
			"wait_for_connections": true,
		},
	}

	modules["lifecycle"] = lifecycle
	fmt.Printf("  ✅ 设置模块生命周期配置\n")
	return nil
}

// setModuleHealthCheck 设置模块健康检查配置
func (m *ModuleMigrator) setModuleHealthCheck(modules map[string]interface{}) error {
	healthCheck := map[string]interface{}{
		"enabled":  true,
		"interval": "30s",
		"timeout":  "10s",
		"endpoints": map[string]interface{}{
			"server":      "/health",
			"monitoring":  "/metrics",
			"proxy":       "/proxy/health",
			"cache":       "/cache/health",
			"dns_service": "/dns/health",
		},
		"failure_threshold": 3,
		"success_threshold": 2,
		"auto_restart":      false,
	}

	modules["health_check"] = healthCheck
	fmt.Printf("  ✅ 设置模块健康检查配置\n")
	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run module_migration.go <config_file> [output_file]")
		fmt.Println("示例: go run module_migration.go config.yaml config_module_migrated.yaml")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputFile := inputFile
	if len(os.Args) > 2 {
		outputFile = os.Args[2]
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("创建输出目录失败: %v", err)
		}
	}

	// 执行迁移
	migrator := NewModuleMigrator(inputFile, outputFile)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("模块启用统一管理迁移失败: %v", err)
	}

	fmt.Println("\n🎉 模块启用统一管理迁移成功完成!")
	fmt.Printf("📁 原配置备份: %s\n", migrator.backupFile)
	fmt.Printf("📁 新配置文件: %s\n", outputFile)
	fmt.Println("\n📋 迁移说明:")
	fmt.Println("  - 所有模块的enabled配置已统一到 modules 节")
	fmt.Println("  - 创建了模块依赖关系配置")
	fmt.Println("  - 创建了模块启动顺序配置")
	fmt.Println("  - 创建了模块生命周期配置")
	fmt.Println("  - 创建了模块健康检查配置")
	fmt.Println("  - 保留了向后兼容字段")
	fmt.Println("  - 请检查新配置文件并根据需要调整")
}
