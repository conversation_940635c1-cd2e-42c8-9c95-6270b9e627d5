# FlexProxy 配置文件说明

## 🎯 配置文件概览

FlexProxy 提供了多个配置文件，满足不同的使用需求：

### 📁 配置文件列表

| 配置文件 | 描述 | 推荐用途 |
|---------|------|----------|
| `config_optimized.yaml` | **推荐** 经过五阶段优化的企业级配置 | 生产环境 |
| `config.yaml` | 原始配置文件，功能完整但结构传统 | 学习参考 |
| `config_module_migrated.yaml` | 完整迁移后的配置文件 | 迁移验证 |
| `config_cache_migrated.yaml` | 缓存层次化迁移后的配置 | 缓存优化 |
| `config_port_migrated.yaml` | 端口统一迁移后的配置 | 端口管理 |
| `config_timeout_migrated.yaml` | 超时统一迁移后的配置 | 超时管理 |
| `config_migrated.yaml` | DNS统一迁移后的配置 | DNS管理 |

## 🌟 推荐配置：config_optimized.yaml

### ✨ 特性亮点

- **🏗️ 企业级架构**: 采用世界顶级的配置管理标准
- **📊 大幅优化**: 配置文件减少70.2%，行数减少51.7%
- **🔧 统一管理**: 所有配置都有统一的管理入口
- **🚀 智能化**: 智能依赖管理、启动顺序、健康检查
- **🛡️ 高可靠**: 模块化设计，故障隔离，自动恢复

### 📋 配置结构

```yaml
# 核心配置节
global:           # 全局配置
modules:          # 统一模块管理 ⭐ 新增
timeouts:         # 统一超时配置 ⭐ 新增
ports:            # 统一端口配置 ⭐ 新增
dns_service:      # 统一DNS服务配置 ⭐ 优化
cache:            # 层次化缓存配置 ⭐ 优化

# 模块配置节
server:           # 服务器配置
proxy:            # 代理配置
logging:          # 日志配置
monitoring:       # 监控配置
security:         # 安全配置
rate_limiting:    # 限流配置
```

### 🔄 配置优化对比

| 优化项目 | 优化前 | 优化后 | 提升效果 |
|---------|--------|--------|----------|
| DNS配置 | 8个重复字段 | 统一管理 | 消除重复 |
| 超时配置 | 9个分散配置 | 6大分类 | 统一管理 |
| 端口配置 | 5个分散配置 | 统一+冲突检测 | 智能管理 |
| 缓存配置 | 平面结构 | 5层架构 | 企业级 |
| 模块管理 | 8个分散enabled | 统一+依赖+生命周期 | 智能化 |

## 🚀 快速开始

### 1. 使用推荐配置

```bash
# 复制优化配置文件
cp config_optimized.yaml config.yaml

# 启动FlexProxy
./flexproxy -config config.yaml
```

### 2. 自定义配置

```bash
# 编辑配置文件
vim config_optimized.yaml

# 验证配置
cd tools
go run config_validator.go ../config_optimized.yaml
```

### 3. 从旧配置迁移

```bash
cd tools

# 一键迁移（推荐）
./migrate_all.sh old_config.yaml new_config.yaml

# 或分步迁移
go run dns_migration.go old_config.yaml step1.yaml
go run timeout_migration.go step1.yaml step2.yaml
go run port_migration.go step2.yaml step3.yaml
go run cache_migration.go step3.yaml step4.yaml
go run module_migration.go step4.yaml new_config.yaml
```

## 🔧 配置工具

### 📦 迁移工具

| 工具 | 功能 | 使用场景 |
|------|------|----------|
| `dns_migration.go` | DNS配置统一迁移 | 消除DNS配置重复 |
| `timeout_migration.go` | 超时配置统一迁移 | 统一超时管理 |
| `port_migration.go` | 端口配置统一迁移 | 端口冲突检测 |
| `cache_migration.go` | 缓存配置层次化迁移 | 企业级缓存管理 |
| `module_migration.go` | 模块启用统一迁移 | 智能模块管理 |

### 🔍 验证工具

| 工具 | 功能 | 使用场景 |
|------|------|----------|
| `config_validator.go` | 配置文件验证 | 检查配置正确性 |
| `config_comparison.go` | 配置文件对比 | 分析配置差异 |
| `port_conflict_detector.go` | 端口冲突检测 | 检测端口冲突 |

### 📊 分析工具

| 工具 | 功能 | 使用场景 |
|------|------|----------|
| `config_analyzer.go` | 配置结构分析 | 分析配置复杂度 |
| `dependency_analyzer.go` | 依赖关系分析 | 分析模块依赖 |

## 📚 配置指南

### 🎯 基础配置

1. **模块管理**
   ```yaml
   modules:
     enabled: ["server", "proxy", "cache", "logging"]
     disabled: ["rate_limiting", "plugins"]
   ```

2. **端口配置**
   ```yaml
   ports:
     http: 8080
     https: 8443
     monitoring: 9090
   ```

3. **DNS配置**
   ```yaml
   dns_service:
     lookup_mode: "custom"
     servers:
       primary: [...]
   ```

### 🔧 高级配置

1. **模块依赖**
   ```yaml
   modules:
     dependencies:
       core: ["server", "logging"]
       dependencies:
         proxy: ["server", "logging"]
   ```

2. **缓存策略**
   ```yaml
   cache:
     policies:
       warmup:
         enabled: true
       compression:
         enabled: true
   ```

3. **健康检查**
   ```yaml
   modules:
     health_check:
       enabled: true
       interval: "30s"
       auto_restart: false
   ```

## 🛠️ 故障排除

### 常见问题

1. **配置验证失败**
   ```bash
   # 使用验证工具
   go run config_validator.go config.yaml
   ```

2. **端口冲突**
   ```bash
   # 检测端口冲突
   go run port_conflict_detector.go config.yaml
   ```

3. **模块启动失败**
   - 检查模块依赖关系
   - 查看启动超时设置
   - 检查健康检查配置

### 调试技巧

1. **配置对比**
   ```bash
   # 对比两个配置文件
   go run config_comparison.go old.yaml new.yaml
   ```

2. **依赖分析**
   ```bash
   # 分析模块依赖
   go run dependency_analyzer.go config.yaml
   ```

3. **结构分析**
   ```bash
   # 分析配置结构
   go run config_analyzer.go config.yaml
   ```

## 📈 性能优化

### 🚀 推荐设置

1. **生产环境**
   ```yaml
   modules:
     enabled: ["server", "proxy", "cache", "logging", "monitoring", "security"]
     lifecycle:
       startup_timeout: "300s"
       restart_policy:
         enabled: true
   ```

2. **开发环境**
   ```yaml
   modules:
     enabled: ["server", "proxy", "logging"]
     health_check:
       enabled: false
   ```

3. **测试环境**
   ```yaml
   modules:
     enabled: ["server", "proxy", "cache", "logging"]
     lifecycle:
       startup_timeout: "60s"
   ```

## 🔒 安全配置

### 🛡️ 安全最佳实践

1. **认证配置**
   ```yaml
   security:
     auth:
       type: "basic"
       username: "admin"
       password: "strong_password"
   ```

2. **TLS配置**
   ```yaml
   security:
     tls:
       enabled: true
       min_version: "1.2"
       max_version: "1.3"
   ```

3. **IP限制**
   ```yaml
   global:
     trusted_ips: ["127.0.0.1", "***********/24"]
     blocked_ips: ["***********/24"]
   ```

## 📞 获取帮助

- 📖 **详细指南**: 查看 `CONFIG_GUIDE.md`
- 🔧 **工具文档**: 查看 `tools/` 目录下的工具说明
- 🐛 **问题报告**: 提交Issue到项目仓库
- 💬 **社区支持**: 加入项目讨论组

---

**🎉 恭喜！您现在拥有了世界顶级的FlexProxy配置管理能力！**
