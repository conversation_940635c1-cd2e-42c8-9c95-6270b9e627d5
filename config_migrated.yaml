actions:
    ban_domain_handler:
        sequence:
            - domain: '{{.domain}}'
              duration: 7200
              type: ban_domain
    banip_handler:
        sequence:
            - duration: 3600
              type: banip
    banipdomain_handler:
        sequence:
            - duration: 3600
              scope: domain
              type: banipdomain
    block_request_handler:
        sequence:
            - reason: 安全策略阻止
              status_code: 403
              type: block_request
    bypass_proxy_handler:
        sequence:
            - timeout_ms: 10000
              type: bypass_proxy
    cache_handler:
        sequence:
            - cache_scope: domain
              duration: 1800
              max_use_count: 100
              type: cache
    cache_response_handler:
        sequence:
            - cache_key: response_{{.url_hash}}
              duration: 3600
              type: cache_response
    error_recovery_handler:
        sequence:
            - retry_count: 2
              type: retry
            - pool_name: error_proxies
              quality_score: 30
              type: save_to_pool
    keyword_intelligent_handler:
        sequence:
            - keyword_rules:
                - pattern: api_key=*
                  replacement: api_key=REDACTED
                  type: wildcard
                - pattern: '"password":\s*"[^"]*"'
                  replacement: '"password": "***"'
                  type: regex
                - condition: contains(body, 'sensitive')
                  else_action: pass_through
                  then_action: encrypt
                  type: conditional
              preserve_structure: true
              type: modify_request
              validate_format: true
    log_handler:
        sequence:
            - level: info
              message: '事件触发: {{.event_name}} - URL: {{.url}}'
              type: log
    modify_auto_detect_example:
        sequence:
            - body_config:
                content: '{"auto_detected": true, "message": "Content-Type will be automatically detected as JSON"}'
              type: modify_response
    modify_binary_example:
        sequence:
            - body_config:
                content: RmxleFByb3h5IEJpbmFyeSBEYXRhIEV4YW1wbGUh
                content_type: application/octet-stream
                encoding: base64
              headers:
                Content-Disposition: attachment; filename="modified_file.bin"
              type: modify_response
    modify_form_example:
        sequence:
            - body_config:
                content: username=flexproxy_user&password=modified_password&action=login&remember=true
                format: form
              type: modify_request
    modify_html_example:
        sequence:
            - body_config:
                content: |
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head>
                        <meta charset="UTF-8">
                        <title>页面已被FlexProxy修改</title>
                    </head>
                    <body>
                        <h1>内容已修改</h1>
                        <p>此页面内容已被FlexProxy代理服务器修改。</p>
                    </body>
                    </html>
                format: html
              type: modify_response
    modify_json_example:
        sequence:
            - body_config:
                content: '{"api_version": "v2", "modified_by": "FlexProxy", "data": {"user_id": 12345}}'
                format: json
              type: modify_request
    modify_request_advanced:
        sequence:
            - body_config:
                content: |
                    {
                      "advanced_modification": true,
                      "supported_formats": ["JSON", "XML", "HTML", "Text", "Form", "Binary"],
                      "auto_content_type": true,
                      "request_data": {
                        "user_id": 12345,
                        "action": "advanced_update"
                      }
                    }
                content_type: application/json; charset=utf-8
                format: json
              headers:
                X-Modified-By: FlexProxy-Advanced
              type: modify_request
    modify_request_handler:
        sequence:
            - body: '{"modified": true, "proxy": "FlexProxy", "timestamp": "2024-01-01T00:00:00Z"}'
              headers:
                Authorization: Bearer token-12345
                User-Agent: FlexProxy/1.0
                X-Forwarded-By: FlexProxy
              remove_headers:
                - X-Real-IP
                - Accept-Encoding
              type: modify_request
    modify_response_advanced:
        sequence:
            - body_config:
                auto_content_type: true
                content: |
                    {
                      "status": "success",
                      "message": "Advanced response modification by FlexProxy",
                      "features": {
                        "json_support": true,
                        "xml_support": true,
                        "html_support": true,
                        "binary_support": true,
                        "auto_content_type": true,
                        "format_validation": true
                      },
                      "data": {
                        "modification_time": "2024-01-01T00:00:00Z",
                        "proxy_version": "FlexProxy v1.0"
                      }
                    }
                format: json
              headers:
                X-Advanced-Modification: "true"
                X-Supported-Formats: JSON,XML,HTML,Text,Form,Binary
              status_code: 200
              type: modify_response
    modify_response_handler:
        sequence:
            - body: '{"success": true, "message": "Response modified by FlexProxy", "data": {"original": "replaced"}}'
              headers:
                Cache-Control: no-cache, no-store
                Content-Type: application/json
                X-Proxy-Modified: "true"
              remove_headers:
                - Server
                - X-Powered-By
              status_code: 200
              type: modify_response
    modify_text_example:
        sequence:
            - body_config:
                content: |
                    FlexProxy 代理服务器响应
                    ========================

                    原始内容已被替换为此文本内容。
                    修改时间: 2024-01-01 00:00:00
                    代理版本: FlexProxy v1.0
                format: text
              type: modify_response
    modify_xml_example:
        sequence:
            - body_config:
                content: |
                    <?xml version="1.0" encoding="UTF-8"?>
                    <response>
                      <status>success</status>
                      <message>XML response modified by FlexProxy</message>
                      <data>
                        <item id="1">Modified Item</item>
                      </data>
                    </response>
                format: xml
              type: modify_response
    null_response_handler:
        sequence:
            - headers:
                X-Null-Response: "true"
              status_code: 204
              type: null_response
    request_url_handler:
        sequence:
            - body: '{"event": "proxy_action", "data": "{{.request_data}}"}'
              body_type: json
              headers: Content-Type:application/json
              method: POST
              timeout_ms: 5000
              type: request_url
              url: https://api.example.com/webhook
    retry_handler:
        sequence:
            - retry_count: 3
              type: retry
    retry_same_handler:
        sequence:
            - retry_count: 2
              type: retry_same
    save_to_pool_handler:
        sequence:
            - pool_name: quality_proxies
              quality_score: 85
              type: save_to_pool
    script_handler:
        sequence:
            - language: javascript
              script: |
                console.log('处理请求:', opt.url);
                return opt.status >= 400;
              type: script
    security_handler:
        sequence:
            - level: error
              message: "\U0001F6A8 安全威胁检测: {{.url}} from {{.client_ip}}"
              type: log
            - duration: 3600
              type: banip
            - body: '{"error": "Access denied", "code": 403}'
              status_code: 403
              type: null_response
advanced:
    debug:
        dump_requests: false
        dump_responses: false
        enabled: false
        profile_enabled: false
        profile_port: 8081
        verbose_logging: false
    enabled: false
    error_recovery:
        circuit_reset_timeout: 300s
        circuit_timeout: 60s
        failure_threshold: 5
        initial_retry_delay: 1s
        max_retry_attempts: 3
        max_retry_delay: 30s
        retry_multiplier: 2
        success_threshold: 3
    performance:
        batch_size: 100
        flush_interval: 5s
        queue_size: 1000
        worker_pool_size: 10
    tracing:
        enabled: true
        hex_generator_length: 16
        sequence_modulus: 10000
cache:
    cleanup_interval: 300s
    enabled: true
    key_prefixes:
        proxy_list: proxy:list
        proxy_status: 'proxy:status:'
        rate_limit: 'rate:limit:'
        user_session: 'user:session:'
    size: 1000
    ttl: 3600s
    type: memory
config_management:
    backup:
        backup_dir: ./backup/config
        compress: true
        enabled: true
        max_backups: 10
    hot_reload:
        backup_on_reload: true
        enabled: false
        reload_interval: 30s
        rollback_on_error: true
        watch_files:
            - config.yaml
            - proxies.txt
    validation:
        fail_fast: true
        strict_mode: true
        validate_on_startup: true
development:
    enabled: false
    hot_reload: false
    mode: production
    profiling:
        block_profile: false
        cpu_profile: false
        enabled: false
        memory_profile: false
        mutex_profile: false
    testing:
        enabled: false
        mock_responses: false
        test_data_dir: ./testdata
dns_service:
    enabled: true
    lookup_mode: ""
    timeout: 5s
    retries: 3
    cache:
        enabled: true
        ttl: 300s
        cleanup_interval: 600s
        max_size: 1000
    servers: null
    ip_version_priority: ipv4
    reverse_lookup:
        enabled: true
        mode: dns
events:
    - conditions:
        - enable: true
          name: error_status
          status_codes:
            codes:
                - 403
                - 404
                - 500
                - 502
                - 503
            relation: or
      enable: true
      matches:
        - actions:
            - duration: 600
              type: banip
            - retry_count: 2
              type: retry
          conditions:
            - error_status
          enable: true
          name: error_match
      name: status_code_handler
      priority: 10
      process_stage: post_header
      trigger_type: status
    - conditions:
        - body_patterns:
            case_sensitive: false
            patterns:
                - pattern: error|failed|denied
                  type: regex
                  weight: 10
            relation: or
          enable: true
          name: error_content
      enable: true
      matches:
        - actions:
            - level: warn
              message: '检测到错误内容: {{.url}}'
              type: log
          conditions:
            - error_content
          enable: true
          name: content_error_match
      name: body_content_handler
      priority: 15
      process_stage: post_body
      trigger_type: body
    - conditions:
        - enable: true
          max_request_time: 10000
          name: slow_request
      enable: true
      matches:
        - actions:
            - retry_count: 1
              type: retry_same
          conditions:
            - slow_request
          enable: true
          name: timeout_match
      name: max_request_time_handler
      priority: 12
      process_stage: post_body
      trigger_type: max_request_time
    - conditions:
        - connection_timeout: 5000
          enable: true
          name: connection_timeout
      enable: true
      matches:
        - actions:
            - retry_count: 2
              type: retry
          conditions:
            - connection_timeout
          enable: true
          name: conn_timeout_match
      name: connection_timeout_handler
      priority: 20
      process_stage: post_body
      trigger_type: conn_time_out
    - conditions:
        - enable: true
          min_request_time: 100
          name: too_fast_response
      enable: true
      matches:
        - actions:
            - cache_scope: url
              duration: 300
              type: cache
          conditions:
            - too_fast_response
          enable: true
          name: fast_response_match
      name: min_request_time_handler
      priority: 8
      process_stage: post_body
      trigger_type: min_request_time
    - conditions:
        - enable: true
          name: api_endpoints
          url_patterns:
            patterns:
                - pattern: /api/
                  type: string
                  weight: 10
            relation: or
      enable: true
      matches:
        - actions:
            - headers:
                X-API-Request: "true"
              type: modify_request
          conditions:
            - api_endpoints
          enable: true
          name: api_match
      name: url_pattern_handler
      priority: 5
      process_stage: pre
      trigger_type: url
    - conditions:
        - domain_patterns:
            patterns:
                - pattern: .*\.cdn\.
                  type: regex
                  weight: 10
            relation: or
          enable: true
          name: cdn_domains
      enable: true
      matches:
        - actions:
            - type: bypass_proxy
          conditions:
            - cdn_domains
          enable: true
          name: cdn_match
      name: domain_handler
      priority: 6
      process_stage: pre
      trigger_type: domain
    - conditions:
        - enable: true
          name: user_agent_check
          request_header_patterns:
            headers:
                User-Agent:
                    patterns:
                        - pattern: bot|crawler
                          type: regex
                          weight: 10
                    relation: or
      enable: true
      matches:
        - actions:
            - status_code: 429
              type: null_response
          conditions:
            - user_agent_check
          enable: true
          name: bot_match
      name: request_header_handler
      priority: 7
      process_stage: pre
      trigger_type: request_header
    - conditions:
        - enable: true
          name: rate_limit_headers
          response_header_patterns:
            headers:
                X-RateLimit-Remaining:
                    patterns:
                        - pattern: ^[0-5]$
                          type: regex
                          weight: 15
                    relation: or
      enable: true
      matches:
        - actions:
            - cache_scope: global
              duration: 60
              type: cache
          conditions:
            - rate_limit_headers
          enable: true
          name: rate_limit_match
      name: response_header_handler
      priority: 11
      process_stage: post_header
      trigger_type: response_header
    - conditions:
        - enable: true
          name: malicious_payload
          request_body_patterns:
            case_sensitive: false
            patterns:
                - pattern: '<script|javascript:'
                  type: regex
                  weight: 20
            relation: or
      enable: true
      matches:
        - actions:
            - reason: 恶意请求检测
              type: block_request
            - duration: 3600
              type: banip
          conditions:
            - malicious_payload
          enable: true
          name: security_threat_match
      name: request_body_handler
      priority: 3
      process_stage: pre
      trigger_type: request_body
    - conditions:
        - enable: true
          max_request_time: 8000
          name: high_latency
        - enable: true
          name: error_status
          status_codes:
            codes:
                - 500
                - 502
                - 503
            relation: or
      enable: true
      matches:
        - actions:
            - duration: 1800
              type: banip
            - pool_name: problematic_proxies
              type: save_to_pool
          conditions:
            - high_latency
            - error_status
          enable: true
          logic: AND
          name: latency_and_error
      name: combined_conditions_handler
      priority: 25
      process_stage: post_body
      trigger_type: combined
    - conditions:
        - enable: true
          name: custom_condition
          trigger_id: custom_business_logic
      enable: true
      matches:
        - actions:
            - language: javascript
              script: console.log('自定义逻辑处理:', opt.url);
              type: script
          conditions:
            - custom_condition
          enable: true
          name: custom_match
      name: custom_trigger_handler
      priority: 30
      process_stage: post_body
      trigger_type: custom
global:
    banned_domains:
        - domain: malicious-site.com
          duration: 86400
        - domain: spam-domain.net
          duration: 24h
        - domain: blocked-forever.com
          duration: reboot
    blocked_ips:
        - ***********
        - ************
        - *********
    default_process_stage: pre
    enable: true
    excluded_patterns:
        - '*.local'
        - localhost:*
        - 127.0.0.1:*
        - '*.internal.company.com'
    excluded_scope: all
    global_banned_ips:
        - duration: 3600
          ip: ***********00
        - duration: 1h30m
          ip: *********
        - duration: reboot
          ip: ***********
    ip_rotation_mode: smart
    max_proxy_fetch_attempts: 3
    min_proxy_pool_size: 10
    proxy_file: ./proxies.txt
    retry_proxy_cooldown_time: 60
    retry_proxy_global_tracking: true
    retry_proxy_reuse_policy: cooldown
    rule_priority: 50
    trusted_ips:
        - 127.0.0.1
        - ::1
        - ***********
        - ********
logging:
    enabled: true
    file: ./logs/flexproxy.log
    format: json
    level: info
    max_age: 30
    max_backups: 10
    max_size: 100
    time_format: 2006-01-02T15:04:05.000Z07:00
monitoring:
    enabled: true
    labels:
        environment: production
        service: flexproxy
        version: 1.0.0
    metrics:
        cache_hit_rate: gauge
        proxy_status: gauge
        request_count: counter
        request_duration: histogram
    path: /metrics
    port: 9090
    statistics:
        cleanup_interval: 24h
        compression: true
        enabled: true
        flush_interval: 60s
        retention_period: 30d
        storage_path: ./data/stats.db
        storage_type: file
paths:
    backup_dir: ./backup
    cache_dir: ./cache
    config_dir: ./config
    data_dir: ./data
    log_dir: ./logs
    plugin_dir: ./plugins
    temp_dir: ./temp
plugins:
    auto_load: true
    configs:
        example_plugin:
            config_file: example.yaml
            enabled: false
            parameters:
                param1: value1
                param2: 42
    enabled: false
    plugin_paths:
        - ./plugins
protocols:
    dns:
        doh: false
        https: false
        tcp: false
        tls: false
        udp: true
    http:
        compression: true
        enabled: true
        keep_alive: true
        version: "1.1"
    https:
        compression: true
        enabled: true
        keep_alive: true
        verify_ssl: true
        version: "1.1"
    socks4:
        enabled: false
    socks5:
        auth_required: false
        enabled: false
proxy:
    enabled: true
    failover:
        enabled: true
        failure_window: 5m
        max_failures: 5
        recovery_time: 10m
    health_check:
        enabled: true
        interval: 30s
        max_consecutive_failures: 3
        max_consecutive_successes: 2
        path: /health
        timeout: 10s
    load_balancer: round_robin
    max_retries: 3
    max_retry_interval: 30s
    pool_size: 100
    quality_score:
        default: 0.5
        max_failure_rate: 0.3
        response_time_baseline: 1000
        response_time_weight: 0.4
        smoothing_factor: 0.1
        success_rate_weight: 0.6
        top_proxy_ratio: 0.2
    retry_interval: 1s
    rotation_interval: 300
    strategy: random
rate_limiting:
    algorithm: token_bucket
    burst: 200
    cleanup_period: 5m
    enabled: false
    rate: 100
    window: 1m
security:
    auth:
        token_expiry: 24h
        type: none
    enabled: true
    encryption:
        algorithm: aes256
        key_length: 32
    tls:
        auto_cert: false
        cert_auto_install: false
        cert_domains: []
        cert_file: ""
        enabled: false
        key_file: ""
        max_version: "1.3"
        min_version: "1.2"
server:
    buffer_size: 4096
    compression:
        algorithms:
            - gzip
            - deflate
        enabled: true
        level: 6
        min_size: 1024
    connect_timeout: 10s
    debounce_delay: 100ms
    host: 0.0.0.0
    http2:
        enabled: true
        initial_window_size: 65536
        max_concurrent_streams: 250
        max_frame_size: 1048576
    https_port: 8443
    idle_timeout: 120s
    max_conns_per_host: 50
    max_header_bytes: 1048576
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    port: 8080
    profiling:
        enabled: false
        port: 6060
    read_timeout: 30s
    socks_port: 1080
    write_timeout: 30s
system:
    arch_detection: true
    limits:
        max_cpu_percent: 80
        max_file_descriptors: 10000
        max_goroutines: 10000
        max_memory: 1GB
    os_detection: true
    signal_handling:
        graceful_shutdown: true
        shutdown_timeout: 30s
        signals:
            - SIGTERM
            - SIGINT
            - SIGQUIT
